plugins {
    id 'com.android.library'
    id 'org.jetbrains.kotlin.android'
    id 'kotlin-kapt'
}

android {
    namespace 'com.ybmmarket20.xyyreport'
    compileSdk 33

    defaultConfig {
        minSdk 21

    }

    buildTypes {
        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            buildConfigField "String", "qt_app_key", '"xhlwuva1iqvzexifae34f5gw"'
        }

        debug {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            buildConfigField "String", "qt_app_key", '"mn3vwmgc6uvxm0wnmz34bz19"'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = '1.8'
        freeCompilerArgs += ['-Xskip-metadata-version-check']
    }

    buildFeatures {
        buildConfig = true
    }
}

dependencies {

    implementation 'androidx.core:core-ktx:1.10.1'
    implementation platform('org.jetbrains.kotlin:kotlin-bom:1.8.0')
    implementation deps.gson
    //qt埋点
    api deps.qtPxCommon
    implementation 'androidx.lifecycle:lifecycle-extensions:2.2.0'
    kapt 'com.xyy.ybm100:xyyReportCompiler:1.0.6'
    implementation 'com.xyy.ybm100:xyyReport:1.0.6'
    implementation project(path: ':common')
}