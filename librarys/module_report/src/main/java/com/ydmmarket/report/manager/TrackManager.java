package com.ydmmarket.report.manager;

import android.content.Context;
import com.analysys.AnalysysAgent;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

public class TrackManager {
    public static String FIELD_SEARCH_SORT_STRATEGY_ID = "search_sort_strategy_id"; //搜索的a/b策略id
    public static String FIELD_PLATFORM = "$platform"; //平台
    public static String FIELD_UP_DATE = "up_date"; //上报日期
    public static String FIELD_ACCOUNT_ID = "account_id"; //账号ID
    public static String FIELD_MERCHANT_ID = "merchant_id"; //所属公司/店铺/医院ID
    public static String FIELD_MERCHANT_NAME = "merchant_name"; //所属公司/店铺/医院名称
    public static String FIELD_PROJECT_NAME = "project_name"; //项目名称
    public static String FIELD_PROVINCE = "province"; //省份
    public static String FIELD_BUSINESSTYPE = "businessType"; //店铺类型
    public static String FIELD_BUSINESSTYPENAME = "businessTypeName"; //店铺类型名称
    public static String MODULE_PRODUCT_LIST = "商品列表";
    public static String FIELD_SESSION_ID = "$session_id";

    /**
     * 事件埋点
     * @param mContext
     * @param eventName
     * @param contentMap
     */
    public static void eventTrack(Context mContext, String eventName, HashMap<String, Object> contentMap) {
        AnalysysAgent.track(mContext, eventName, contentMap);
    }

    /**
     * pageView事件
     * @param mContext
     * @param pageName
     * @param contentMap
     */
    public static void pageViewTrack(Context mContext, String pageName, HashMap<String, Object> contentMap) {
        AnalysysAgent.pageView(mContext, pageName, contentMap);
    }

    /**
     * 设置通用属性
     * @param mContext
     * @param contentMap
     */
    public static void setSuperProperties(Context mContext, HashMap<String, Object> contentMap) {
        AnalysysAgent.registerSuperProperties(mContext, contentMap);
    }

    /**
     * 获取单个通用属性
     * @param mContext
     * @param key
     */
    public static void getSuperProperty(Context mContext, String key) {
        AnalysysAgent.getSuperProperty(mContext, key);
    }

    /**
     * 注册设置通用属性
     * @param mContext
     * @param key
     * @param value
     */
    public static void setSuperProperty(Context mContext, String key, Object value) {
        AnalysysAgent.registerSuperProperty(mContext, key, value);
    }

    /**
     * 同步获取预制属性
     * @param mContext
     */
    public static Map<String,Object> getPresetProperties(Context mContext){
        return AnalysysAgent.getPresetProperties(mContext);
    }

    /**
     * 取消注册通用属性
     * @param mContext
     * @param superPropertyName
     */
    public static void unRegisterSuperProperty(Context mContext, String superPropertyName) {
        AnalysysAgent.unRegisterSuperProperty(mContext, superPropertyName);
    }

    /**
     * 立即上传埋点数据
     * @param mContext
     */
    public static void flush(Context mContext) {
        AnalysysAgent.flush(mContext);
    }

    /**
     * 匿名ID与用户关联
     * @param mContext
     * @param aliasId
     */
    public static void alias(Context mContext, String aliasId) {
        AnalysysAgent.alias(mContext, aliasId);
    }

    /**
     * 获取用户的匿名ID
     * @param mContext
     */
    public static String getDistinctId(Context mContext) {
        return timeoutGetDistinctId(mContext);
    }

    /**
     * 请求体添加distinctid，偶现存在阻塞情况，超时处理
     */
    private static String timeoutGetDistinctId(Context mContext){
//        Callable<String> task = ()-> AnalysysAgent.getDistinctId(mContext);
//        Future<String> future = TrackThreadPoolManager.INSTANCE.submit(task);
//        String distinctId = "";
//        try {
//            distinctId = future.get(100, TimeUnit.MILLISECONDS);
//        } catch (TimeoutException e) {
//            System.out.println("Timeout occurred while getting DISTINCTID.");
//        } catch (Exception e){
//            e.printStackTrace();
//        }
//        return distinctId;
        return "";
    }

    public static String getSessionId(Context mContext){
        if (getPresetProperties(mContext).get(FIELD_SESSION_ID) != null){
            return (String) getPresetProperties(mContext).get(FIELD_SESSION_ID);
        }else return  "";
    }
}
