package com.ybm.app.utils;


import android.content.Context;
import android.content.DialogInterface;
import android.view.View;


import com.ybm.app.common.BaseDialog;

public class PermissionDialogUtil {

    public static void showPermissionInfoDialog(Context context, String content, final Callback callback) {
        final BaseDialog baseDialog = new BaseDialog(context);
        baseDialog.setOnCancelListener(new DialogInterface.OnCancelListener() {
            @Override
            public void onCancel(DialogInterface dialog) {
                if (callback != null) {
                    callback.callback();
                }
            }
        });
        baseDialog.setTitle("权限申请")
                .setMsg(content)
                .setConfirmButton("确认", new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        baseDialog.dismiss();
                        if (callback != null) {
                            callback.callback();
                        }
                    }
                }).show();
    }

    public interface Callback {
        void callback();
    }

}
