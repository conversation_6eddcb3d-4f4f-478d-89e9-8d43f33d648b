package com.ybm.app.common.WindowToast;

import android.app.ActivityManager;
import android.content.Context;
import android.os.Handler;
import android.text.Html;
import android.text.TextUtils;
import android.util.DisplayMetrics;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;
import android.widget.TextView;
import android.widget.Toast;
import com.apkfuns.logutils.LogUtils;
import com.ybm.app.R;
import com.ybm.app.common.BaseYBMApp;
import com.ybm.app.utils.BugUtil;
import com.ybm.app.utils.IntentUtil;
import com.ybm.app.utils.UiUtils;

import java.lang.reflect.Field;
import java.lang.reflect.Method;

/**
 * 打开应用的toast
 */

public class ToastTips {

    public static final int LENGTH_ALWAYS = 0;
    public static final int LENGTH_SHORT = 4;
    public static final int LENGTH_LONG = 6;

    public int animations = -1;

    public Context mContext;

    public LayoutInflater inflater;

    public int toastWidth;

    public int toastHeight;

    public Toast toast;

    public View dragView;

    public boolean isShow;

    public Handler handler = new Handler();

    public int mDuration = LENGTH_ALWAYS;

    public DisplayMetrics metrics;

    private Object mTN;
    private Method show;
    private Method hide;
    private View.OnClickListener listener;
    private View.OnLongClickListener longlistener;
    public WindowManager mWM;
    public WindowManager.LayoutParams params;
    protected TextView textView;

    public ToastTips(Context context) {
        mContext = context;
        metrics = UiUtils.getMetrics(BaseYBMApp.getAppContext());
        inflater = (LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        toast = new Toast(context);
        dragView = initView(inflater);
        getToastSize();
    }

    private void getToastSize() {
        int[] size = UiUtils.getSize(dragView);
        toastWidth = size[0];
        toastHeight = size[1];
    }

    public void show() {
        show(LENGTH_ALWAYS);
    }

    /**
     * @param autoDuration 单位秒
     */
    public void show(int autoDuration){
        if (isShow) return;
        toast.setView(dragView);
        initTN();
        try {
            show.invoke(mTN);
        } catch (Exception e) {
            e.printStackTrace();
        }
        isShow = true;
        mDuration =autoDuration;
        if (mDuration > LENGTH_ALWAYS) {
            handler.removeCallbacks(hideRunnable);
            handler.postDelayed(hideRunnable, mDuration * 1000);
        }
    }

    public void hide() {
        if (!isShow) return;
        try {
            hide.invoke(mTN);
        } catch (Exception e) {
            e.printStackTrace();
        }
        isShow = false;
    }


    private void initTN() {
        try {
            Field tnField = toast.getClass().getDeclaredField("mTN");
            tnField.setAccessible(true);
            mTN = tnField.get(toast);
            show = mTN.getClass().getMethod("show");
            hide = mTN.getClass().getMethod("hide");
            Field tnParamsField = mTN.getClass().getDeclaredField("mParams");
            tnParamsField.setAccessible(true);
            params = (WindowManager.LayoutParams) tnParamsField.get(mTN);
            params.flags = WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL
                    | WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE;
            params.windowAnimations = R.style.CleanToastAnimation;
            Field tnNextViewField = mTN.getClass().getDeclaredField("mNextView");
            tnNextViewField.setAccessible(true);
            tnNextViewField.set(mTN, toast.getView());
            mWM = (WindowManager) mContext.getApplicationContext().getSystemService(Context.WINDOW_SERVICE);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if(toast.getXOffset() >0 && toast.getYOffset() >0){

        }else {
            setGravity(Gravity.RIGHT | Gravity.TOP, 0, metrics.heightPixels * 1 / 3-toastHeight/2);
        }
    }

    public void setGravity(int gravity, int xOffset, int yOffset) {
//        LogUtils.d("xOffset: "+xOffset +"  yOffset"+yOffset);
        toast.setGravity(gravity, xOffset, yOffset);
    }

    private Runnable hideRunnable = new Runnable() {
        @Override
        public void run() {
            hide();
        }
    };

    public void setClickListener(View.OnClickListener listener){
        this.listener = listener;
    }

    public void setLonglistener(View.OnLongClickListener longlistener){
        this.longlistener = longlistener;
    }

    public void setText(String str){
        if(textView !=null && !TextUtils.isEmpty(str)){
            textView.setText(Html.fromHtml(str));
        }
    }

    private View initView(LayoutInflater inflater) {
        View view = inflater.inflate(R.layout.window_toast,null);
        textView = (TextView) view.findViewById(R.id.tv_ok);
        textView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {//打开App
                if(listener !=null){
                    listener.onClick(v);
                    return;
                }
                hide();
               showMe();
            }
        });
        textView.setOnLongClickListener(new View.OnLongClickListener() {
            @Override
            public boolean onLongClick(View v) {
                if(longlistener !=null){
                    return longlistener.onLongClick(v);
                }
                hide();
                showApp();
                return true;
            }
        });
        return view;

    }


    public static void showMe(){
        try {
            ActivityManager am = (ActivityManager) BaseYBMApp.getAppContext().getSystemService(Context.ACTIVITY_SERVICE);
            if(BaseYBMApp.getApp().getCurrActivity() == null){
                showApp();
            }else {
                am.moveTaskToFront(BaseYBMApp.getApp().getCurrActivity().getTaskId(), 0);
            }
        }catch (Throwable e){
            showApp();
            e.printStackTrace();
            BugUtil.sendBug(e);
        }
    }

    public static void showApp(){
        IntentUtil.openPackage(BaseYBMApp.getAppContext(),BaseYBMApp.getAppContext().getPackageName());
    }

}
