apply plugin: 'com.android.library'

android {
    compileSdkVersion rootProject.ext.android.compileSdkVersion
    buildToolsVersion rootProject.ext.android.buildToolsVersion

    defaultConfig {
        minSdkVersion rootProject.ext.android.minSdkVersion
        targetSdkVersion rootProject.ext.android.targetSdkVersion
        versionCode 1
        versionName "1.0"
        consumerProguardFiles 'consumer-proguard-rules.pro'
    }
    lintOptions {
        abortOnError Boolean.valueOf(modulesLintAbortOnError)
        ignoreWarnings Boolean.valueOf(modulesLintIgnoreWarnings)
        if (Boolean.valueOf(modulesLintBaseLineEnable)) {
            baseline file("lint-baseline.xml")
        }
        lintConfig file("$rootDir/lint.xml")
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

dependencies {
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    implementation deps.appcompatV7
    implementation deps.supportV4
    implementation deps.constraintLayout
    implementation deps.recyclerview

    //EventBus
    api deps.eventbus
    implementation 'com.tencent.tdos-diagnose:diagnose:0.4.11'
    implementation 'com.tencent.tdos-diagnose:logger:0.4.11'
}
