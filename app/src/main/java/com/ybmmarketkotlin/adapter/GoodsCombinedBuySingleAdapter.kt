package com.ybmmarketkotlin.adapter

import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.adapter.YBMBaseListAdapter
import com.ybmmarket20.bean.GroupPurchaseInfo
import com.ybmmarket20.bean.GroupPurchaseInfoCopy
import com.ybmmarketkotlin.views.combinedbuy.BannerCombinedBuyView
import com.ybmmarketkotlin.views.combinedbuy.CombinedBuyListener


/**
 * <AUTHOR>
 * @desc    大搜组合购
 * @date    2025/5/6
 */
class GoodsCombinedBuySingleAdapter(purchaseInfos: MutableList<GroupPurchaseInfoCopy>?) :
    YBMBaseListAdapter<GroupPurchaseInfoCopy>(R.layout.adapter_combinedbuy_single, purchaseInfos) {
    var mListener: CombinedBuyListener? = null
    var bannerPos:Int = -1   // banner item位置记录
    var loadMoreFlag:Boolean = false
        set(value) {
            field = value
            if(!field){
                bannerPos = -1
            }
        }

    public override fun bindItemView(baseViewHolder: YBMBaseHolder, purchaseInfo: GroupPurchaseInfoCopy) {
        val banner = baseViewHolder.itemView.findViewById<BannerCombinedBuyView>(R.id.banner)
        // 首次展示banner，不是viewHolder复用
        if(!loadMoreFlag && bannerPos == -1){
            bannerPos = 0
            banner.setData(purchaseInfo,mListener,0)
            banner.requestDetail(purchaseInfo, bannerPos,true)
        }else{
            banner.setData(purchaseInfo,mListener,bannerPos)
        }
    }

    public override fun bindItemView(baseViewHolder: YBMBaseHolder, t: GroupPurchaseInfoCopy, payloads: List<Any?>) {
        payloads.forEach {
            it ?: return@forEach
            val banner = baseViewHolder.itemView.findViewById<BannerCombinedBuyView>(R.id.banner)
            banner.adapter?.updateData(it as GroupPurchaseInfo)
        }
    }
}