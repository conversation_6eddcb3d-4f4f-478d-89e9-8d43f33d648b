package com.ybmmarketkotlin.views.combinedbuy

import android.content.Context
import android.graphics.Color
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.RecyclerView
import com.xyy.canary.utils.DensityUtil
import com.ybmmarket20.R
import com.ybmmarket20.bean.GroupPurchaseInfo
import com.ybmmarket20.bean.RowsBeanCombinedExt
import com.ybmmarket20.utils.SpanUtils
import com.ybmmarket20.utils.UiUtils
import com.ybmmarketkotlin.adapter.GoodsCombinedBuyItemAdapter

/**
 * <AUTHOR>
 * @desc    组合购区域
 * @date    2025/5/6
 */
class CombinedBuySingleLayout @JvmOverloads constructor(context: Context, attrs: AttributeSet? = null, def: Int = 0) :
    ConstraintLayout(context, attrs, def) {
    private val rlvGoods by lazy { findViewById<RecyclerView>(R.id.rlvGoods) }
    private val tvSettle by lazy { findViewById<TextView>(R.id.tvSettle) }
    private val tvTotalPrice by lazy { findViewById<TextView>(R.id.tvTotalPrice) }
    private val tvDiscount by lazy { findViewById<TextView>(R.id.tvDiscount) }
    private val tvRefresh by lazy { findViewById<TextView>(R.id.tvRefresh) }
    private val tvTitle by lazy { findViewById<TextView>(R.id.tvTitle) }
    var mListener: CombinedBuyListener? = null
        set(value) {
            field = value
            adapter.mListener = object : CombinedBuyListener {
                override fun changeNum(bean: RowsBeanCombinedExt, subPos: Int, addFlag: Boolean, preNum: Int) {
                    field?.changeNum(bean, curSubPos, addFlag, preNum)
                }

                override fun changeNumClick(bean: RowsBeanCombinedExt, curSubPosition: Int, addFlag: Boolean, preNum: Int) {
                    field?.changeNumClick(bean, curSubPos, addFlag, preNum)
                }

                override fun jumpToGoodsDetail(bean: RowsBeanCombinedExt) {
                    field?.jumpToGoodsDetail(bean)
                }
            }
        }

    var curSubPos = 0
    val adapter by lazy {
        GoodsCombinedBuyItemAdapter(null)
    }

    private var purchaseInfo: GroupPurchaseInfo? = null

    init {
        LayoutInflater.from(context).inflate(R.layout.layout_combined_buy_single, this)
        initView()
    }

    private fun initView() {
        rlvGoods.adapter = adapter

        tvRefresh.setOnClickListener {
            if (purchaseInfo?.subProducts?.isNotEmpty() == false) {
                return@setOnClickListener
            }
            val preCurPos = curSubPos
            if (curSubPos < (purchaseInfo!!.subProducts.size - 1)) {
                curSubPos++
            } else {
                curSubPos = 0
            }
            mListener?.refresh(purchaseInfo!!.subProducts[preCurPos], preCurPos, purchaseInfo!!.subProducts[curSubPos], curSubPos)
        }
        tvSettle.setOnClickListener {
            // 下单
            mListener?.preSettle(curSubPos)
        }
    }

    /**
     * 首次设置商品或变更商品
     */
    fun setNewData(bean: GroupPurchaseInfo) {
        purchaseInfo = bean
        tvTitle.setText(purchaseInfo?.title)
        purchaseInfo!!.combinedList = mutableListOf(
            bean.mainProduct,
            bean.subProducts[curSubPos]
        )
        // 主品因为各种原因未选中时，直接隐藏换一个
        if(bean.mainProduct.selectStatus == 1){
            tvRefresh.visibility = View.VISIBLE
        }else{
            tvRefresh.visibility = View.GONE
        }
        adapter.setNewData(purchaseInfo!!.combinedList)
        updateTotalData()
    }

    private fun updateTotalData() {
        if (purchaseInfo!!.combinedDiscountSub == null || purchaseInfo!!.combinedDiscountSub == 0.0) {
            tvDiscount.visibility = View.GONE
        } else {
            tvDiscount.visibility = View.VISIBLE
            tvDiscount.text = "组合优惠：￥${UiUtils.transform(purchaseInfo!!.combinedDiscountSub!!)}"
        }
        if(purchaseInfo!!.realPay.isNullOrEmpty()){
            tvSettle.alpha = 0.5f
            tvSettle.isEnabled = false
        }else{
            tvSettle.alpha = 1f
            tvSettle.isEnabled = true
        }
        val priceSplit = UiUtils.transform(purchaseInfo!!.realPay ?: "0.00").split(".")
        tvTotalPrice.text = SpanUtils().append("到手价:")
            .setForegroundColor(Color.parseColor("#666666"))
            .append("￥").setFontSize(DensityUtil.dip2px(context, 12f))
            .setBold()
            .append(priceSplit[0])
            .setBold()
            .setFontSize(DensityUtil.dip2px(context, 18f))
            .apply {
                if (priceSplit.size > 1) {
                    this.append(".${priceSplit[1]}")
                    this.setBold()
                    this.setFontSize(DensityUtil.dip2px(context, 14f))
                }
            }
            .create()
    }
}