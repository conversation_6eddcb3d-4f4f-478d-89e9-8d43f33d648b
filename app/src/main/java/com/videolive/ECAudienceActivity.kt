package com.videolive

import android.annotation.SuppressLint
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Point
import android.text.TextUtils
import android.view.Gravity
import android.view.View
import android.view.WindowManager
import com.apkfuns.logutils.LogUtils
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.shuyu.gsyvideoplayer.GSYVideoManager
import com.shuyu.gsyvideoplayer.model.VideoOptionModel
import com.shuyu.gsyvideoplayer.utils.GSYVideoType
import com.videolive.im.IVideoLiveMessageReceiver
import com.ybm.app.bean.NetError
import com.ybm.app.common.BaseYBMApp
import com.ybm.app.common.ImageLoader.ImageHelper
import com.ybm.app.common.SmartExecutorManager
import com.ybmmarket20.R
import com.ybmmarket20.adapter.VideoLiveGroupTextMsgListAdapter
import com.ybmmarket20.bean.*
import com.ybmmarket20.bean.im.*
import com.ybmmarket20.common.AlertDialogEx
import com.ybmmarket20.common.BaseResponse
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.common.util.Abase
import com.ybmmarket20.common.util.ToastUtils
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.constant.ECLiveConstants
import com.ybmmarket20.network.HttpManager
import com.ybmmarket20.repertory.getSId
import com.ybmmarket20.utils.ImageUtil
import com.ybmmarket20.utils.ShareUtil
import com.ybmmarket20.utils.SpUtil
import com.ybmmarket20.utils.YbmCommand
import com.ybmmarket20.utils.analysis.XyyIoUtil
import com.ybmmarket20.utils.analysis.flowDataPageCommoditySearch
import com.ybmmarket20.utils.analysis.updateFlowData
import com.ybmmarket20.utils.im.core.data.MESSAGE_PRIORITY_HIGH
import com.ybmmarket20.utils.im.core.data.MESSAGE_PRIORITY_NORMAL
import com.ybmmarket20.utils.im.core.data.MemberInfo
import com.ybmmarket20.view.ShowBottomTVLiveCouponListDialog
import com.ybmmarket20.view.ShowBottomTVLiveGoodsListDialog
import com.ybmmarket20.view.VideoLiveCouponPopWindow
import com.ybmmarket20.view.VideoLiveGoodsPopWindow
import com.ybmmarket20.widget.InputTextMsgDialog
import kotlinx.android.synthetic.main.activity_ec_audience.*
import kotlinx.android.synthetic.main.layout_live_pusher_info.*
import org.json.JSONObject
import tv.danmaku.ijk.media.player.IjkMediaPlayer
import java.util.*


/**
 * <AUTHOR> Brin
 * @date : 2020/6/4 - 17:52
 * @Description :
 * @version
 */

class ECAudienceActivity : IMActivity() {

    private var liveRoomName: String? = null
    private var mGroupId: String? = null
    private var mCurrentAudienceCount: Int = 0
    private var mUserId: String? = null
    private var mUserSig: String? = null
    private var mAvatar: String? = null
    private var mPlayUrl: String? = null
    private var ecLiveId: String? = null
    private var liveStatus: Int = 1
    private var showProductCount: Int = 0  // 商品数量


    //region 直播间分享信息
    private var shareUrl: String? = null
    private var shareImageUrl: String? = null
    private var shareTitle: String? = null
    private var shareContent: String? = null
    //endregion

    val mInputTextMsgDialog: InputTextMsgDialog by lazy { InputTextMsgDialog(this, R.style.InputDialog) }          // 消息输入框
    val goodsListDialog: ShowBottomTVLiveGoodsListDialog by lazy {
        ShowBottomTVLiveGoodsListDialog(ecLiveId)
    }  // 商品列表展示dialog
    val couponListDialog: ShowBottomTVLiveCouponListDialog by lazy {
        ShowBottomTVLiveCouponListDialog(ecLiveId)
    }  // 优惠券列表展示dialog
    private var mLikeFrequeControl: TCFrequeControl? = null                 // 点赞频率控制器
    private var videoRestartTime: Long = 0                                  // video播放失败重试次数
    private var adapter: VideoLiveGroupTextMsgListAdapter? = null
    private var messageList = ArrayList<VideoLiveChatBean>()

    private val couponPopWindow: VideoLiveCouponPopWindow by lazy {
        VideoLiveCouponPopWindow()
    }
    private val discountGoodsPopUpWindow: VideoLiveGoodsPopWindow by lazy {
        VideoLiveGoodsPopWindow()
    }

    // 分享popwindow
    private val mSharePopWindow: LiveSharePopWindow by lazy {
        LiveSharePopWindow(object : LiveSharePopWindow.DialogItemClick {
            override fun click(platform: String) {
                if ("wx" == platform) {
                    SmartExecutorManager.getInstance().execute {
                        try {
                            val bitmap: Bitmap = ImageHelper.with(this@ECAudienceActivity).load(shareImageUrl).asBitmap().placeholder(R.color.white).dontAnimate().dontTransform().diskCacheStrategy(DiskCacheStrategy.SOURCE).into(100, 100).get()
                            ShareUtil.shareWXPage(0, shareTitle, shareUrl, shareContent, bitmap)
                        } catch (e: Exception) {
                            e.printStackTrace()
                            val bmp = BitmapFactory.decodeResource(Abase.getResources(), R.drawable.logo)
                            ShareUtil.shareWXPage(0, shareTitle, shareUrl, shareContent, bmp)
                        }
                    }
                } else if ("wxpyq" == platform) {
                    SmartExecutorManager.getInstance().execute {
                        try {
                            val bitmap: Bitmap = ImageHelper.with(this@ECAudienceActivity).load(shareImageUrl).asBitmap().placeholder(R.color.white).dontAnimate().dontTransform().diskCacheStrategy(DiskCacheStrategy.SOURCE).into(100, 100).get()
                            SmartExecutorManager.getInstance().executeUI {
                                ShareUtil.shareWXPage(1, shareTitle, shareUrl, shareContent, bitmap)
                            }
                        } catch (e: Exception) {
                            e.printStackTrace()
                            val bmp = BitmapFactory.decodeResource(Abase.getResources(), R.drawable.logo)
                            ShareUtil.shareWXPage(1, shareTitle, shareUrl, shareContent, bmp)
                        }
                    }
                } else if ("linkurl" == platform) {
                    YbmCommand.setClipboardMsg(shareUrl)
                    ToastUtils.showShort("复制成功")
                }
            }
        })
    }

    override fun getContentViewId(): Int {
        return R.layout.activity_ec_audience
    }

    override fun initData() {
        liveStatus = intent.getIntExtra(ECLiveConstants.LIVE_STATUS, 1)
        liveRoomName = intent.getStringExtra(ECLiveConstants.LIVE_ROOM_NAME)
        mCurrentAudienceCount = intent.getIntExtra(ECLiveConstants.MEMBER_COUNT, 0)
        mGroupId = intent.getStringExtra(ECLiveConstants.GROUP_ID)
        mUserId = intent.getStringExtra(ECLiveConstants.IM_USER_ID)
        mUserSig = intent.getStringExtra(ECLiveConstants.IM_USER_SIG)
        mAvatar = intent.getStringExtra(ECLiveConstants.AVATAR)
        mPlayUrl = intent.getStringExtra(ECLiveConstants.PLAY_URL)
        ecLiveId = intent.getStringExtra(ECLiveConstants.EC_LIVE_ID)

        initView()
        GSYVideoType.setShowType(GSYVideoType.SCREEN_TYPE_FULL)

        // 重试5次
        val videoOptionModel = VideoOptionModel(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "reconnect", 5)
        var list: MutableList<VideoOptionModel> = mutableListOf()
        list.add(videoOptionModel)

        GSYVideoManager.instance().optionModelList = list

        video_player?.setVideoAllCallBack(object : AbstractVideoAllCallBack() {
            override fun onPrepared(url: String?, vararg objects: Any?) {
                LogUtils.e("im 直播加载成功")
                fl_error?.visibility = View.GONE
            }

            override fun onPlayError(url: String?, vararg objects: Any?) {
                LogUtils.e("im 直播播放异常 ")
                showVideoLiveLoading()
                if (!reTryPlayVideo()) {
                    LogUtils.e("im 自动重试多次失败 退出直播间")
                    showVideoReload()
                }
            }

            override fun onAutoComplete(url: String?, vararg objects: Any?) {
            }
        })

        if (!TextUtils.isEmpty(mPlayUrl)) {
            startVideoPlay()
        } else {
            ToastUtils.showLong("播放地址有误，请重试")
        }

        if (TextUtils.isEmpty(ecLiveId) || TextUtils.isEmpty(mGroupId)) {
            ToastUtils.showLong("IM信息有误")
        } else {
            showProgress(resources.getString(R.string.str_tv_live_loading))
            initIM(1400355958, mGroupId)
        }

        getPullNewMsg()
        getLiveVideoShareInfo()
    }

    /**
     * 获取直播间的分享信息
     */
    private fun getLiveVideoShareInfo() {
        val params = RequestParams()
        params.put("ecLiveId", ecLiveId)
        HttpManager.getInstance().post(AppNetConfig.GET_WEBCASTINFO_BY_LIVEID, params, object : BaseResponse<WebcastLiveShareInfo?>() {
            override fun onSuccess(content: String?, obj: BaseBean<WebcastLiveShareInfo?>?, t: WebcastLiveShareInfo?) {
                dismissProgress()
                shareUrl = t?.webcastLiveShareInfo?.webcastUrl
                shareImageUrl = t?.webcastLiveShareInfo?.roomPicUrl
                shareTitle = t?.webcastLiveShareInfo?.webcastName
                shareContent = t?.webcastLiveShareInfo?.webcastDesciption
            }

            override fun onFailure(error: NetError?) {
                dismissProgress()
            }
        })
    }

    // 获取商品推送数量信息，并修改角标
    private fun getPullNewMsg() {
        val params = RequestParams()
        val merchantId = SpUtil.getMerchantid()
        params.put("merchantId", merchantId)
        params.put("ecLiveId", ecLiveId)
        HttpManager.getInstance().post(AppNetConfig.TV_LIVE_PULL_NEW_MSG, params, object : BaseResponse<TVLivePullMsgBean?>() {
            override fun onSuccess(content: String?, obj: BaseBean<TVLivePullMsgBean?>?, t: TVLivePullMsgBean?) {
                dismissProgress()
                if (obj != null && obj.isSuccess && t != null) {
                    if (t.showProductCount == 0) {
                        showProductCount = 0
                        tv_smg_num?.visibility = View.GONE
                    } else {
                        showProductCount = t.showProductCount
                        tv_smg_num?.visibility = View.VISIBLE
                        tv_smg_num?.text = if (t.showProductCount > 99) "99+" else "${t.showProductCount}"
                    }
                }
            }

            override fun onFailure(error: NetError?) {
                dismissProgress()
            }
        })
    }

    @SuppressLint("SetTextI18n")
    private fun initView() {
        changeLiveStatue(liveStatus)
        anchor_tv_broadcasting_time.text = liveRoomName
        anchor_tv_member_counts.text = "${mCurrentAudienceCount}${resources.getString(R.string.str_tv_live_watch_count_suffix)}"
        mAvatar?.let {
            ImageUtil.loadCircleImage(this, it, anchor_iv_head_icon)
        }
        mInputTextMsgDialog.setOnTextSendListener {
            mIMManger.sendGroupTextMessage(it, mUserId, mGroupId, MESSAGE_PRIORITY_NORMAL, imCallback)
        }
        adapter = VideoLiveGroupTextMsgListAdapter(this, im_msg_listview, messageList)
        im_msg_listview.adapter = adapter
    }

    // 直播重试
    private fun reTryPlayVideo(): Boolean {
        if (videoRestartTime < 3) {
            videoRestartTime++
            BaseYBMApp.handler.postDelayed(
                    { startVideoPlay() },
                    1000 * videoRestartTime)
            return true
        } else {
            return false
        }
    }


    /**
     * 开始播放视频
     */
    private fun startVideoPlay() {
        showVideoLiveLoading()
        video_player?.setUp(mPlayUrl, false, "")
        video_player?.startPlayLogic()
    }

    var sid: String? = ""
    fun onClick(v: View) {
        when (v.id) {
            R.id.btn_message_input -> showInputMsgDialog()
            R.id.btn_back -> finishLivePage()
            R.id.iv_shopping_bag -> {
                getPullNewMsg()
                getSid(v)
            }
            R.id.iv_coupon_bag -> ecLiveId?.let {
                XyyIoUtil.track(XyyIoUtil.ACTION_WEBCAST_COUPON_ENTRANCE, JSONObject().apply {
                    put("webcastId", ecLiveId)
                })
                couponListDialog.show(v)
            }
            R.id.rl_reload -> startVideoPlay()
            R.id.btn_like -> {
                mIMManger?.sendGroupTextMessage(
                        JSONObject().apply {
                            put("MessageType", "memberLiked")
                            put("ecLiveId", ecLiveId)
                            put("currentDate", System.currentTimeMillis())
                        }.toString(),
                        mUserId, mGroupId, MESSAGE_PRIORITY_HIGH, imCallback)
            }
            R.id.btn_share -> {
                mSharePopWindow.show(tool_bar)
                // 直播间分享埋点
                XyyIoUtil.track(XyyIoUtil.ACTION_GENERAL_CLICK, JSONObject().apply {
                    put("pageName", "page_Webcast")
                    put("locatedText", "分享")
                    put("extendedInfo", "${ecLiveId}")
                })

            }
        }
    }

    private fun showProductListDialog(v: View, sid: String?) {
        updateFlowData(mFlowData, "7", ecLiveId, sid)
        flowDataPageCommoditySearch(mFlowData)
        ecLiveId?.let {
            goodsListDialog.updateFlowData(mFlowData)
            goodsListDialog.show(v)
        }
    }

    /**
     * 获取 sid
     */
    private fun getSid(v: View) {
        getSId(object : BaseResponse<FlowDataAnalysisSId>() {
            override fun onSuccess(content: String, obj: BaseBean<FlowDataAnalysisSId?>, flowDataAnalysisSId: FlowDataAnalysisSId?) {
                super.onSuccess(content, obj, flowDataAnalysisSId)
                showProductListDialog(v, flowDataAnalysisSId?.sid)
            }

            override fun onFailure(error: NetError) {
                super.onFailure(error)
                showProductListDialog(v, "-1")
            }
        })
    }

    private fun finishLivePage() {
        if (liveStatus != 1) {
            finish()
        } else {
            showQuitAlertDialog()
        }
    }

    private fun showQuitAlertDialog() {
        val dialogEx = AlertDialogEx(mySelf)
        dialogEx.setTitle("")
                .setMessage("直播精彩进行中，还有更多优惠，确认要退出吗")
                .setCancelButton("取消", null)
                .setConfirmButton("确定") { _, _ -> finish() }
                .setMessageGravity(Gravity.CENTER)
                .setCancelable(false)
                .setCanceledOnTouchOutside(false)
                .show()
        val mutableListOf = mutableListOf<String>("1", "2")
        mutableListOf.map { it.length }.filter { it > 3 }

    }

    /**
     * 发送普通消息弹出框
     */
    private fun showInputMsgDialog() {
        val windowManager = windowManager
        val display = windowManager.defaultDisplay
        val lp: WindowManager.LayoutParams? = mInputTextMsgDialog?.window?.attributes
        val size = Point()
        display.getSize(size)
        lp?.width = size.x //设置宽度
        mInputTextMsgDialog?.apply {
            window?.attributes = lp
            setCancelable(true)
            window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_VISIBLE)
            show()
        }
    }

    /**
     * 增加一个点赞
     */
    private fun addFavor() {
        if (heart_layout != null) {
            heart_layout?.addFavor()
        }
        //点赞发送请求限制
        if (mLikeFrequeControl == null) {
            mLikeFrequeControl = TCFrequeControl()
            mLikeFrequeControl?.init(2, 1)
        }
    }

    override fun getVideoLiveMessageCallback(): IVideoLiveMessageReceiver? = object : IVideoLiveMessageReceiver {
        // 视频播放状态切换
        override fun onVideoLiveStateChange(messageStatusBean: VideoLiveMessageStatusBean?) {
            LogUtils.e("im video change ${messageStatusBean.toString()}")
            messageStatusBean?.also {
                liveStatus = it.liveStatus
                changeLiveStatue(liveStatus)
            }
        }

        // 直播间发放优惠券
        override fun onVideoLiveDispatchCoupon(messageCouponBean: VideoLiveMessageCouponBean?) {
            couponPopWindow.newData(messageCouponBean?.couponMessageInfo, ecLiveId ?: "")
            XyyIoUtil.track(XyyIoUtil.PAGE_WEBCAST_COUPON_EXPOSURE, JSONObject().apply {
                put("webcastId", ecLiveId)
                put("couponId", messageCouponBean?.couponMessageInfo?.templateId)
                put("source", "2")  // 1.列表；2.卡片
            })
            couponPopWindow.show(iv_coupon_bag)
        }

        // 直播间发放商品
        override fun onVideoLiveDispatchDiscountGoods(messageGoodsBean: VideoLiveMessageGoodsBean?) {
            getPullNewMsg()
            messageGoodsBean?.takeIf {
                it.opType == 1  // 上架商品才展示弹窗，下架只更新角标数量
            }?.apply {
                discountGoodsPopUpWindow.newData(this?.productMessageInfo, this?.productMessageInfo?.licenseStatus
                        ?: 0)
                if (!goodsListDialog.isShow) {
                    discountGoodsPopUpWindow.show(iv_shopping_bag)
                }
            }
        }

        // 观众进入
        @SuppressLint("SetTextI18n")
        override fun onVideoLiveMemberEnter(messageEnterRoom: VideoLiveMessageJoinMemberBean?) {
            anchor_tv_member_counts.text = "${messageEnterRoom?.audienceCount}${resources.getString(R.string.str_tv_live_watch_count_suffix)}"
            VideoLiveChatBean(messageEnterRoom?.memberInfo?.nickName
                    ?: "", messageEnterRoom?.message, MEMBER_ENTER).let { notifyMsg(it) }
        }

        // 收到点赞
        override fun onVideoLiveFavor(messageFavor: VideoLiveMessageJoinMemberBean?) {

            VideoLiveChatBean((if (messageFavor?.memberInfo?.isSelf
                            ?: false) "我" else messageFavor?.memberInfo?.nickName ?: ""),
                    " 点了赞",
                    PRAISE)
                    .let { notifyMsg(it) }
            // 点赞埋点
            if (messageFavor?.memberInfo?.isSelf ?: false) {
                XyyIoUtil.track(XyyIoUtil.ACTION_WEBCAST_LIKE, JSONObject().apply { put("webcastId", ecLiveId) })
            }
            addFavor()
        }

        // 收到普通消息
        override fun onReceiveGroupTextMessage(msgID: String?, groupID: String?, sender: MemberInfo?, text: String?) {
            VideoLiveChatBean(sender?.nickName ?: "", text ?: "", TEXT_TYPE, sender?.isSelf
                    ?: false).let { notifyMsg(it) }
        }
    }

    private fun changeLiveStatue(liveStatus: Int) {
        when (liveStatus) {
            //未开始
            VIDEO_LIVE_STATUS_NO_START -> {
                fl_error?.visibility = View.VISIBLE
                rl_reload?.visibility = View.GONE
                tv_wait_over?.visibility = View.VISIBLE
                tv_wait_over?.setCompoundDrawablesWithIntrinsicBounds(null, resources.getDrawable(R.drawable.icon_tv_live_wait), null, null)
                tv_wait_over?.setText(resources.getString(R.string.str_tv_live_unstart))
            }
            //正在直播
            VIDEO_LIVE_STATUS_LIVING -> {
                fl_error?.visibility = View.GONE
            }
            //直播中断
            VIDEO_LIVE_STATUS_STREAM_INTERRUPT -> {
                showVideoReload()
            }
            //直播结束
            VIDEO_LIVE_STATUS_STOP -> {
                fl_error?.visibility = View.VISIBLE
                rl_reload?.visibility = View.GONE
                iv_coupon_bag?.visibility = View.GONE
                tv_wait_over?.visibility = View.VISIBLE
                GSYVideoManager.releaseAllVideos()
                tv_wait_over?.setCompoundDrawablesWithIntrinsicBounds(null, resources.getDrawable(R.drawable.icon_tv_live_over), null, null)
                tv_wait_over?.setText(resources.getString(R.string.str_tv_live_end))
            }
        }
    }

    // 展示重新加载视频的ui
    private fun showVideoReload() {
        fl_error?.visibility = View.VISIBLE
        rl_reload?.visibility = View.VISIBLE
        tv_wait_over?.visibility = View.GONE
    }

    // 展示视频加载中的UI
    private fun showVideoLiveLoading() {
        fl_error?.visibility = View.VISIBLE
        rl_reload?.visibility = View.GONE
        tv_wait_over?.visibility = View.VISIBLE
        tv_wait_over?.setCompoundDrawablesWithIntrinsicBounds(null, resources.getDrawable(R.drawable.icon_tv_live_wait), null, null)
        tv_wait_over?.setText(resources.getString(R.string.str_tv_live_loading))
    }

    /**
     * 更新消息列表
     */
    fun notifyMsg(chatBean: VideoLiveChatBean) {
        if (messageList.size > 1000) {
            while (messageList.size > 900) {
                messageList.removeAt(0)
            }
        }
        messageList.add(chatBean)
        adapter?.notifyDataSetChanged()
    }

    /**
     * im加载出错
     */
    override fun onIMLoadFail() {

    }

    /**
     * IM加载成功
     */
    override fun onIMLoadSuccess() {
        XyyIoUtil.track(XyyIoUtil.ACTION_WEBCAST_ENTEREXIT, JSONObject().apply {
            put("webcastId", ecLiveId)
            put("enterExitId", 1)  // 1进入直播间
        })
    }

    override fun onIMQuiteGroupdSuccess() {
    }


    override fun getSig(): String? = mUserSig

    override fun onPause() {
        super.onPause()
        GSYVideoManager.onPause()
    }


    override fun onRestart() {
        super.onRestart()
        video_player?.setUp(mPlayUrl, false, "")
        video_player?.startPlayLogic()
    }


    override fun onDestroy() {
        super.onDestroy()
        GSYVideoManager.releaseAllVideos()
        mIMManger.removeAllSimpleGroupMessageListener()
        mIMManger.quiteGroup(mUserId, groupId, imCallback)
        mIMManger.unInitStrategy(imCallback)
        XyyIoUtil.track(XyyIoUtil.ACTION_WEBCAST_ENTEREXIT, JSONObject().apply {
            put("webcastId", ecLiveId)
            put("enterExitId", 2)  // 2退出·直播间
        })

    }

    override fun onBackPressed() {
        //释放所有
        video_player?.setVideoAllCallBack(null)
        super.onBackPressed()
    }


}