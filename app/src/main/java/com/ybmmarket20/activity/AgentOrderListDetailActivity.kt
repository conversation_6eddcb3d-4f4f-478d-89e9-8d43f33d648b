package com.ybmmarket20.activity

import android.annotation.SuppressLint
import androidx.recyclerview.widget.LinearLayoutManager
import android.view.View
import com.github.mzule.activityrouter.annotation.Router
import com.ybm.app.bean.NetError
import com.ybm.app.view.WrapLinearLayoutManager
import com.ybmmarket20.R
import com.ybmmarket20.adapter.AgentOrderDetailAdapter
import com.ybmmarket20.bean.AgentOrderDetailBean
import com.ybmmarket20.bean.AgentOrderListRowBean
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.common.*
import com.ybmmarket20.common.eventbus.Event
import com.ybmmarket20.common.eventbus.EventBusUtil
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.constant.IntentCanst.*
import com.ybmmarket20.network.HttpManager
import com.ybmmarket20.utils.DateTimeUtil
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.SpUtil
import io.reactivex.Observable
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import kotlinx.android.synthetic.main.activity_agent_detail.*
import java.util.concurrent.TimeUnit


/**
 * 代下单订单详情
 */
@Router("sproutOrderDetail/:order_id", "sproutOrderDetail")
class AgentOrderListDetailActivity : BaseActivity() {

    var orderId: String = ""
    var adapter: AgentOrderDetailAdapter? = null
    var orderBean: AgentOrderListRowBean? = null
    var reason: String? = null
    var agentOrderId: String = ""
    val compositeDisposable: CompositeDisposable = CompositeDisposable()

    override fun getContentViewId(): Int = R.layout.activity_agent_detail

    override fun initData() {
        intent.apply {
            orderBean = getParcelableExtra("orderBean")
            if (orderBean == null) {
                orderId = intent.getStringExtra("order_id") ?: ""
                orderBean = AgentOrderListRowBean()
                orderBean!!.id = orderId
            } else {
                orderId = orderBean!!.id!!
            }
        }
        tv_agent_order_confirm.setOnClickListener {
            AlertDialogEx(this).let {
                it.setTitle(resources.getString(R.string.title_desc))
                it.setMessage("是否确认提交订单?")
                it.addButton(2002, getString(R.string.cancel)) { _, _ -> it.dismiss() }
                it.addButton(2001, getString(R.string.confirm)) { _, _ -> confirmOrder() }
                it.show()
            }
        }
        tv_agent_order_reject.setOnClickListener {
            AgentOrderRejectDialog(this, orderId).apply {
                setOnRejectListener {
                    handleStatus(AGENT_ORDER_STATUS_REJECTED, it)
                    this@AgentOrderListDetailActivity.tv_agent_order_status_reject_content.text = "关闭原因：$it"
                    //驳回后通知列表页更新
                    orderBean!!.status = AGENT_ORDER_STATUS_REJECTED
                    EventBusUtil.sendEvent(Event<AgentOrderListRowBean>(RX_BUS_AGENT_ORDER_REJECT_ORDER, orderBean))
                }
                show()
            }
        }
        tv_agent_order_phone_content.setOnClickListener {
            tv_agent_order_phone_content.text?.let {
                RoutersUtils.telPhone(true, it.toString())
            }
        }

        rl_agent_order_status_reject.setOnClickListener {
            reason?.also { rejectReason ->
                RejectMessageDialog(this).let {
                    it.setTitle("关闭原因")
                    it.setMessage(rejectReason)
                    it.setConfirmButton("我知道了") { _, _ -> it.dismiss() }
                    it.show()
                }
            }

        }

        getData()
    }

    private fun getData() {
        showProgress()
        val params = RequestParams().apply {
            put("id", orderId)
            put("merchantId", SpUtil.getMerchantid())
        }
        HttpManager.getInstance().post(AppNetConfig.AGENT_ORDER_DETAIL, params, object : BaseResponse<AgentOrderDetailBean>() {
            override fun onSuccess(content: String?, obj: BaseBean<AgentOrderDetailBean>?, t: AgentOrderDetailBean?) {
                super.onSuccess(content, obj, t)
                dismissProgress()
                if (obj != null && t != null) {
                    agentOrderId = t.orderId ?:""
                    handleNetData(t)
                    if (t.status == AGENT_ORDER_STATUS_UNCONFIRM) {
                        val remainderTime: Long = t.confirmExpireTime - t.currentDate
                        if (remainderTime > 0) countDown(remainderTime)
                    }
                    setAgentDetailAdapter(t)
                    t.refuseReason?.also {
                        reason = if (it == "其它") {
                            t.refuseExplan
                        } else {
                            t.refuseReason
                        }
                    }
                    tv_agent_order_status_reject_content.text = "关闭原因：$reason"
                }
            }

            override fun onFailure(error: NetError?) {
                super.onFailure(error)
                dismissProgress()
            }
        })
    }

    @SuppressLint("SetTextI18n")
    private fun handleNetData(detail: AgentOrderDetailBean) {
        detail.also {
            handleStatus(it.status, it.refuseReason ?: "")
            tv_agent_order_No_content.text = it.purchaseNo
            tv_agent_order_time_content.text = DateTimeUtil.getLogDateTime(it.createTime)
            tv_agent_order_price_content.text = "¥${it.money}"
            tv_agent_order_submit_content.text = it.saleName
            tv_agent_order_phone_content.text = it.saleMobile
            tv_agent_order_count.text = "${resources.getString(R.string.order_goods)}(${it.orderDetailDtoList?.size
                    ?: "0"})"
        }
    }

    private fun handleStatus(status: Int, rejectMes: String = "") {
        cl_agent_order_bottom.visibility = View.GONE
        when (status) {
            AGENT_ORDER_STATUS_UNCONFIRM -> {
                setStatus(R.drawable.icon_tobeconfirm_bg, R.drawable.icon_tobeconfirm, resources.getString(R.string.tab_name_unconfirm))
                cl_agent_order_bottom.visibility = View.VISIBLE
                tv_agent_order_status_center.visibility = View.VISIBLE
                tv_agent_order_status_text.visibility = View.GONE
                tv_agent_order_status_content.visibility = View.GONE
                rl_agent_order_status_reject.visibility = View.GONE
                tv_agent_order_status_center.text = resources.getString(R.string.tab_name_unconfirm)
            }
            AGENT_ORDER_STATUS_REJECTED -> {
                setStatus(R.drawable.icon_rejected_bg, R.drawable.icon_rejected, resources.getString(R.string.tab_name_rejected))
                tv_agent_order_status_center.visibility = View.GONE
                tv_agent_order_status_text.visibility = View.VISIBLE
                tv_agent_order_status_content.visibility = View.VISIBLE
                tv_agent_order_status_reject_content.visibility = View.VISIBLE
                tv_agent_order_status_content.visibility = View.GONE
                rl_agent_order_status_reject.visibility = View.VISIBLE
                tv_agent_order_status_reject_arrow.visibility = View.VISIBLE
                tv_agent_order_status_reject_content.text = "关闭原因：$rejectMes"
                reason = rejectMes
            }
            AGENT_ORDER_STATUS_CANCELED -> {
                setStatus(R.drawable.icon_cancled_bg, R.drawable.icon_cancled, resources.getString(R.string.tab_name_canceled), "订单超时取消")
                tv_agent_order_status_center.visibility = View.GONE
                tv_agent_order_status_text.visibility = View.VISIBLE
                tv_agent_order_status_content.visibility = View.VISIBLE
                rl_agent_order_status_reject.visibility = View.GONE
            }

            else -> cl_agent_order_detail_status.visibility = View.GONE
        }
    }

    private fun setStatus(bg: Int, icon: Int, statusTitle: String, content: String = "") {
        iv_agent_order_status.setImageResource(bg)
        iv_agent_order_status_icon.setImageResource(icon)
        tv_agent_order_status_text.text = statusTitle
        tv_agent_order_status_content.text = "${resources.getString(R.string.close_reason_with_symbol)}$content"
    }

    private fun setAgentDetailAdapter(detail: AgentOrderDetailBean) {
        val rowData = detail.orderDetailDtoList
        adapter = AgentOrderDetailAdapter(R.layout.item_agent_order_detail, rowData ?: listOf())
        rv_agent_order_detail.layoutManager = WrapLinearLayoutManager(this, LinearLayoutManager.VERTICAL, false)
        rv_agent_order_detail.adapter = adapter
    }

    /**
     * 确定
     */
    private fun confirmOrder() {
        showProgress()
        val params = RequestParams().apply {
            put("id", orderId)
            put("merchantId", SpUtil.getMerchantid())
        }
        HttpManager.getInstance().post(AppNetConfig.AGENT_ORDER_CONFIRM_ORDER, params, object : BaseResponse<Any>() {
            override fun onSuccess(content: String?, obj: BaseBean<Any>?, t: Any?) {
                super.onSuccess(content, obj, t)
                if(obj != null && obj.isSuccess()){
//                    ToastUtils.showLong("订单确定成功,可跳转至我的订单页面查询")
                    dismissProgress()
                    EventBusUtil.sendEvent(Event<String>(RX_BUS_AGENT_ORDER_CONFIRM_ORDER, orderBean?.id?:""))
                    RoutersUtils.open("ybmpage://orderdetail/$agentOrderId")
                    finish()
                }
            }

            override fun onFailure(error: NetError?) {
                super.onFailure(error)
                dismissProgress()
            }
        })
    }

    /**
     * 时间格式化
     */
    private fun timeFormat(millisUntilFinished: Long): String {
        val h = millisUntilFinished / 1000 / 3600
        val fen = millisUntilFinished / 1000 % 3600 / 60
        val s = millisUntilFinished / 1000 % 60
        val hh = if (h < 10) {
            "0$h"
        } else {
            "" + h
        }
        val ff = if (fen < 10) {
            "0$fen"
        } else {
            "" + fen
        }
        val ss = if (s < 10) {
            "0$s"
        } else {
            "" + s
        }
        return "$hh:$ff:$ss"
    }

    /**
     * 倒计时
     * remainderTime 剩余时间
     */
    private fun countDown(remainderTime: Long) {
        compositeDisposable.add(
                Observable
                        .interval(0, 1, TimeUnit.SECONDS)
                        .take(remainderTime/1000 + 1)
                        .map { remainderTime - it * 1000 }
                        .subscribeOn(Schedulers.computation())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribe({tv_agent_order_count_down.text = "剩余确认时间：${timeFormat(it)}"},
                                { it.printStackTrace()}) {
                            compositeDisposable.dispose()
//                            handleStatus(AGENT_ORDER_STATUS_CANCELED)
//                            EventBusUtil.sendEvent(Event<AgentOrderListRowBean>(RX_BUS_AGENT_ORDER_CANCLE, orderBean))
                            tv_agent_order_count_down.text = "剩余确认时间：00:00:00"
                        }
        )

    }

    override fun onDestroy() {
        super.onDestroy()
        compositeDisposable.dispose()
    }

}