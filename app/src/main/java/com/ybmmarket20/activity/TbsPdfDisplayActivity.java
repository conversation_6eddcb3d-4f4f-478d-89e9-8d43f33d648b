package com.ybmmarket20.activity;

import android.content.Intent;
import android.net.Uri;
import android.text.TextUtils;
import android.view.View;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.github.barteksc.pdfviewer.PDFView;
import com.github.mzule.activityrouter.annotation.Router;
import com.tencent.smtt.sdk.TbsReaderView;
import com.ybm.app.utils.BugUtil;
import com.ybmmarket20.R;
import com.ybmmarket20.common.AppUpdateManagerV2;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.util.ToastUtils;
import com.ybmmarket20.utils.DialogUtil;
import com.ybmmarket20.utils.FileUtil;
import com.ybmmarket20.utils.ShareUtil;
import com.ybmmarket20.utils.SpUtil;
import com.ybmmarket20.utils.analysis.XyyIoUtil;
import com.ybmmarket20.utils.fileTypeUtil.FileTypeEnum;
import com.ybmmarket20.utils.fileTypeUtil.FileTypeUtil;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.net.MalformedURLException;
import java.net.URL;

/**
 * Pdf浏览
 */
@Router({"tbspdfdisplay", "tbspdfdisplay/:fileurl", "tbspdfdisplay/:fileurl/:title"})
public class TbsPdfDisplayActivity extends BaseActivity implements TbsReaderView.ReaderCallback {

    //    private TbsReaderView mTbsReaderView;
    private PDFView pdfView;
    private TextView tvTip;

    private String mFileUrl;
    private String mFileName;
    private RelativeLayout mRootRl;
    private String mTitle;

    private AppUpdateManagerV2 updateManagerV2;
    private File pdfFile;

    private int pdfDownloadStatus = PDF_DOWNLOAD_STATUS_LOADING;
    public static final int PDF_DOWNLOAD_STATUS_LOADING = 0; // 正在下载
    public static final int PDF_DOWNLOAD_STATUS_SUCCESS = 1; // 下载成功
    public static final int PDF_DOWNLOAD_STATUS_FAILED = 2; // 下载失败
    private int shareStatus = PDF_SHARE_FINISH;
    public static final int PDF_SHARE_WAIT_DOWNLOAD = 0; // 等待下载
    public static final int PDF_SHARE_FAILED = 1; // 分享失败
    public static final int PDF_SHARE_FINISH = 2; // 分享完成


    @Override
    protected void initData() {
        String isShowShare = "1";
        try {
            isShowShare = getIntent().getStringExtra("isShowShare");
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (isShowShare == null) isShowShare = "1";
        setTitle("发票详情");
        if (TextUtils.equals(isShowShare, "1")) {
            setRigthImg(new ShareClickListener(), R.drawable.icon_pdf_share);
        }
        mFileUrl = getIntent().getStringExtra("fileurl");
        mTitle = getIntent().getStringExtra("title");
        mFileName = generateFileName();
        if (!TextUtils.isEmpty(mTitle)) {
            setTitle(mTitle);
        }
//        mTbsReaderView = new TbsReaderView(TbsPdfDisplayActivity.this, this);
//        mRootRl = (RelativeLayout) findViewById(R.id.rl_root);
//        mRootRl.addView(mTbsReaderView, new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
        pdfView = findViewById(R.id.pdfView);
        tvTip = findViewById(R.id.tv_tip);

//        displayFile(null);
        downloadPdf();
    }

    private void downloadPdf() {
        updateManagerV2 = new AppUpdateManagerV2();
        updateManagerV2.setDownloadListener(new AppUpdateManagerV2.OnDownloadListener() {
            @Override
            public void onDownloadSuccess(File file) {
                if (isDestroy) return;
                dismissProgress();
                pdfFile = file;
                checkDownloadFile(file);
                displayFile(pdfFile);
                pdfDownloadStatus = PDF_DOWNLOAD_STATUS_SUCCESS;
                if (shareStatus == PDF_SHARE_WAIT_DOWNLOAD) {
                    startShare();
                }
            }

            @Override
            public void onDownloading(int progress) {
                pdfDownloadStatus = PDF_DOWNLOAD_STATUS_LOADING;
            }

            @Override
            public void onDownloadFailed(Exception e) {
                if (isDestroy) return;
                dismissProgress();
                BugUtil.sendBug(new Throwable("pdf文件下载失败"));
                pdfDownloadStatus = PDF_DOWNLOAD_STATUS_FAILED;
                if (shareStatus == PDF_SHARE_WAIT_DOWNLOAD) {
                    ToastUtils.showShort("文件下载失败");
                }
            }
        });
        showProgress();
        updateManagerV2.downFile(mFileUrl, mFileName);
    }

    private void checkDownloadFile(File file) {
        if (file == null) return;
        FileTypeEnum fileTypeEnum = FileTypeUtil.INSTANCE.getFileTypeByPath(file.getAbsolutePath());
        if (TextUtils.equals(fileTypeEnum.getFileTypeName().toUpperCase(), FileTypeEnum.PDF.getFileTypeName())) {
            if (file.getAbsolutePath().endsWith(".pdf")) return;
            FileInputStream fis = null;
            try {
                fis = new FileInputStream(FileUtil.getFileFullPath(pdfFile.getName()));
                String fileNewName = pdfFile.getName() + ".pdf";
                if (!TextUtils.isEmpty(fileNewName)) {
                    File newFile = new File(FileUtil.getFileFullPath(fileNewName));
                    if (!newFile.exists()) {
                        FileUtil.copyFile(FileUtil.getFileFullPath(pdfFile.getName()), FileUtil.getFileFullPath(newFile.getName()));
                        pdfFile.delete();
                    }
                    pdfFile = newFile;
                }
            } catch (FileNotFoundException e) {
                e.printStackTrace();
            } finally {
                if (fis != null) {
                    try {
                        fis.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            }
        }
    }


    @Override
    public int getContentViewId() {
        return R.layout.activity_tbs_pdf_display;
    }

    @Override
    public void onCallBackAction(Integer integer, Object o, Object o1) {

    }

    /**
     * 生成文件名称
     *
     * @return
     */
    private String generateFileName() {
        try {
            URL url = new URL(mFileUrl);
            String query = url.getQuery();
            if (!TextUtils.isEmpty(query)) {
                String[] queryArr = query.split("&");
                for (String s : queryArr) {
                    if (!TextUtils.isEmpty(s)) {
                        if (TextUtils.equals(s.split("=")[0], "filename")) {
                            return s.split("=")[1];
                        }
                    }
                }
            }
        } catch (MalformedURLException e) {
            throw new RuntimeException(e);
        }
        String fileName = getIntent().getStringExtra("pdffilename");
        if (TextUtils.isEmpty(fileName)) fileName = System.currentTimeMillis() + "";
        if (fileName.contains("/")) fileName = fileName.replace("/", "");
        if (fileName.contains(".")) fileName = fileName.replace(".", "");
        return fileName;
    }

    private void displayFile(File file) {
        try {
            tvTip.setVisibility(View.GONE);
            pdfView.fromFile(file)
                    .enableSwipe(true)          // allows to block changing pages using swipe
                    .defaultPage(0)
                    .onError(t -> {
                        BugUtil.sendBug(new Throwable("userid = "+ SpUtil.getMerchantid() + ";error = "+t.toString() + ";pdfurl = "+mFileUrl));
//                        ToastUtils.showShort("文件加载错误");
                        tvTip.setVisibility(View.VISIBLE);
                    })
                    .enableAnnotationRendering(true) // render annotations (such as comments, colors or forms)
                    .load();

        } catch (Exception e) {
            BugUtil.sendBug(e);
            Uri uri = Uri.parse(mFileUrl);
            Intent intent = new Intent(Intent.ACTION_VIEW, uri);
            startActivity(intent);
            finish();
        }
    }

    private String parseFormat(String fileName) {
        return fileName.substring(fileName.lastIndexOf(".") + 1);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
//        if (mTbsReaderView != null) {
//            mTbsReaderView.onStop();
//        }
    }

    @Override
    public String getPageName() {
        String pageName = "";
        pageName = ((TextView) getView(R.id.tv_title)).getText().toString();
        if ("发票详情".equals(pageName)) {
            pageName = XyyIoUtil.PAGE_ORDERINVOICEDETAILS;
        }
        return pageName;
    }

    /**
     * 开始分享
     */
    private void startShare() {
        if (pdfDownloadStatus == PDF_DOWNLOAD_STATUS_LOADING) {
            shareStatus = PDF_SHARE_WAIT_DOWNLOAD;
        } else if (pdfDownloadStatus == PDF_DOWNLOAD_STATUS_FAILED) {
            shareStatus = PDF_SHARE_WAIT_DOWNLOAD;
            downloadPdf();
        } else if (pdfDownloadStatus == PDF_DOWNLOAD_STATUS_SUCCESS) {
            share();
            shareStatus = PDF_SHARE_FINISH;
        }
    }

    private void share() {
        if (pdfFile == null) return;
        DialogUtil.shareDialogInvoice(this, content -> {
            if ("wx".equals(content)) {
                ShareUtil.sharePdfFileWechatFriend(TbsPdfDisplayActivity.this, pdfFile);
            }
        }, DialogUtil.SHARE_TYPE_WECHAT);
    }

    class ShareClickListener implements View.OnClickListener {

        @Override
        public void onClick(View v) {
            startShare();
        }
    }

}
