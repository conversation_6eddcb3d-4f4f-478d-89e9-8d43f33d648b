package com.ybmmarket20.activity

import android.content.Intent
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.style.AbsoluteSizeSpan
import android.text.style.ForegroundColorSpan
import android.text.style.StrikethroughSpan
import android.util.SparseArray
import android.view.View
import android.view.inputmethod.EditorInfo
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.activity.viewModels
import androidx.core.content.ContextCompat
import androidx.core.widget.addTextChangedListener
import androidx.lifecycle.Observer
import androidx.lifecycle.SavedStateViewModelFactory
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.github.mzule.activityrouter.annotation.Router
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.*
import com.ybmmarket20.common.AlertDialogEx
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.common.BaseResponse
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.common.util.ToastUtils
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.constant.CURRENT_PAGE_RESULT_QR
import com.ybmmarket20.constant.CURRENT_PAGE_RESULT_VOICE
import com.ybmmarket20.network.HttpManager
import com.ybmmarket20.utils.ImageUtil
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.SpUtil
import com.ybmmarket20.utils.UiUtils
import com.ybmmarket20.utils.analysis.BaseFlowData
import com.ybmmarket20.utils.analysis.XyyIoUtil
import com.ybmmarket20.utils.analysis.flowDataPageListPageExposure
import com.ybmmarket20.utils.analysis.flowDataPageListPageExposureForFeed
import com.ybmmarket20.view.ProductEditLayoutNew
import com.ybmmarket20.view.ProductEditLayoutSuiXinPin
import com.ybmmarket20.view.homesteady.whenAllNotNull
import com.ybmmarket20.viewmodel.SearchSpellGroupRecommendGoodsViewModel
import com.ybmmarket20.viewmodel.SpellGroupRecommendGoodsViewModel
import com.ybmmarket20.viewmodel.viewstore.GlobalViewModelStore
import com.ybmmarketkotlin.utils.TextWithPrefixTag
import com.zxing.activity.CaptureActivity
import kotlinx.android.synthetic.main.activity_search.iv_a_magnifying_glass
import kotlinx.android.synthetic.main.activity_search.iv_clear
import kotlinx.android.synthetic.main.activity_search.title_et
import kotlinx.android.synthetic.main.activity_search.title_left_search
import kotlinx.android.synthetic.main.activity_search_spell_group_recommend_goods.*
import kotlin.math.absoluteValue

/**
 * 搜索拼团推荐拼
 */
@Router("searchspellgrouprecommendgoods")
class SearchSpellGroupRecommendGoodsActivity : BaseActivity() {

    //搜索
    private val searchViewModel: SearchSpellGroupRecommendGoodsViewModel by viewModels()
    private var preSearchKey: String? = ""

    //随心拼购物车
    val localCartViewModel: SpellGroupRecommendGoodsViewModel by lazy {
        ViewModelProvider(
            GlobalViewModelStore.get().getGlobalViewModelStore(),
            SavedStateViewModelFactory(application, this)
        ).get(SpellGroupRecommendGoodsViewModel::class.java)
    }

    //店铺编码
    var shopCode: String? = null

    // 店铺名称
    var shopName: String? = null

    //埋点参数
    var flowData: BaseFlowData? = null

    //列表数据
    val datalist = mutableListOf<RowsBean>()
    val mAdapter = SearchSpellGroupRecommendGoodsAdapter(datalist)
    var mFeedParams: RequestParams? = null

    //搜索关键字
    var mKeyword: String? = null

    //是否第三方厂家(0：否；1：是)
    private var isThirdCompany: Int = 0

    override fun getContentViewId(): Int = R.layout.activity_search_spell_group_recommend_goods

    override fun initData() {
        try {
            mKeyword = intent.getStringExtra("keyword")
            shopName = intent.getStringExtra("shopName")
        } catch (ignore: Exception) {
            mKeyword = null
            shopName = null
        }
        shopCode = localCartViewModel.shopCode
        isThirdCompany = localCartViewModel.isThirdCompany
        //初始化监听器
        initListener()
        //初始化livedata观察者并注册
        initObserver()
        //初始化列表
        initList()
        //loading
        showProgress()
        if (!mKeyword.isNullOrEmpty()) {
            title_et.setText(mKeyword)
        }
        //无keyword获取搜索列表数据
        getSearchData()
        XyyIoUtil.track(
            "action_freeBuy_searchResult", hashMapOf(
                "shop_code" to localCartViewModel.shopCode,
                "org_id" to localCartViewModel.orgId,
                "sku_id" to localCartViewModel.mainGoodsSkuId
            )
        )
    }

    /**
     * 初始化列表
     */
    private fun initList() {
        mAdapter.setEnableLoadMore(true)
        rv.layoutManager = LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false)
        rv.adapter = mAdapter
        mAdapter.setEmptyView(
            this,
            R.layout.layout_search_product_empty_view,
            R.drawable.icon_empty,
            "啊哦...\n没有找到相关的商品"
        )
        mAdapter.setOnLoadMoreListener { getSearchData() }
    }

    /**
     * 搜索随心拼商品
     */
    private fun getSearchData() {
        searchViewModel.searchSpellGroupRecommendGoods(
            if (mFeedParams == null) {
                val map = mapOf(
                    "tagList" to "YBM_ACT_SUI_XIN_PIN",
                    "shopCodes" to localCartViewModel.shopCode,
                    "keyword" to (mKeyword ?: ""),
                    "isThirdCompany" to "${localCartViewModel.isThirdCompany}"
                ).toMutableMap()
                localCartViewModel.mainGoodsPId?.let { map.put("excludeIds", it) }
                map
            }
            else mFeedParams!!.paramsMap
        )
    }

    /**
     * 初始化监听器
     */
    private fun initListener() {
        //扫描二维码
        iv_a_magnifying_glass.setOnClickListener {
//            RoutersUtils.open("ybmpage://captureactivity?current_page=searchspellgrouprecommendgoods")
            startActivityForResult(Intent(this, CaptureActivity::class.java).apply {
                putExtra("current_page", "searchspellgrouprecommendgoods")
            }, CURRENT_PAGE_RESULT_QR)
        }
        //语音或清除搜索框
        iv_clear.setOnClickListener {
            if (title_et.text.trim().isEmpty()) {
//                RoutersUtils.open("ybmpage://searchvoiceactivity?fromPage=searchspellgrouprecommendgoods")
                startActivityForResult(Intent(this, SearchVoiceActivity::class.java).apply {
                    putExtra("current_page", "searchspellgrouprecommendgoods")
                }, CURRENT_PAGE_RESULT_VOICE)
            } else {
                title_et.text.clear()
            }
        }
        //监听内容变化控制清除和语音按钮
        title_et.addTextChangedListener(
            afterTextChanged = { editable ->
                if (editable.isNullOrEmpty()) {
                    iv_clear.setImageResource(R.drawable.icon_home_steady_voice)
                } else {
                    iv_clear.setImageResource(R.drawable.clear_sousou)
                }
            }
        )
        title_et.setOnEditorActionListener { _, actionId, _ ->
            if (actionId == EditorInfo.IME_ACTION_SEARCH) {
                if (title_et.text.trim().isNotEmpty()) {
                    mFeedParams = null
                    mKeyword = title_et.text.trim().toString()
                    datalist.clear()
                    getSearchData()
                    XyyIoUtil.trackSug(hashMapOf(
                        "spid" to "3",
                        "wq" to mKeyword,
                        "keyword" to mKeyword,
                        "pkw" to (preSearchKey?: "")
                    ), "3")
                    preSearchKey = mKeyword
                    return@setOnEditorActionListener true
                } else ToastUtils.showLong("请输入搜索关键字")
//                XyyIoUtil.trackSug(hashMapOf(
//                    "spid" to "3",
//                    "wq" to mKeyword,
//                    "keyword" to mKeyword,
//                    "pkw" to (preSearchKey?: "")
//                ), "3")
                preSearchKey = mKeyword
            }
            return@setOnEditorActionListener false
        }
        //返回按钮
        title_left_search.setOnClickListener { finish() }

        //客服
        iv_service.setOnClickListener {
            sendOnLineService()
        }
    }

    /**
     * 初始化observer
     */
    private fun initObserver() {
        searchViewModel.searchSpellGroupRecommendGoodsLiveData.observe(this, Observer {
            dismissProgress()
            if (it.isSuccess) {
                if (datalist.isEmpty()) mAdapter.clearTrackData()
                datalist.addAll(it.data.rows)
                mFeedParams = it.data.requestParams
                mAdapter.notifyDataChangedAfterLoadMore(!it.data.isEnd)
                flowData = BaseFlowData(it.data.sptype, it.data.spid, it.data.sid)
            }
        })
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        data?.let {
            if (resultCode == CURRENT_PAGE_RESULT_QR) {
                //二维码页面返回
                mKeyword = data.getStringExtra("mCode")
            } else if (resultCode == CURRENT_PAGE_RESULT_VOICE) {
                //语音页面
                mKeyword = data.getStringExtra("key")
            }
            if (!mKeyword.isNullOrEmpty()) {
                mFeedParams = null
                title_et.setText(mKeyword)
                getSearchData()
            }
        }
    }

    inner class SearchSpellGroupRecommendGoodsAdapter(data: List<RowsBean>) :
        YBMBaseAdapter<RowsBean>(R.layout.item_search_spell_group_recommend_goods, data) {

        private val traceProductData = SparseArray<String>()

        fun clearTrackData() {
            traceProductData.clear()
        }

        override fun bindItemView(baseViewHolder: YBMBaseHolder?, t: RowsBean?) {
            whenAllNotNull(baseViewHolder, t) { holder, bean ->
                ImageUtil.load(
                    mContext,
                    AppNetConfig.LORD_IMAGE + bean.imageUrl,
                    holder.getView(R.id.iv_goods)
                )
                ImageUtil.loadNoPlace(
                    mContext,
                    AppNetConfig.LORD_TAG + bean.markerUrl,
                    holder.getView(R.id.iv_goods_tag)
                )
                val showName = holder.getView<TextView>(R.id.tv_goods_title)
                showName.TextWithPrefixTag(bean.tags?.titleTags, bean.productName)
                showName.setLineSpacing(0f, 1.1f)
                holder.setText(R.id.tv_price, getPriceSpannableBuilder(bean))
                //加购
                val pel = holder.getView<ProductEditLayoutSuiXinPin>(R.id.pel)
                pel.bindData(
                    bean.productId,
                    bean.status,
                    true,
                    true,
                    1,
                    bean.isSplit == 1,
                    "${
                        localCartViewModel.spellGroupRecommendGoodsLiveData.value?.goodsIdMapping?.get(
                            bean.productId
                        ) ?: 0
                    }"
                )
                val mainGoods =
                    localCartViewModel.spellGroupRecommendGoodsLiveData.value?.mainRowsBean
                pel.setOnAddCartListener(object : ProductEditLayoutSuiXinPin.AddCartListener {
                    override fun onPreAddCart(params: RequestParams?): RequestParams =
                        RequestParams()

                    override fun onAddCartSuccess(params: ProductEditLayoutSuccessParams?) {
//                        mAddChargeLocalCallback?.invoke(pel)
                        hideSoftInput()
                        val goodsAmount =
                            ((params?.amount?.toIntOrNull() ?: 0) - (params?.preAmount ?: 0))
                        val spellGroupGoodsItem = SpellGroupGoodsItem(
                            bean.imageUrl,
                            bean.markerUrl,
                            bean.showName,
                            bean.actSuiXinPin?.suiXinPinPrice ?: "0",
                            bean.productUnit,
                            "${bean.fob}",
                            0,
                            bean.productId,
                            bean.stepNum,
                            bean.isSplit,
                            source = bean.sourceType,
                            nearEffect = bean.nearEffect
                        )
                        localCartViewModel.addSpellGroupRecommendCart(
                            spellGroupGoodsItem,
                            goodsAmount > 0, goodsAmount.absoluteValue
                        )

                        XyyIoUtil.track(
                            "page_ListPage_Purchase", hashMapOf(
                                "commodityId" to localCartViewModel.mainGoodsSkuId,
                                "commodityName" to (bean.showName ?: ""),
                                "sptype" to (flowData?.spType ?: ""),
                                "spid" to (flowData?.spId ?: ""),
                                "sid" to (flowData?.sId ?: ""),
                                "direct" to "5",
                                "source" to bean.sourceType,
                                "index" to "${holder.bindingAdapterPosition}"
                            )
                        )

                    }
                })
                //厂商
                holder.setText(R.id.tv_manufactor, bean.manufacturer)
                //效期
                holder.setText(R.id.tv_effect, "有效期：${bean.nearEffect}")
                //售罄
                val isSettleOut = bean.status == 2 || bean.status == 4 || bean.availableQty <= 0
                holder.getView<TextView>(R.id.tv_settle_out).visibility =
                    if (isSettleOut) View.VISIBLE else View.GONE
                holder.getView<LinearLayout>(R.id.ll_subscribe).visibility =
                    if (isSettleOut) View.VISIBLE else View.GONE
                pel.visibility = if (!isSettleOut) View.VISIBLE else View.GONE
                //到货通知
                val tvGoodsSubscribe = holder.getView<TextView>(R.id.tv_goods_subscribe)
                val ivGoodsSubscribe = holder.getView<ImageView>(R.id.iv_goods_subscribe)
                if (bean.favoriteStatus == 2) {
                    ivGoodsSubscribe.setBackgroundResource(R.drawable.icon_goods_arrival)
                    tvGoodsSubscribe.text = "到货通知"
                } else {
                    ivGoodsSubscribe.setBackgroundResource(R.drawable.icon_goods_subscribe)
                    tvGoodsSubscribe.text = " 已订阅"
                }
                ivGoodsSubscribe.setOnClickListener {
                    checkCollect(
                        bean,
                        holder.adapterPosition,
                        this
                    )
                }
                // 商品列表曝光埋点
                if (flowData != null && traceProductData[holder.bindingAdapterPosition] == null) {
                    XyyIoUtil.track(
                        "page_ListPage_Exposure", hashMapOf(
                            "commodityId" to localCartViewModel.mainGoodsSkuId,
                            "commodityName" to (mainGoods?.goodsTitle ?: ""),
                            "sptype" to (flowData?.spType ?: ""),
                            "spid" to (flowData?.spId ?: ""),
                            "sid" to (flowData?.sId ?: ""),
                            "direct" to "5",
                            "source" to bean.sourceType,
                            "index" to "${holder.bindingAdapterPosition}"
                        )
                    )
                    traceProductData.put(holder.bindingAdapterPosition, bean.productId)
                }
            }
        }

        /**
         * 设置价格样式
         */
        private fun getPriceSpannableBuilder(bean: RowsBean): SpannableStringBuilder {
            //随心拼价格
            val suiXinPinPrice = bean.actSuiXinPin?.suiXinPinPrice ?: "0"
            //原价
            val fob = "¥${bean.fob}"
            val priceBuilder = UiUtils.getPriceWithFormat("¥$suiXinPinPrice", 11)
            val unitBuilder = SpannableStringBuilder("/${bean.productUnit}")
            unitBuilder.setSpan(
                AbsoluteSizeSpan(11, true),
                0,
                unitBuilder.length,
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            )
            priceBuilder.append(unitBuilder)
            if (!UiUtils.transform(bean.fob).equals(UiUtils.transform(suiXinPinPrice))) {


                val originalPriceBuilder = SpannableStringBuilder(fob)
                originalPriceBuilder.setSpan(
                    AbsoluteSizeSpan(11, true),
                    0,
                    originalPriceBuilder.length,
                    Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                )
                originalPriceBuilder.setSpan(
                    ForegroundColorSpan(
                        ContextCompat.getColor(
                            mContext,
                            R.color.color_676773
                        )
                    ), 0, originalPriceBuilder.length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                )
                originalPriceBuilder.setSpan(
                    StrikethroughSpan(),
                    0,
                    originalPriceBuilder.length,
                    Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                )
                priceBuilder.append(" ")
                priceBuilder.append(originalPriceBuilder)
            }
            return priceBuilder
        }
    }

    /**
     * 收藏-取消收藏
     */
    private fun checkCollect(rowsBean: RowsBean, potion: Int, adapter: RecyclerView.Adapter<*>) {
        val id = rowsBean.id
        val collectNet =
            if (rowsBean.favoriteStatus == 1) AppNetConfig.CANCEL_COLLECT else AppNetConfig.COLLECT
        val collectStr = if (rowsBean.favoriteStatus == 1) "取消收藏" else "收藏成功"
        val params = RequestParams()
        val merchantId = SpUtil.getMerchantid()
        params.put("merchantId", merchantId)
        params.put("skuId", id.toString())
        HttpManager.getInstance().post(collectNet, params, object : BaseResponse<EmptyBean?>() {
            override fun onSuccess(content: String?, obj: BaseBean<EmptyBean?>?, t: EmptyBean?) {

                if (obj != null && obj.isSuccess) {
                    if (rowsBean.favoriteStatus == 1) {
                        rowsBean.setFavoriteStatus(2)
                    } else {
                        rowsBean.setFavoriteStatus(1)
                        AlertDialogEx(this@SearchSpellGroupRecommendGoodsActivity)
                            .setMessage("订阅成功")
                            .setConfirmButton("我知道了", object : AlertDialogEx.OnClickListener {
                                override fun onClick(dialog: AlertDialogEx?, button: Int) {
                                    dialog?.dismiss()
                                }

                            }).show()
                    }
                    ToastUtils.showLong(collectStr)
                    adapter.notifyItemChanged(potion)
                }

            }
        })
    }

    /*
     * 在线客服
     * */
    private fun sendOnLineService() {
        val params = RequestParams()
        params.put("isThirdCompany", "$isThirdCompany")
        HttpManager.getInstance()
            .post(AppNetConfig.GET_IM_PACKURL, params, object : BaseResponse<ImPackUrlBean>() {
                override fun onSuccess(
                    content: String?,
                    obj: BaseBean<ImPackUrlBean>?,
                    t: ImPackUrlBean?
                ) {
                    super.onSuccess(content, obj, t)
                    if (obj != null && obj.isSuccess) {
                        if (t != null) {
                            if (isThirdCompany == 1) {
                                RoutersUtils.open(
                                    RoutersUtils.getRouterPopCustomerServiceUrl(
                                        t.IM_PACK_URL,
                                        localCartViewModel.orgId,
                                        "",
                                        shopName ?: ""
                                    )
                                )
                            } else {
                                RoutersUtils.open(
                                    RoutersUtils.getRouterYbmDetailCustomerServiceUrl(
                                        t.IM_PACK_URL
                                    )
                                )
                            }
                        }
                    }
                }
            })
    }
}