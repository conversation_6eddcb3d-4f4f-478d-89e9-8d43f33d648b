package com.ybmmarket20.activity;

import android.content.Context;
import android.content.Intent;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.text.Html;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.github.mzule.activityrouter.annotation.Router;
import com.ybm.app.bean.NetError;
import com.ybm.app.common.BaseYBMApp;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.RefundOrderStatusBean;
import com.ybmmarket20.common.AlertDialogEx;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.util.ConvertUtils;
import com.ybmmarket20.common.util.ToastUtils;
import com.ybmmarket20.common.widget.RoundEditText;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.utils.RoutersUtils;
import com.ybmmarket20.utils.SpUtil;
import com.ybmmarket20.utils.UiUtils;
import com.ybmmarket20.view.ButtonObserver;
import com.ybmmarket20.view.MyImageSpan;

import butterknife.Bind;
import butterknife.OnClick;

/*
 * 编辑收款账号信息
 * */
@Router({"editgathering"})
public class EditGatheringActivity extends BaseActivity {

    @Bind(R.id.iv_back)
    ImageView ivBack;
    @Bind(R.id.tv_title)
    TextView tvTitle;
    @Bind(R.id.tv_right)
    TextView tvRight;
    @Bind(R.id.iv_right)
    ImageView ivRight;
    @Bind(R.id.ll_title)
    RelativeLayout llTitle;
    @Bind(R.id.tv_hint)
    TextView tvHint;
    @Bind(R.id.tv_amount)
    TextView tvAmount;
    @Bind(R.id.tv_balance)
    TextView tvBalance;
    @Bind(R.id.btn_ok)
    ButtonObserver btnOk;
    @Bind(R.id.et_bank_name)
    RoundEditText etBankName;
    @Bind(R.id.et_bank_card)
    RoundEditText etBankCard;
    @Bind(R.id.et_owner)
    RoundEditText etOwner;
    @Bind(R.id.et_cell_phone)
    RoundEditText etCellPhone;

    private String refundOrderId;
    private String id;

    private String refundFee;
    private String refundBalance;

    private String bankName;
    private String bankCard;
    private String owner;
    private String cellphone;

    @Override
    protected int getContentViewId() {
        return R.layout.activity_edit_gathering;
    }

    public static void getInstance(Context from, String refundOrderId, String id, String refundFee, String refundBalance, String bankName, String bankCard, String owner, String cellphone) {
        Intent intent = new Intent(from, EditGatheringActivity.class);
        intent.putExtra("refundOrderId", refundOrderId);
        intent.putExtra("id", id);
        intent.putExtra("refundFee", refundFee);
        intent.putExtra("refundBalance", refundBalance);
        intent.putExtra("bankName", bankName);
        intent.putExtra("bankCard", bankCard);
        intent.putExtra("owner", owner);
        intent.putExtra("cellphone", cellphone);
        from.startActivity(intent);
    }

    @Override
    protected void initData() {

        setTitle("编辑收款账户信息");
        refundOrderId = getIntent().getStringExtra("refundOrderId");
        id = getIntent().getStringExtra("id");

        refundFee = getIntent().getStringExtra("refundFee");
        refundBalance = getIntent().getStringExtra("refundBalance");

        bankName = getIntent().getStringExtra("bankName");
        bankCard = getIntent().getStringExtra("bankCard");
        owner = getIntent().getStringExtra("owner");
        cellphone = getIntent().getStringExtra("cellphone");
        if (TextUtils.isEmpty(refundOrderId)) {
            ToastUtils.showShort("参数错误");
            finish();
            return;
        }

        btnOk.observer(etBankName, etBankCard, etOwner, etCellPhone);
        btnOk.setOnItemClickListener(new ButtonObserver.OnButtonObserverListener() {
            @Override
            public void onButtonObserver(boolean isFlag) {
                if (isFlag) {
                    setButtonStyle(R.drawable.bg_image_apply_for_convoy_btn, UiUtils.getColor(R.color.white));
                } else {
                    setButtonStyle(R.drawable.bg_image_apply_for_convoy_btn_2, UiUtils.getColor(R.color.white));
                }
            }
        });

        if (!TextUtils.isEmpty(refundFee)) {
            tvAmount.setText(Html.fromHtml(String.format(getResources().getString(R.string.text_refund_money), "¥" + refundFee)));
        }
        if (!TextUtils.isEmpty(refundBalance)) {
            tvBalance.setText(Html.fromHtml(String.format(getResources().getString(R.string.text_refund_balance), "¥" + refundBalance)));
        }

        if (!TextUtils.isEmpty(bankName)) {
            etBankName.setText(bankName);
        }
        if (!TextUtils.isEmpty(bankCard)) {
            etBankCard.setText(bankCard);
        }
        if (!TextUtils.isEmpty(owner)) {
            etOwner.setText(owner);
        }
        if (!TextUtils.isEmpty(cellphone)) {
            etCellPhone.setText(cellphone);
        }

        SpannableStringBuilder shopName = getName(getResources().getString(R.string.refund_optimize_hint), R.drawable.icon_refund_optimize_hint);
        if (!TextUtils.isEmpty(shopName)) tvHint.setText(shopName);
    }

    /**
     * 设置按钮风格
     *
     * @param drawableStyle 背景样式
     * @param colorStyle    文字颜色
     */
    public void setButtonStyle(int drawableStyle, int colorStyle) {
        if (btnOk == null) {
            return;
        }
        btnOk.setBackgroundResource(drawableStyle);
        btnOk.setTextColor(colorStyle);
    }

    private SpannableStringBuilder getName(String shopName, Integer icons) {
        SpannableStringBuilder spannableString = new SpannableStringBuilder(shopName);
        Drawable drawable = getResources().getDrawable(icons);
        drawable.setBounds(0, 0, ConvertUtils.dp2px(15), ConvertUtils.dp2px(15));
        MyImageSpan imageSpan = new MyImageSpan(drawable, 2);
        //占个位置
        spannableString.insert(0, "-");
        spannableString.setSpan(imageSpan, 0, 1, Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
        return spannableString;
    }

    @OnClick({R.id.btn_ok})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.btn_ok:
                requestData();
                break;
        }
    }

    private void requestData() {

        String etBankNameStr = etBankName.getText().toString().trim();
        if (TextUtils.isEmpty(etBankNameStr)) {
            UiUtils.toast("开户行及支行不能为空");
            return;
        }
        if (etBankNameStr.length() > 20) {
            ToastUtils.showShort("开户行及支行限20个字，请检查");
            return;
        }
        String etBankCardStr = etBankCard.getText().toString().trim();
        if (TextUtils.isEmpty(etBankCardStr)) {
            UiUtils.toast("银行卡号不能为空");
            return;
        }
        if (!UiUtils.isBankCardNO(etBankCardStr)) {
            UiUtils.toast("请输入正确的银行卡号");
            return;
        }
        String etOwnerStr = etOwner.getText().toString().trim();
        if (TextUtils.isEmpty(etOwnerStr)) {
            UiUtils.toast("开户人不能为空");
            return;
        }
        if (etOwnerStr.length() > 10) {
            UiUtils.toast("开户人限10个字，请检查");
            return;
        }
        String etCellPhoneStr = etCellPhone.getText().toString().trim();
        if (TextUtils.isEmpty(etCellPhoneStr)) {
            UiUtils.toast("联系电话不能为空");
            return;
        }
        if (!UiUtils.isMobile2NO(etCellPhoneStr)) {
            ToastUtils.showShort(R.string.validate_mobile_error);
            return;
        }

        String merchantid = SpUtil.getMerchantid();
        RequestParams params = new RequestParams();
        params.put("merchantId", merchantid);
        params.put("orderRefundId", refundOrderId);
        params.put("id", id);
        params.put("bankName", etBankNameStr);
        params.put("bankCard", etBankCardStr);
        params.put("owner", etOwnerStr);
        params.put("cellphone", etCellPhoneStr);
        HttpManager.getInstance().post(AppNetConfig.SAVE_ORUPDATE_ORDER_REFUND_BANK, params, new BaseResponse<RefundOrderStatusBean>() {

            @Override
            public void onSuccess(String content, BaseBean<RefundOrderStatusBean> obj, RefundOrderStatusBean data) {

                if (obj != null && obj.isSuccess()) {

                    if (data != null && data.checkBankState != 0) {
//                        if (!TextUtils.isEmpty(obj.msg)) {
//                            ToastUtils.showShort(obj.msg);
//                        }
                        ToastUtils.showShort("保存成功");
                        finish();

                    } else {
                        showHintDialog(new AlertDialogEx.OnClickListener() {
                            @Override
                            public void onClick(AlertDialogEx dialog, int button) {
                                Intent intent = new Intent(Intent.ACTION_DIAL, Uri.parse("tel:" + RoutersUtils.kefuPhone));
                                BaseYBMApp.getApp().getCurrActivity().startActivity(intent);
                            }
                        }, "当前退款单客服已受理，如需修改收款账户请联系客服人员!");

                    }
                }
            }

            @Override
            public void onFailure(NetError error) {

            }
        });

    }

    private void showHintDialog(AlertDialogEx.OnClickListener listener, String title) {
        AlertDialogEx dialogEx = new AlertDialogEx(this);
        dialogEx.setMessage(title).setCancelButton("我知道了", new AlertDialogEx.OnClickListener() {
            @Override
            public void onClick(AlertDialogEx dialog, int button) {
                dialog.dismiss();
                finish();
            }
        }).setCancelable(false).setConfirmButton("联系客服", listener).setCanceledOnTouchOutside(false).setTitle(null).show();
    }

}
