package com.ybmmarket20.activity;

import android.view.View;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.RelativeLayout;

import com.github.mzule.activityrouter.annotation.Router;
import com.ybm.app.bean.NetError;
import com.ybm.app.view.CommonRecyclerView;
import com.ybmmarket20.R;
import com.ybmmarket20.adapter.PopMerchantsAdapter;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.PopMerchantsBean;
import com.ybmmarket20.bean.PopMerchantsListBean;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.util.ToastUtils;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.utils.SpUtil;

import java.util.ArrayList;
import java.util.List;

import butterknife.Bind;
import butterknife.OnClick;

/**
 * pop商家列表
 */
@Router("popmerchants")
public class PopMerchantsActivity extends BaseActivity {

    @Bind(R.id.title_left)
    RelativeLayout titleLeft;
    @Bind(R.id.rb_merchants)
    RadioButton rbMerchants;
    @Bind(R.id.fl_merchants)
    FrameLayout flMerchants;
    @Bind(R.id.rb_merchant_list)
    RadioButton rbMerchantList;
    @Bind(R.id.fl_merchant_list)
    FrameLayout flMerchantList;
    @Bind(R.id.tv_menu)
    LinearLayout tvMenu;
    @Bind(R.id.crv_list)
    CommonRecyclerView crvList;

    private int mElectronicTab = 1;
    private int mPageSize = 10;
    private int mPage = 0;

    private PopMerchantsAdapter mAdapter;
    private List<PopMerchantsBean> mDataList = new ArrayList<>();

    @Override
    protected void initData() {

        titleLeft.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });

        mAdapter = new PopMerchantsAdapter(R.layout.item_pop_merchants, mDataList, mElectronicTab);
        mAdapter.openLoadMore(10, true);
        crvList.setListener(new CommonRecyclerView.Listener() {
            @Override
            public void onRefresh() {
                getPlanList(mPage = 0);
            }

            @Override
            public void onLoadMore() {
                getPlanList(mPage);

            }
        });
        crvList.setEnabled(false);
        crvList.setRefreshEnable(true);
        crvList.setAdapter(mAdapter);
        crvList.setEmptyView(R.layout.layout_empty_view);

    }

    /**
     * 获取计划单列表
     */
    private void getPlanList(final int page) {

        final int requestTab = mElectronicTab;
        String merchantId = SpUtil.getMerchantid();
        RequestParams params = new RequestParams();
        params.put("merchantId", merchantId);
        params.put("isBuyCompany", "" + mElectronicTab);
        params.put("limit", String.valueOf(mPageSize));
        params.put("offset", String.valueOf(String.valueOf(page)));
        HttpManager.getInstance().post(AppNetConfig.COMPANY_CENTER_COMPANYINFO_POPCOMPANYLIST, params, new BaseResponse<PopMerchantsListBean>() {

            @Override
            public void onSuccess(String content, BaseBean<PopMerchantsListBean> obj, PopMerchantsListBean data) {
                dismissProgress();
                //确保是最后一次的请求
                if (requestTab != mElectronicTab) {
                    return;
                }
                crvList.setRefreshing(false);
                if (obj != null) {
                    if (obj.isSuccess()) {
                        if (data != null) {
                            if (data.getRows() != null) {

                                if (data.getRows().size() > 0) {
                                    if (page <= 0) {
                                        PopMerchantsActivity.this.mPage = 1;
                                    } else {
                                        PopMerchantsActivity.this.mPage++;
                                    }
                                }

                                if (page <= 0) {
                                    if (mDataList == null) {
                                        mDataList = new ArrayList<>();
                                    }
                                    mDataList.clear();
                                    if (mDataList.size() <= 0 && data.getRows() != null) {
                                        mDataList.addAll(data.getRows());
                                    }else{
                                        if (data.getRows() == null ||data.getRows().isEmpty()) {

                                        } else {
                                            for (PopMerchantsBean bean : data.getRows()) {
                                                if (mDataList.contains(bean)) {
                                                    mDataList.remove(bean);
                                                }
                                            }
                                            mDataList.addAll(0,  data.getRows());
                                        }
                                    }
                                    mAdapter.setNewData(mDataList);
                                    mAdapter.notifyDataChangedAfterLoadMore(mDataList.size() >= mPageSize);
                                } else {
                                    if (data.getRows() == null || data.getRows().size() <= 0) {
                                        mAdapter.notifyDataChangedAfterLoadMore(false);
                                    } else {

                                        for (PopMerchantsBean bean : data.getRows()) {
                                            if (mDataList.contains(bean)) {
                                                mDataList.remove(bean);
                                            }
                                        }
                                        mDataList.addAll(data.getRows());
                                        mAdapter.setNewData(mDataList);
                                        mAdapter.notifyDataChangedAfterLoadMore(data.getRows().size() >= mPageSize);
                                    }
                                }
                            }
                        }
                    } else {
                        mAdapter.setNewData(mDataList);
                    }
                } else {
                    mAdapter.notifyDataChangedAfterLoadMore(false);
                }
            }

            @Override
            public void onFailure(NetError error) {
                dismissProgress();
                if (requestTab != mElectronicTab) {
                    return;
                }
                ToastUtils.showShort(error.message);
                crvList.setRefreshing(false);
                mAdapter.notifyDataChangedAfterLoadMore(false);
            }
        });
    }

    @Override
    public int getContentViewId() {
        return R.layout.activity_pop_merchants;
    }

    @OnClick({R.id.fl_merchants, R.id.fl_merchant_list})
    public void onViewClicked(View view) {
        switch (view.getId()) {

            case R.id.fl_merchants:
                switchTab(1);
                break;
            case R.id.fl_merchant_list:
                switchTab(0);
                break;
        }
    }

    private void switchTab(int tab) {
        //如果点击的是同一个tab就不请求数据
        if (mElectronicTab == tab) {
            return;
        }
        mElectronicTab = tab;
        rbMerchants.setChecked(tab == 1);
        rbMerchantList.setChecked(tab == 0);
        showProgress();
        getPlanList(mPage = 0);

    }

}
