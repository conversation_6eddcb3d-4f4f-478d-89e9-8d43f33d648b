package com.ybmmarket20.activity;

import android.view.View;

import com.github.mzule.activityrouter.annotation.Router;
import com.ybmmarket20.R;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.navigationbar.DefaultNavigationBar;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.utils.RoutersUtils;

import butterknife.OnClick;

/**
 * 常见问题
 */
@Router("personalhelp")
public class PersonalHelpActivity extends BaseActivity {

    @Override
    protected int getContentViewId() {
        return R.layout.activity_personal_help;
    }

    @Override
    protected void initHead() {
        super.initHead();
//        new DefaultNavigationBar.Builder(this).setTitle("常见问题").build();
    }

    @Override
    protected void initData() {
        setTitle("常见问题");
    }

    @OnClick({R.id.ll_account_to_guide, R.id.ll_aptitude_to_guide, R.id.ll_order_problem, R.id.ll_after_sales_problem, R.id.ll_else_problem})
    public void clickTab(View view) {
        switch (view.getId()) {
            case R.id.ll_account_to_guide:
                //账户指引
                RoutersUtils.open("ybmpage://commonh5activity?url=" + getGotoH5Url(AppNetConfig.COMMONPROBLEM_ACCOUNT));
                break;
            case R.id.ll_aptitude_to_guide:
                //资质指引
                RoutersUtils.open("ybmpage://commonh5activity?url=" + getGotoH5Url(AppNetConfig.COMMONPROBLEM_QUALIFICATION));
                break;
            case R.id.ll_order_problem:
                //订单问题
                RoutersUtils.open("ybmpage://commonh5activity?url=" + getGotoH5Url(AppNetConfig.COMMONPROBLEM_ORDER));
                break;
            case R.id.ll_after_sales_problem:
                //售后问题
                RoutersUtils.open("ybmpage://commonh5activity?url=" + getGotoH5Url(AppNetConfig.COMMONPROBLEM_AFTERMARKET));
                break;
            case R.id.ll_else_problem:
                //其他问题
                RoutersUtils.open("ybmpage://commonh5activity?url=" + getGotoH5Url(AppNetConfig.COMMONPROBLEM_OTHER));
                break;
        }
    }

    public String getGotoH5Url(String url) {
        String scUrl = url;
        if (scUrl.startsWith("http:")) {
            scUrl = scUrl.replace("http", "https");
        }
        return scUrl;
    }
}
