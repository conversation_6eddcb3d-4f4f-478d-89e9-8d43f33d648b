package com.ybmmarket20.activity.afterSales.adapter.detail

import android.view.View
import android.widget.EditText
import android.widget.FrameLayout
import android.widget.TextView
import androidx.core.view.isVisible
import androidx.core.widget.addTextChangedListener
import com.chad.library.adapter.base.BaseViewHolder
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.activity.afterSales.adapter.YBMSingleViewAdapter
import com.ybmmarket20.bean.VideoPicPreviewEntity
import com.ybmmarket20.bean.aftersales.RefundInvoiceInfo
import com.ybmmarket20.common.AlertDialogEx
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.common.widget.RoundConstraintLayout
import com.ybmmarket20.common.widget.RoundLinearLayout
import com.ybmmarket20.common.widget.RoundTextView
import com.ybmmarket20.fragments.AddImage3Fragment
import com.ybmmarket20.fragments.UploadSuccessCallback

/**
 * 售后详情发票退回
 */
//撤回售后申请
const val TYPE_WITHDRAW_AFTER_SALES_APPLICATION = 0
//确认发票已退回
const val TYPE_ENSURE_INVOICE_RESULT = 1
//平台介入
const val TYPE_PLATFORM = 2
class AfterSalesDetailWithdrawInvoiceAdapter(
    private val singleData: RefundInvoiceInfo,
    private val canPlatformIn: Boolean=false,
    private val onClickCallback: ((type: Int)->Unit)?
) : YBMSingleViewAdapter<RefundInvoiceInfo>(
    R.layout.item_after_sales_detail_withdraw_invoice,
    singleData
) {
    override fun bindSingleView(holder: YBMBaseHolder, bean: RefundInvoiceInfo) {
        val etLogistics = holder.getView<EditText>(R.id.etLogistics)
        val etExpress = holder.getView<EditText>(R.id.etExpress)
        val rtvWithdrawAfterSalesApplication = holder.getView<RoundTextView>(R.id.rtvWithdrawAfterSalesApplication)
        val rtvEnsureInvoiceReturn = holder.getView<TextView>(R.id.rtvEnsureInvoiceReturn)
        val btnPlatform = holder.getView<TextView>(R.id.btnPlatform)
        val rllTips = holder.getView<RoundLinearLayout>(R.id.rllTips)
        val rclInvoiceReturn = holder.getView<RoundConstraintLayout>(R.id.rclInvoiceReturn)
        val placeHolder = holder.getView<View>(R.id.placeHolder)
        etLogistics.addTextChangedListener { bean.logisticsCompany = it?.toString() }
        etExpress.addTextChangedListener { bean.expressNo = it?.toString() }
        rtvWithdrawAfterSalesApplication.visibility = if (bean.isShowCancelBtn == 1) View.VISIBLE else View.GONE
        if (bean.auditProcessState == 6) {
            rllTips.visibility = View.VISIBLE
            rclInvoiceReturn.visibility = View.VISIBLE
            rtvEnsureInvoiceReturn.visibility = View.VISIBLE
        } else {
            rllTips.visibility = View.GONE
            rclInvoiceReturn.visibility = View.GONE
            rtvEnsureInvoiceReturn.visibility = View.GONE
        }
        placeHolder.visibility = if (bean.isShowCancelBtn != 1 && bean.auditProcessState == 6) {
            View.VISIBLE
        } else if (bean.isShowCancelBtn == 1 && bean.auditProcessState != 6) {
            View.VISIBLE
        } else View.GONE
        rtvWithdrawAfterSalesApplication.setOnClickListener {
            val dialog = AlertDialogEx(mContext)
            dialog
                .setMessage("确定要撤回售后申请吗？")
                .setCancelButton("取消") { _, _ -> dialog.dismiss()}
                .setConfirmButton("确定") {_, _ ->
                    onClickCallback?.invoke(TYPE_WITHDRAW_AFTER_SALES_APPLICATION)
                }.show()
        }
        rtvEnsureInvoiceReturn.setOnClickListener { onClickCallback?.invoke(TYPE_ENSURE_INVOICE_RESULT) }
        btnPlatform.isVisible=canPlatformIn
        btnPlatform.setOnClickListener { onClickCallback?.invoke(TYPE_PLATFORM) }
    }

    override fun onViewAttachedToWindow(holder: BaseViewHolder) {
        val fl = holder.getView<FrameLayout>(R.id.flUploadImageReplace)
        val fm = (mContext as BaseActivity).supportFragmentManager
        val containsFragment = fm.fragments.firstOrNull { it is AddImage3Fragment }
        val fragment = if (containsFragment == null) {
            AddImage3Fragment().apply {
                val arg = AddImage3Fragment.getBundle2Me(3, true, false, true)
                arg.putBoolean("allowe_add", true)
                arg.putCharSequence("hint", "")
                arguments = arg
            }
        } else if (fl.childCount == 0) {
            fm.beginTransaction().remove(containsFragment).commitNowAllowingStateLoss()
            containsFragment as AddImage3Fragment
        } else null

        if (fragment != null) {
            (mContext as BaseActivity).supportFragmentManager.beginTransaction()
                .add(R.id.flUploadImageReplace, fragment).commitNowAllowingStateLoss()
            fragment.setOnUploadSuccessCallback(object : UploadSuccessCallback {
                override fun onUploadSuccess(localPathList: List<VideoPicPreviewEntity>) {
                    singleData.images = localPathList.map { it.v_url }
                }
            })
        }
        super.onViewAttachedToWindow(holder)
    }
}