package com.ybmmarket20.activity

import androidx.activity.viewModels
import com.github.mzule.activityrouter.annotation.Router
import com.ybmmarket20.R
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.common.util.ToastUtils
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.viewmodel.BindWXChatViewModel

/**
 *    author : 朱勇闯
 *    e-mail : <EMAIL>
 *    date   : 2025/6/26 11:16
 *    desc   :
 */
@Router("bindWX","bindWX/:code")
class BindWXActivity : BaseActivity() {
    private val mViewModel: BindWXChatViewModel by viewModels()
    override fun getContentViewId() = R.layout.activity_get_voucher

    override fun initData() {

        var code = intent?.getStringExtra("code") ?: ""
        if (!isLogin()) { //去登陆
            RoutersUtils.open("ybmpage://login/$code")
            finish()
            return
        }
        mViewModel.bindWechatCustomer(code)
        setWechatBindObserver()
    }

    private fun setWechatBindObserver() {
        mViewModel.bindWxChatLiveData.observe(this) {
            if (it != null && it.isSuccess) {
                ToastUtils.showShort("绑定成功")
                RoutersUtils.open("ybmpage://main")
                finish()
            } else {
//                ToastUtils.showShort("绑定微信失败")
                RoutersUtils.open("ybmpage://main")
                finish()
            }
        }
    }

}