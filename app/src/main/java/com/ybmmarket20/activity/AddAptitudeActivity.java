package com.ybmmarket20.activity;

import static com.ybmmarket20.bean.LicenseListBean.License.STATUS_DISQUALIFICATION;
import static com.ybmmarket20.bean.LicenseListBean.License.STATUS_REJECTED;
import static com.ybmmarket20.constant.ConstantData.LICENSE_STATUS_SY_AUDITING;
import static com.ybmmarket20.constant.IntentCanst.RX_BUS_REFRESH_LICENCE_AUDIT_LIST;
import static com.ybmmarket20.constant.IntentCanst.RX_BUS_UPDATE_LICENCEDTAIL;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Intent;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.os.Bundle;
import android.text.InputFilter;
import android.text.SpannableString;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.EditorInfo;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.animation.GlideAnimation;
import com.bumptech.glide.request.target.SimpleTarget;
import com.google.gson.Gson;
import com.luck.picture.lib.PictureSelector;
import com.luck.picture.lib.config.PictureConfig;
import com.luck.picture.lib.config.PictureMimeType;
import com.luck.picture.lib.entity.LocalMedia;
import com.luck.picture.lib.tools.DoubleUtils;
import com.lxj.xpopup.XPopup;
import com.tbruyelle.rxpermissions2.RxPermissions;
import com.ybm.app.bean.NetError;
import com.ybm.app.common.SmartExecutorManager;
import com.ybm.app.utils.PermissionDialogUtil;
import com.ybmmarket20.R;
import com.ybmmarket20.adapter.LicensePicListAdapter;
import com.ybmmarket20.bean.AptitudeBasicInfoExtrasBean;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.EmptyBean;
import com.ybmmarket20.bean.LicenceBean;
import com.ybmmarket20.bean.LicenseUpload;
import com.ybmmarket20.bean.UrlStringBean;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.YBMAppLike;
import com.ybmmarket20.common.eventbus.Event;
import com.ybmmarket20.common.eventbus.EventBusUtil;
import com.ybmmarket20.common.util.ToastUtils;
import com.ybmmarket20.common.widget.RoundTextView;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.utils.AuditStatusSyncUtil;
import com.ybmmarket20.utils.DateTimeUtil;
import com.ybmmarket20.utils.FileUtil;
import com.ybmmarket20.utils.InputFilter.EditUtil;
import com.ybmmarket20.utils.RoutersUtils;
import com.ybmmarket20.utils.UiUtils;
import com.ybmmarket20.view.AptitudeProgressEntry;
import com.ybmmarket20.view.AptitudeProgressView;
import com.ybmmarket20.view.MyScrollView;
import com.ybmmarket20.view.PayTipsPopWindow;
import com.ybmmarket20.view.ShowAptitudeBottomAddImageDialog;
import com.ybmmarket20.view.ShowBigBitmapPopPublishForLongPic;
import com.ybmmarket20.view.UploadInstructionsPopWindow;
import com.ybmmarket20.view.WrapContentLinearLayoutManager;
import com.ybmmarket20.view.picker.PickerManager;

import java.io.File;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.LinkedList;
import java.util.List;

import butterknife.Bind;
import butterknife.OnClick;
import kotlin.Unit;
import kotlin.jvm.functions.Function0;
import kotlin.jvm.functions.Function1;

public class AddAptitudeActivity extends BaseActivity implements LicensePicListAdapter.Listener, MyScrollView.OnScrollListener {


    private static final String EXTRA_MERCHANT_ID = "merchant_id";
    private static final String EXTRA_APTITUDE_ID = "aptitude_id";
    private static final String EXTRA_ORG_ID = "ec_org_id";
    private static final String EXTRA_NECESSARY = "aptitude_necessary";
    private static final String EXTRA_OPTIONAL = "aptitude_optional";
    private static final String EXTRA_STATUS = "aptitude_status";
    private static final String EXTRA_STATUS_NAME = "aptitude_status_name";
    private static final String EXTRA_STATUS_CODE = "aptitude_status_code";
    private static final String LICENSE_AUDITID = "licenseAuditId";
    private static final String EXTRA_STATUS_DRAFT = "is_draft";
    private static final String EXTRA_REMARK = "remark";
    private static final String EXTRA_TEMPREMARK = "tempRemark";
    private static final String EXTRA_TYPE = "customer_type";
    private static final String EXTRA_CODE = "code";
    private static final String EXTRA_FROM = "from";
    private static final String EXTRA_TIME = "time";
    private static final String EXTRA_BASIC_INFO = "basic_info";
    private static final String EXTRA_IS_THREE_STEP = "isThreadStep";
    public static final String APTITUDE_DETAIL_ACTIVITY = "APTITUDE_DETAIL_ACTIVITY";
    public static final String APTITUDE_LIST_ACTIVITY = "APTITUDE_LIST_ACTIVITY";
    public static final int APTITUDE_REQUEST_CODE = 100;
    //开票信息或开户许可证
    public static final String CODE_KPXX = "KPXX";
    //小药药委托书
    public static final String CODE_XYYWT = "XYYWT";
    //其他
    public static final String CODE_QT = "QT";

    private final int REQUEST_SELECT = 100;

    @Bind(R.id.tvId)
    TextView tvId;
    @Bind(R.id.tvRemark)
    TextView tvRemark;
    @Bind(R.id.tv_remark_licence_title)
    TextView tvRemarkHint;
    @Bind(R.id.tvStatus)
    TextView tvStatus;
    @Bind(R.id.tv_up_time)
    TextView tvUpTime;

    //必要资质容器
    @Bind(R.id.ll_necessary)
    LinearLayout llNecessary;

    @Bind(R.id.msv)
    MyScrollView mScrollView;

    @Bind(R.id.edit)
    RoundTextView edit;

    @Bind(R.id.cl_document_status)
    ConstraintLayout mStatusC;

    @Bind(R.id.ll_top_tips)
    LinearLayout llTopTips;

    @Bind(R.id.apv)
    AptitudeProgressView apv;

    protected String mMerchantId;
    private String mAptitudeId = "-1";  //  -1 表示添加资质，其他值表示更新资质
    private String orgId = "";          //  机构编码
    private boolean mIsAddType;         //资质类型：1.添加首营、2.资质变更
    @Deprecated
    private boolean mIsDraft;           //只有草稿（未提交）态才能保存草稿
    protected List<LicenceBean> mNecessaryList;//必填数据
    protected List<LicenceBean> mOptionalList;//非必填的数据
    public String remark;
    public String tempRemark;
    private String customerType;//客户类型


    public EditText etRemark;
    public EditText etSerailNumber;
    private LicenceBean xyy;
    private long validityTimeLong;
    private TextView validityTime;
    @Deprecated
    private String licenseAuditId;

    private String statusName;
    private int statusCode;
    private String code;
    private String from;
    private String time;
    private AptitudeBasicInfoExtrasBean basicInfoExtrasBean;
    private boolean isThreadStep;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setLeft(this::onViewClicked);
    }

    @Override
    protected void initData() {
        mMerchantId = getIntent().getStringExtra(EXTRA_MERCHANT_ID);
        licenseAuditId = getIntent().getStringExtra(LICENSE_AUDITID);
        mAptitudeId = getIntent().getStringExtra(EXTRA_APTITUDE_ID);
        orgId = getIntent().getStringExtra(EXTRA_ORG_ID);
        mIsAddType = getIntent().getBooleanExtra(EXTRA_STATUS, false);
        mIsDraft = getIntent().getBooleanExtra(EXTRA_STATUS_DRAFT, false);
        mNecessaryList = getIntent().getParcelableArrayListExtra(EXTRA_NECESSARY);
        mOptionalList = getIntent().getParcelableArrayListExtra(EXTRA_OPTIONAL);
        remark = getIntent().getStringExtra(EXTRA_REMARK);
        tempRemark = getIntent().getStringExtra(EXTRA_TEMPREMARK);
        customerType = getIntent().getStringExtra(EXTRA_TYPE);
        statusName = getIntent().getStringExtra(EXTRA_STATUS_NAME);
        statusCode = getIntent().getIntExtra(EXTRA_STATUS_CODE, 0);
        code = getIntent().getStringExtra(EXTRA_CODE);
        from = getIntent().getStringExtra(EXTRA_FROM);
        time = getIntent().getStringExtra(EXTRA_TIME);
        basicInfoExtrasBean = (AptitudeBasicInfoExtrasBean) getIntent().getSerializableExtra(EXTRA_BASIC_INFO);
        isThreadStep = getIntent().getBooleanExtra(EXTRA_IS_THREE_STEP, false);
        getExamplePic();
    }

    private String downloadUrl;

    protected void getExamplePic() {
        showProgress();
        RequestParams params = new RequestParams();
        params.put("merchantId", mMerchantId);
        HttpManager.getInstance().post(AppNetConfig.LICENSE_DOWNLOAD_VIEW_EXAMPLES_IMG, params, new BaseResponse<UrlStringBean>() {

            @Override
            public void onFailure(NetError error) {
                dismissProgress();
                renderUi();
            }

            @Override
            public void onSuccess(String content, BaseBean<UrlStringBean> baseBean, UrlStringBean data) {
                dismissProgress();
                if (baseBean != null && baseBean.isSuccess() && data != null) {
                    downloadUrl = data.url;
                }
                renderUi();
            }
        });
    }

    /**
     * 设置进度View
     */
    private void setProgressView() {
        List<AptitudeProgressEntry> entries = new ArrayList<>();
        if (isThreadStep) {
            entries.add(new AptitudeProgressEntry("信息认证"));
            entries.add(new AptitudeProgressEntry("信息确认"));
            entries.add(new AptitudeProgressEntry("上传资质照片"));
        } else {
            entries.add(new AptitudeProgressEntry("填写基本信息"));
            entries.add(new AptitudeProgressEntry("上传资质图片"));
        }
        apv.setData(entries);
        apv.setSelected(entries.size() - 1);
    }

    protected void renderUi() {
        if (APTITUDE_DETAIL_ACTIVITY.equals(from)) {//详情页
            setTitle("资质单据");
        } else {
            if (mIsAddType) {
                setTitle("资质上传");
                edit.setText("提交资质");
            } else {
                setTitle("资质变更");
                edit.setText("提交资质");
            }
        }
        setProgressView();
        mScrollView.setOnScrollListener(this);
        if (!TextUtils.isEmpty(statusName) && !TextUtils.isEmpty(code)) {
            tvStatus.setText(statusName);
            tvStatus.setTextColor(UiUtils.getColorFromAptitudeStatus(statusCode));
            tvId.setText(code);
            tvRemark.setText(remark);
            if (TextUtils.isEmpty(remark)) {//备注为空不显示
                tvRemark.setVisibility(View.GONE);
                tvRemarkHint.setVisibility(View.GONE);
            } else {
                if (statusCode == STATUS_REJECTED || statusCode == STATUS_DISQUALIFICATION) {
                    tvRemark.setVisibility(View.VISIBLE);
                    tvRemarkHint.setVisibility(View.VISIBLE);
                } else {
                    tvRemark.setVisibility(View.GONE);
                    tvRemarkHint.setVisibility(View.GONE);
                }
            }
            tvUpTime.setText(time);
            mStatusC.setVisibility(View.VISIBLE);
        } else {
            mStatusC.setVisibility(View.GONE);
        }
        if (TextUtils.isEmpty(mMerchantId) || mNecessaryList == null) {
            finish();
        }
        //渲染列表
        initItem(llNecessary, mNecessaryList);
        initItem(llNecessary, mOptionalList);
        addRemark();
    }

    @OnClick({R.id.edit, R.id.iv_top_tips_delete})
    public void onViewClicked(View view) {
        // mAptitudeId从上个页面带过来，不为-1就表示存在资质，当前是修改
        switch (view.getId()) {
            case R.id.iv_back:
                onBackPressed();
                break;
            case R.id.edit:
                if (!DoubleUtils.isFastDoubleClick()) {
                    addOrUpdateAptitude("-1".equals(mAptitudeId));
                }
                break;
            case R.id.iv_top_tips_delete://顶部提示点击隐藏
                llTopTips.setVisibility(View.GONE);
                break;
        }
    }

    private int getCount() {
        int submitCount = 0;
        //必须的资质
        if (mNecessaryList != null) {
            for (LicenceBean licenceBean : mNecessaryList) {
                submitCount++;
                List<LicensePicListAdapter.ImageInfo> imageUrlList = licenceBean.adapter.getData();
                if (imageUrlList.size() == 1) {
                    submitCount--;
                }

            }
        }
        return submitCount;
    }

    /**
     * @param isAdd true 新建 false 修改
     */
    private void addOrUpdateAptitude(boolean isAdd) {
        int count = getCount();//已添加的数量
        //只有首营的时候才去检查
        List<LicenseUpload> list = new ArrayList<>();
        if (xyy != null) {
            xyy.xyyEntrusCode = etSerailNumber.getText().toString().trim();
            xyy.xyyEntrusValidateTime = validityTimeLong;
        }
        //必须的资质
        if (mNecessaryList != null) {
            for (LicenceBean licenceBean : mNecessaryList) {
                List<LicensePicListAdapter.ImageInfo> imageUrlList = licenceBean.adapter.getData();
                if (imageUrlList.size() == 1) {
                    //认为该属性没有添加图片
                    if (mIsAddType) {
                        // ToastUtils.showShortSafe("必须的资质均需上传，" + licenceBean.name + "不能为空");
                        if (mNecessaryList != null && mNecessaryList.size() > 0) {
                            //有三项以上必填项未填写，就提示： 请上传您的资质信息；如果是三项以内（包含三项）未填写，就提示：请上传****  （详情未填信息名称展示）
                            if (mNecessaryList.size() - count <= 3) {
                                ToastUtils.showShort("请上传" + licenceBean.name);
                            } else {
                                ToastUtils.showShort("请上传您的资质信息");
                            }
                        }
                        return;
                    } else {
                        continue;
                    }
                }
                LicenseUpload licenseUpload = new LicenseUpload(licenceBean);
                list.add(licenseUpload);
            }
        }
        if (mOptionalList != null) {
            //非必须的资质
            for (LicenceBean licenceBean : mOptionalList) {
                List<LicensePicListAdapter.ImageInfo> imageUrlList = licenceBean.adapter.getData();
                if (imageUrlList.size() > 1 || licenceBean.xyyEntrusCode != null || licenceBean.xyyEntrusValidateTime >= 0) {
                    LicenseUpload licenseUpload = new LicenseUpload(licenceBean);
                    list.add(licenseUpload);
                }
            }
        }
        if (list.isEmpty()) {
            ToastUtils.showShort("请至少提交一张资质");
            return;
        } else {
            for (int i = 0; i < list.size(); i++) {
                if (TextUtils.isEmpty(list.get(i).licenseImgUrls) && TextUtils.isEmpty(list.get(i).xyyEntrusCode) && list.get(i).xyyEntrusValidateTime == 0) {
                    list.remove(i);
                    i--;
                }
            }
            if (list.size() == 0) {
                ToastUtils.showShort("请至少提交一张资质");
                return;
            }

        }
        if (xyy != null && xyy.adapter.getData().size() > 1 && mIsAddType) {
            if (xyy.xyyEntrusValidateTime <= 0) {
                ToastUtils.showShort("请选择小药药委托书有效期!");
                return;
            }
            if (TextUtils.isEmpty(xyy.xyyEntrusCode)) {
                ToastUtils.showShort("请填写小药药委托书编号!");
                return;
            }
        } else if (xyy != null && !TextUtils.isEmpty(xyy.xyyEntrusCode) && mIsAddType) {
            if (xyy.adapter.getData().size() <= 1) {
                ToastUtils.showShort("请上传小药药委托书!");
                return;
            }
            if (xyy.xyyEntrusValidateTime <= 0) {
                ToastUtils.showShort("请选择小药药委托书有效期!");
                return;
            }
        } else if (xyy != null && xyy.xyyEntrusValidateTime > 0 && mIsAddType) {
            if (xyy.adapter.getData().size() <= 1) {
                ToastUtils.showShort("请上传小药药委托书!");
                return;
            }
            if (TextUtils.isEmpty(xyy.xyyEntrusCode)) {
                ToastUtils.showShort("请填写小药药委托书编号!");
                return;
            }
        }
        boolean has = false;
        for (LicenceBean licenceBean : mNecessaryList) {
            //当必填项里面有开户信息 并且为空时 进行提示 ，其他情况不进行提示
            List<LicensePicListAdapter.ImageInfo> imageUrlList = licenceBean.adapter.getData();
            if (CODE_KPXX.equals(licenceBean.categoryCode) && imageUrlList.size() == 1) {
                has = true;
                break;
            }
        }
        if (has && !mIsAddType && "1".equals(from)) {
            ToastUtils.showLong("请上传开户信息和开户许可证");
            return;
        }

        tempRemark = etRemark.getText().toString().trim();
        Gson gson = new Gson();
        String result = gson.toJson(list);
        //1.添加首营，2.资质变更
        String type = mIsAddType ? "1" : "2";
        //String typeParm = isAdd ? "addLicenseAudit" : "updateLicenseAudit";
        String url = isAdd ? AppNetConfig.ADD_LICENSE_AUDIT : AppNetConfig.UPDATE_LICENSE_AUDIT;

        showProgress();
        RequestParams params = new RequestParams();
        params.put("merchantId", mMerchantId);
        params.put("licenseAuditImgListStr", result);
        params.put("remark", tempRemark);
        if (!TextUtils.isEmpty(mAptitudeId) && !"-1".equals(mAptitudeId)) {
            params.put("applicationNumber", mAptitudeId);
        }
        if (!TextUtils.isEmpty(orgId)) {
            params.put("ecOrgCode", orgId);
        }
        if (customerType != null) {
            params.put("customerType", customerType);
        }
        params.put("type", type);
        params.put("audit1Status", "0");
        if (basicInfoExtrasBean != null) {
            params.put("customerName", basicInfoExtrasBean.getCompanyName());
            params.put("deliveryProvinceId", basicInfoExtrasBean.getProvinceId());
            params.put("deliveryCityId", basicInfoExtrasBean.getCityId());
            params.put("deliveryDistrictId", basicInfoExtrasBean.getDistrictId());
            params.put("deliveryStreetId", basicInfoExtrasBean.getStreetId());
            params.put("deliveryAddress", basicInfoExtrasBean.getDetailAddress());
            if (mIsAddType || !isAdd) params.put("invoiceType", basicInfoExtrasBean.invoiceType + "");
        }
        HttpManager.getInstance().post(url, params, new BaseResponse<EmptyBean>() {

            @Override
            public void onFailure(NetError error) {
                dismissProgress();
                ToastUtils.showShort(error.message);
            }

            @Override
            public void onSuccess(String content, BaseBean<EmptyBean> baseBean, EmptyBean data) {
                dismissProgress();
                if (isFinishing() || baseBean == null || !baseBean.isSuccess()) {
                    String msg = "提交失败";
                    if (baseBean != null) {
                        msg = baseBean.msg;
                    }
                    ToastUtils.showShort(msg);
                    return;
                }
                ToastUtils.showLong("提交成功");
                //订阅成功发送event 事件
                boolean isSubscribe = true;
                Event<Boolean> event = new Event<>(RX_BUS_UPDATE_LICENCEDTAIL, isSubscribe);
                EventBusUtil.sendEvent(event);
                boolean mineSelectShop = ((YBMAppLike)getApplication()).mineSelectShop;
                if (mineSelectShop) {
                    AuditStatusSyncUtil.getInstance().updateLicenseStatus(LICENSE_STATUS_SY_AUDITING, null);
                    setResult(RESULT_OK);
                } else {
                    RoutersUtils.open("ybmpage://main/0");
                }
                ((YBMAppLike)getApplication()).mineSelectShop = false;
                finish();
            }
        });

    }

    /**
     * 动态的构建资质页面
     *
     * @param container
     * @param list
     */
    private void initItem(LinearLayout container, List<LicenceBean> list) {
        if (list != null) {
            for (LicenceBean licenceBean : list) {
                if (CODE_QT.equals(licenceBean.categoryCode)) {//其他
                    addViewAndBindData(licenceBean, container, 10);
                } else {
                    addViewAndBindData(licenceBean, container, 3);
                    if (CODE_XYYWT.equals(licenceBean.categoryCode)) {//小药药委托书
                        xyy = licenceBean;
                        //在委托书下方添加委托书有效期和编号
                        addValidity();
                        addSerialNumber();
                    }
                }
            }
        }
    }

    /**
     * 动态添加view 绑定数据
     *
     * @param item
     * @param parent
     * @param maxSize
     */
    private void addViewAndBindData(LicenceBean item, ViewGroup parent, int maxSize) {
        View view = getLayoutInflater().inflate(R.layout.item_licence_item, parent, false);

        // 资质条目名称, 增加示例图片逻辑
        TextView name = (TextView) view.findViewById(R.id.name);
        //必填添加红星
        SpannableStringBuilder nameSSB = new SpannableStringBuilder(item.name);

        //必填添加红星
        if (item.isRequired == 1) {
            Drawable drawable = ContextCompat.getDrawable(this, R.drawable.icon_need_checked);
            drawable.setBounds(0, 0, drawable.getMinimumWidth(), drawable.getMinimumHeight());
            name.setCompoundDrawables(drawable, null, null, null);
        }
        name.setText(nameSSB);

        RecyclerView recyclerView = view.findViewById(R.id.recyclerView);
        recyclerView.setVisibility(View.VISIBLE);
        WrapContentLinearLayoutManager linearLayoutManager = new WrapContentLinearLayoutManager(this);
        linearLayoutManager.setOrientation(WrapContentLinearLayoutManager.HORIZONTAL);
        recyclerView.setLayoutManager(linearLayoutManager);
        List<LicensePicListAdapter.ImageInfo> imageUrlList = item.fetchImageUrlList();
        if (item.fetchImageUrlList().size() < maxSize) {
            LicensePicListAdapter.ImageInfo imageInfo = new LicensePicListAdapter.ImageInfo();
            imageInfo.localPath = LicensePicListAdapter.EDIT_FLAG;
            imageUrlList.add(imageInfo);
        }
        LicensePicListAdapter adapter = new LicensePicListAdapter(R.layout.item_image, imageUrlList, true, item.categoryCode,linearLayoutManager);
        adapter.setItemStatus(item.savedStatus);
        adapter.setMaxSize(maxSize);
        adapter.setListener(this);
        item.adapter = adapter;
        recyclerView.setAdapter(adapter);
        parent.addView(view);
    }

    protected void addRemark() {
        ViewGroup view = (ViewGroup) getLayoutInflater().inflate(R.layout.item_licence_item, llNecessary, false);
        RelativeLayout rl = view.findViewById(R.id.rl);
        rl.removeViewAt(0);
        TextView name = (TextView) view.findViewById(R.id.name);
        name.setText("备注");

        etRemark = (EditText) view.findViewById(R.id.et);
        etRemark.setHint("200字以内");
        EditUtil.setEditTextInhibitInputIllegaCharacter(etRemark, 200);
        etRemark.setVisibility(View.VISIBLE);
        etRemark.setText(tempRemark);
        llNecessary.addView(view);
    }

    private void addValidity() {
        ViewGroup view = (ViewGroup) getLayoutInflater().inflate(R.layout.layout_aptitude_detail_item_view, llNecessary, false);
        RelativeLayout rl = view.findViewById(R.id.rl_validity);
        rl.setVisibility(View.VISIBLE);
//        TextView name = (TextView) view.findViewById(R.id.tv_validity_date_hint);
//        name.setText("小药药委托书有效期");
        validityTime = view.findViewById(R.id.tv_validity_date);
        validityTime.setVisibility(View.VISIBLE);
        if (xyy.xyyEntrusValidateTime > 0) {
            validityTime.setTextColor(UiUtils.getColor(R.color.text_color_cancel_button));
            validityTime.setText(DateTimeUtil.getDate(xyy.xyyEntrusValidateTime));
            //validityTime.setBackground(null);
            validityTimeLong = xyy.xyyEntrusValidateTime;
        }
        validityTime.setOnClickListener(v -> {
            //显示时间选择器
            showTimeSheetDialog();
        });
        llNecessary.addView(view);
    }

    public void showTimeSheetDialog() {
        Calendar startDate = Calendar.getInstance();
        Calendar endDate = Calendar.getInstance();
        endDate.add(Calendar.YEAR, 2);
        boolean[] type = new boolean[]{true, true, true, false, false, false};
        PickerManager.showDateSelectPicker(getMySelf(), null, startDate, endDate, type, (date, dataStr) -> {
            validityTimeLong = date.getTime();
            validityTime.setText(DateTimeUtil.getDate(validityTimeLong));
            validityTime.setTextColor(UiUtils.getColor(R.color.text_color_cancel_button));
        });
    }

    private void addSerialNumber() {
        ViewGroup view = (ViewGroup) getLayoutInflater().inflate(R.layout.layout_aptitude_detail_item_view, llNecessary, false);
        RelativeLayout rl = view.findViewById(R.id.rl_no);
        rl.setVisibility(View.VISIBLE);
//        rl.removeViewAt(0);
//        TextView name = (TextView) view.findViewById(R.id.tv_no_hint);
//        name.setText("小药药委托书编号");
        etSerailNumber = (EditText) view.findViewById(R.id.et_no);
        etSerailNumber.setVisibility(View.VISIBLE);
        etSerailNumber.setHint("请填写");
        etSerailNumber.setFilters(new InputFilter[]{new InputFilter.LengthFilter(20), new InputFilter.AllCaps() {
            @Override
            public CharSequence filter(CharSequence source, int start, int end, Spanned dest, int dstart, int dend) {
                for (int i = start; i < end; i++) {
                    int charInt = source.charAt(i);
                    if (!((charInt >= 48 && charInt <= 57) || (charInt >= 65 && charInt <= 90) || (charInt >= 97 && charInt <= 122))) {
                        return "";
                    }
                }
                for (int i = start; i < end; i++) {
                    if (Character.isLowerCase(source.charAt(i))) {
                        char[] v = new char[end - start];
                        TextUtils.getChars(source, start, end, v, 0);
                        String s = new String(v).toUpperCase();

                        if (source instanceof Spanned) {
                            SpannableString sp = new SpannableString(s);
                            TextUtils.copySpansFrom((Spanned) source, start, end, null, sp, 0);
                            return sp;
                        } else {
                            return s;
                        }
                    }
                }
                return null;
            }
        }});
//        etSerailNumber.setTextSize(14f);
//        etSerailNumber.setGravity(Gravity.LEFT | Gravity.CENTER_VERTICAL);
        etSerailNumber.setInputType(EditorInfo.TYPE_TEXT_FLAG_CAP_CHARACTERS | EditorInfo.TYPE_NUMBER_FLAG_SIGNED);
        if (!TextUtils.isEmpty(xyy.xyyEntrusCode)) {
            String trim = xyy.xyyEntrusCode.trim();
            etSerailNumber.setText(trim);
        }
        llNecessary.addView(view);
    }

    public static void startActivity(Activity context, String customerType, String merchantId, String aptitudeId, String orgId, boolean isAdd, boolean isDraft,
                                     ArrayList<LicenceBean> necessary, ArrayList<LicenceBean> optional,
                                     String remark, String tempRemark, String code, String status, int statusCode, String licenseAuditId,
                                     String time, String from, AptitudeBasicInfoExtrasBean basicInfoExtrasBean, boolean isThreadStep) {
        Intent intent = new Intent(context, AddAptitudeActivity.class);
        intent.putExtra(AddAptitudeActivity.EXTRA_MERCHANT_ID, merchantId);
        intent.putExtra(AddAptitudeActivity.EXTRA_APTITUDE_ID, aptitudeId);
        intent.putExtra(AddAptitudeActivity.EXTRA_ORG_ID, orgId);
        intent.putExtra(AddAptitudeActivity.EXTRA_STATUS, isAdd);
        intent.putExtra(AddAptitudeActivity.EXTRA_REMARK, remark);
        intent.putExtra(AddAptitudeActivity.EXTRA_TEMPREMARK, tempRemark);
        intent.putExtra(AddAptitudeActivity.EXTRA_STATUS_DRAFT, isDraft);
        intent.putExtra(AddAptitudeActivity.EXTRA_TYPE, customerType);
        intent.putExtra(AddAptitudeActivity.EXTRA_CODE, code);
        intent.putExtra(AddAptitudeActivity.EXTRA_STATUS_NAME, status);
        intent.putExtra(AddAptitudeActivity.EXTRA_STATUS_CODE, statusCode);
        intent.putExtra(AddAptitudeActivity.EXTRA_TIME, time);
        intent.putExtra(AddAptitudeActivity.EXTRA_FROM, from);
        intent.putExtra(AddAptitudeActivity.LICENSE_AUDITID, licenseAuditId);
        intent.putParcelableArrayListExtra(AddAptitudeActivity.EXTRA_NECESSARY, necessary);
        intent.putParcelableArrayListExtra(AddAptitudeActivity.EXTRA_OPTIONAL, optional);
        intent.putExtra(EXTRA_BASIC_INFO, basicInfoExtrasBean);
        intent.putExtra(EXTRA_IS_THREE_STEP, isThreadStep);
        context.startActivityForResult(intent, APTITUDE_REQUEST_CODE);
    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_add_aptitude;
    }
    public String getRemark( String code) {
        ArrayList<LicenceBean> licenceBeans = new ArrayList<>();
        if ( mNecessaryList== null&& mOptionalList== null) {
            return "";
        }
        licenceBeans.addAll(mNecessaryList);
        licenceBeans.addAll(mOptionalList);
        for (LicenceBean item : licenceBeans) {
            if (item.categoryCode.equals(code)) {
                return item.remark.isEmpty() ?  "" : item.remark;
            }
        }
        return "";
    }

    public String getImageUrl(String code) {
        ArrayList<LicenceBean> licenceBeans = new ArrayList<>();
        if ( mNecessaryList== null&& mOptionalList== null) {
            return "";
        }
        licenceBeans.addAll(mNecessaryList);
        licenceBeans.addAll(mOptionalList);
        for (LicenceBean item : licenceBeans) {
            if (item.categoryCode.equals(code)) {
                return item.templateUrl.isEmpty() ?  "" : item.templateUrl;
            }
        }
        return "";
    }

    @Override
    public void addImage(LicensePicListAdapter adapter,String typeStr) {
        switch (typeStr){
            //营业执照
            case "YYZZ":
            //药品经营许可证
            case "YPJY":
            //授权委托书
            case "FRSQ":
            //被委托人身份证复印件(正反两面)
            case "WTRZ":
            //开票信息和开户许可证
            case "KPXX":
            //医疗机构执业许可证
            case "ZYXK": {
                UploadInstructionsPopWindow pop = new UploadInstructionsPopWindow(
                        this, getImageUrl(typeStr),getRemark(typeStr), new Function0<Unit>() {
                    @Override
                    public Unit invoke() {
                        checkPermissions(adapter, 1);
                        return null;
                    }
                }, new Function0<Unit>() {
                    @Override
                    public Unit invoke() {
                        checkPermissions(adapter, 2);
                        return null;
                    }
                }, new Function1<Integer, Unit>() {
                    @Override
                    public Unit invoke(Integer integer) {
                        new ShowBigBitmapPopPublishForLongPic(integer).show(tvId);
                        return null;
                    }
                });
                new XPopup.Builder(this)
                        .dismissOnTouchOutside(false)
                        .dismissOnBackPressed(false)
                        .asCustom(pop)
                        .show();
                break;
            }
            default:{
                checkPermissions(adapter,0);
                break;
            }
        }
    }

    //type=0 原先的逻辑弹窗选择  1直接进相册 2 打开相机
    private void checkPermissions(LicensePicListAdapter adapter,int type) {
        RxPermissions rxPermissions = new RxPermissions(this);
        if (rxPermissions.isGranted(android.Manifest.permission.READ_EXTERNAL_STORAGE)
                && rxPermissions.isGranted(android.Manifest.permission.WRITE_EXTERNAL_STORAGE)
                && rxPermissions.isGranted(android.Manifest.permission.CAMERA)) {
            getRootPermissions(adapter,type);
        } else {
            PermissionDialogUtil.showPermissionInfoDialog(this,
                    "药帮忙App需要申请存储权限和相机权限，用于拍照并存储照片",
                    () -> getRootPermissions(adapter,type));
        }
    }
    private LicensePicListAdapter currentPicListAdapter;

    /**
     * 获取6.0读取文件的权限  //type=0 原先的逻辑弹窗选择  1直接进相册 2 打开相机
     */
    @SuppressLint("CheckResult")
    private void getRootPermissions(final LicensePicListAdapter adapter,int type) {
        RxPermissions rxPermissions = new RxPermissions(this); // where this is an Activity instance
        rxPermissions.request(
                android.Manifest.permission.READ_EXTERNAL_STORAGE,
                android.Manifest.permission.WRITE_EXTERNAL_STORAGE,
                android.Manifest.permission.CAMERA
        ).subscribe(granted -> {
            if (granted) { // 在android 6.0之前会默认返回true
                currentPicListAdapter = adapter;
                if (type == 1) {
                    photoGallery(adapter);
                } else if (type == 2) {
                    takingPictures(adapter);
                } else {
                    selectPics(adapter);
                }
            } else {
                // 未获取权限
                Toast.makeText(AddAptitudeActivity.this, "您没有授权该权限，请在设置中打开授权", Toast.LENGTH_LONG).show();
            }
        }, throwable -> {

        });

    }
private void photoGallery(LicensePicListAdapter adapter){
    PictureSelector.create(getMySelf())
            .openGallery(PictureMimeType.ofImage())
            .maxSelectNum(adapter == null ? 3 : adapter.getAllowAddSize())
            .minSelectNum(1)
            .imageSpanCount(4)
            .compress(true)
            .selectionMode(PictureConfig.MULTIPLE)
            .forResult(PictureConfig.CHOOSE_REQUEST);
}
private void takingPictures(LicensePicListAdapter adapter) {
    PictureSelector.create(getMySelf())
            .openCamera(PictureMimeType.ofImage())
            .maxSelectNum(adapter == null ? 3 : adapter.getAllowAddSize())
            .minSelectNum(1)
            .compress(true)
            .forResult(PictureConfig.CHOOSE_REQUEST);
}
    public void selectPics(LicensePicListAdapter adapter) {
        ShowAptitudeBottomAddImageDialog mDialogLayout;
        mDialogLayout = new ShowAptitudeBottomAddImageDialog(getMySelf());
        mDialogLayout.setOnCancelClickListener(v -> mDialogLayout.dismiss());
        mDialogLayout.setOnPhotoGalleryClickListener(v -> {
            // 调用图库
            photoGallery(adapter);
            mDialogLayout.dismiss();
        });
        mDialogLayout.setOnTakingPicturesClickListener(v -> {
            //调用系统相机程序
            takingPictures(adapter);
            mDialogLayout.dismiss();
        });
        mDialogLayout.show();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == RESULT_OK && requestCode == PictureConfig.CHOOSE_REQUEST) {
            List<LocalMedia> images = PictureSelector.obtainMultipleResult(data);
            if (images == null || images.isEmpty()) {
                ToastUtils.showShort("未找到图片");
                return;
            }
            ArrayList<LocalMedia> selectList = new ArrayList<>(3);
            selectList.addAll(images);
            ArrayList<LicensePicListAdapter.ImageInfo> imageInfos = new ArrayList<>();
            for (LocalMedia localMedia : selectList) {
                LicensePicListAdapter.ImageInfo imageInfo = new LicensePicListAdapter.ImageInfo();
                imageInfo.localPath = localMedia.getCompressPath();
                imageInfos.add(imageInfo);
            }
            //currentPicListAdapter 空指针
            try {
                currentPicListAdapter.addSelectPic(imageInfos);
                llNecessary.post(() -> currentPicListAdapter.linearLayoutManager.scrollToPosition(currentPicListAdapter.getItemCount() - 1));
                uploadImage(imageInfos);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public void uploadImage(List<LicensePicListAdapter.ImageInfo> imageInfoList) {
        for (LicensePicListAdapter.ImageInfo info : imageInfoList) {
            File file = new File(info.localPath);
            if (!file.exists()) {
                ToastUtils.showShort("图片未找到：" + info.localPath, Toast.LENGTH_LONG);
//                dismissProgress();
                return;
            }
        }
//        showProgress("资质上传中……");
        showProgress(false);
        LinkedList<LicensePicListAdapter.ImageInfo> linkedList = new LinkedList<>(imageInfoList);
        uploadCircle(linkedList);
    }

    public void uploadCircle(final LinkedList<LicensePicListAdapter.ImageInfo> linkedList) {
        if (linkedList.isEmpty()) {
            dismissProgress();
            ToastUtils.showShort("上传成功", Toast.LENGTH_LONG);
            return;
        }
        final LicensePicListAdapter.ImageInfo s1 = linkedList.removeFirst();
        File file = new File(s1.localPath);
        if (file == null || !file.exists()) {
            ToastUtils.showShort("上传文件不存在");
            return;
        }
        // showProgress();
        RequestParams params = new RequestParams();
        if (!TextUtils.isEmpty(mMerchantId)) {
            params.put("merchantId", mMerchantId);
        }
        params.put("file", file);
        HttpManager.getInstance().post(AppNetConfig.LICENSE_AUDIT_UPLOADIMG, params, new BaseResponse<List<String>>() {

            @Override
            public void onSuccess(String content, BaseBean<List<String>> data, List<String> bean) {
                // dismissProgress();
                if (bean != null && bean.size() > 0) {
                    //拼上上传的图片全路径 /ybm/license/**********/334dd569-5716-45d4-999d-46833ac9633c.jpeg
                    s1.newPath = AppNetConfig.getCDNHost() + bean.get(0);
                    uploadCircle(linkedList);
                    //需要保持上传成功服务器返回的地址
                } else {
                    dismissProgress();
                    ToastUtils.showShort("上传失败");
                    currentPicListAdapter.getData().clear();
                    currentPicListAdapter.notifyDataSetChanged();
                    currentPicListAdapter.showAddView();
                }
            }

            @Override
            public void onFailure(NetError error) {
                dismissProgress();
                ToastUtils.showShort("上传失败");
                currentPicListAdapter.getData().clear();
                currentPicListAdapter.notifyDataSetChanged();
                currentPicListAdapter.showAddView();
            }
        });

    }

    private boolean isDownload;

    private void download() {
        if (isDownload) {
            ToastUtils.showLong("正在下载示例图片中，图片比较大请耐心等候");
            return;
        }
        if (!TextUtils.isEmpty(downloadUrl)) {
            isDownload = true;
            Glide.with(getMySelf())
                    .load(AppNetConfig.getCDNHost() + downloadUrl)
                    .asBitmap()
                    .toBytes()
                    .into(new SimpleTarget<byte[]>() {
                        @Override
                        public void onResourceReady(byte[] bytes, GlideAnimation<? super byte[]> glideAnimation) {
                            // 下载成功回调函数
                            // 数据处理方法，保存bytes到文件
                            String file = FileUtil.getAptitudeExamplePicFilePath();
                            SmartExecutorManager.getInstance().execute(new Runnable() {
                                @Override
                                public void run() {
                                    FileUtil.copy(file, bytes);
                                }
                            });
                            // 最后通知图库更新
                            sendBroadcast(new Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE,
                                    Uri.parse("file://" + file)));
                            ToastUtils.showLong("下载成功，已保存到本地相册");
                            isDownload = false;
                        }

                        @Override
                        public void onLoadFailed(Exception e, Drawable errorDrawable) {
                            // 下载失败回调
                            ToastUtils.showLong("下载失败");
                            isDownload = false;
                        }
                    });
        } else {
            ToastUtils.showLong("示例图片下载地址不存在");
            isDownload = false;
        }
    }

    @Override
    public void onScrollChanged(int x, int y) {
//        View childAt = mScrollView.getChildAt(0);
//        int childHeight = childAt.getMeasuredHeight();//获取子控件高度
//        int height = mScrollView.getHeight();//获取Scrollview的控件高度
//        if(y+height >= childHeight){//判断条件 当子控件高度=Scrollview的控件高度+x的时候控件到达底部
//            edit.setVisibility(View.VISIBLE);
//        }
    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
        if (APTITUDE_LIST_ACTIVITY.equals(from)) {//资质管理页面
            //刷新资质变更列表
            Event<Boolean> event = new Event<>(RX_BUS_REFRESH_LICENCE_AUDIT_LIST, true);
            EventBusUtil.sendEvent(event);
        }
    }
}
