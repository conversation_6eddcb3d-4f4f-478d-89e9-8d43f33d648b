package com.ybmmarket20.activity;

import androidx.recyclerview.widget.LinearLayoutManager;
import android.view.View;
import android.widget.TextView;

import com.github.mzule.activityrouter.annotation.Router;
import com.ybm.app.bean.NetError;
import com.ybm.app.view.CommonRecyclerView;
import com.ybmmarket20.R;
import com.ybmmarket20.adapter.TransactionDetailsAdapter;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.TransactionDetailsBean;
import com.ybmmarket20.bean.TransactionDetailsChildrenBean;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.widget.RoundRelativeLayout;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.utils.SpUtil;
import com.ybmmarket20.utils.UiUtils;

import java.util.ArrayList;
import java.util.List;

import butterknife.Bind;

/**
 * 小药白条-交易明细
 */
@Router({"transactiondetails", "transactiondetails/:financeCode"})
public class TransactionDetailsActivity extends BaseActivity {

    @Bind(R.id.rv_data)
    CommonRecyclerView rvData;
    @Bind(R.id.head_bg)
    RoundRelativeLayout headBg;
    @Bind(R.id.tv_head_time)
    TextView tvHeadTime;
    @Bind(R.id.tv_head_money)
    TextView tvHeadMoney;

    private boolean isShow = false;
    private boolean showHead = true;
    private int mCurrentPosition = 0;
    private String financeCode;

    protected TransactionDetailsAdapter adapter;
    private List<TransactionDetailsChildrenBean> beans = new ArrayList<>();

    @Override
    protected int getContentViewId() {
        return R.layout.activity_transaction_details;
    }

    @Override
    protected void initData() {
        setTitle("交易明细");

        financeCode = getIntent().getStringExtra("financeCode");

        adapter = new TransactionDetailsAdapter(beans);
        adapter.setEnableLoadMore(false);
        rvData.setRefreshEnable(true);
        rvData.setAdapter(adapter);
        rvData.setEmptyView(R.layout.layout_empty_view, R.drawable.icon_empty, "暂无交易明细");
        rvData.setListener(new CommonRecyclerView.Listener() {
            @Override
            public void onRefresh() {
                getData();
            }

            @Override
            public void onLoadMore() {

            }
        });

        initListener();
    }

    private void initListener() {

        rvData.setOnScrollListener(new CommonRecyclerView.OnScrollListener() {
            LinearLayoutManager linearLayoutManager = (LinearLayoutManager) rvData.getLayoutManager();
            int mSuspensionHeight = UiUtils.dp2px(30);

            @Override
            public void onScrollChanged(int x, int y) {

            }

            @Override
            public void onScrollRollingDistance(int y, int dy) {

                if (beans != null && beans.size() > 0) {
                    mSuspensionHeight = headBg.getHeight();

                    int firstVisPos = linearLayoutManager.findFirstVisibleItemPosition();

                    TransactionDetailsChildrenBean firstVisibleItem = beans.get(firstVisPos);
                    TransactionDetailsChildrenBean nextItem = beans.get(firstVisPos + 1);
                    View nextView = linearLayoutManager.findViewByPosition(firstVisPos + 1);

                    if (y > 0) {
                        if (!isShow) {
                            headBg.setAlpha(1);
                            isShow = true;
                        }
                    } else {
                        if (isShow) {
                            headBg.setAlpha(0);
                            isShow = false;
                        }
                    }

                    if (dy > 0) {

                        if (nextItem.getItemType() == TransactionDetailsChildrenBean.ITEMTYPE_HEAD) {

                            if (nextView.getTop() <= mSuspensionHeight) {
                                //被顶掉的效果
                                headBg.setY(-(mSuspensionHeight - nextView.getTop()));
                            } else {
                                headBg.setY(0);
                            }
                        }

                        //判断是否需要更新悬浮条
                        if (mCurrentPosition != firstVisPos && firstVisibleItem.getItemType() == TransactionDetailsChildrenBean.ITEMTYPE_HEAD) {
                            mCurrentPosition = firstVisPos;
                            //更新悬浮条
                            updateSuspensionBar();
                            headBg.setY(0);
                        }
                    } else {

                        if (nextItem.getItemType() == TransactionDetailsChildrenBean.ITEMTYPE_HEAD) {
                            mCurrentPosition = firstVisibleItem.getItemType() == TransactionDetailsChildrenBean.ITEMTYPE_HEAD ? firstVisPos : firstVisibleItem.getParentPostPos();
                            updateSuspensionBar();

                            if (nextView.getTop() <= mSuspensionHeight) {
                                //被顶掉的效果
                                headBg.setY(-(mSuspensionHeight - nextView.getTop()));
                            } else {
                                headBg.setY(0);
                            }
                        }
                    }
                }
            }

            @Override
            public void onScrollState(int i) {

            }

        });

    }

    /*
     * head
     * */
    private void updateSuspensionBar() {
        if (rvData == null) {
            return;
        }
        if (beans != null && beans.size() > 0) {
            if (beans.size() <= mCurrentPosition) {
                mCurrentPosition = beans.size() - 1;
            }
            TransactionDetailsChildrenBean cartItemBean = beans.get(mCurrentPosition);
            String amount = "共" + cartItemBean.getSize() + "笔，合计：-" + UiUtils.transform(cartItemBean.getTotalAmount());
            tvHeadMoney.setText(amount);
            tvHeadTime.setText(cartItemBean.getDate());
        } else {
            headBg.setVisibility(View.GONE);
        }

    }

    /*
     * 请求接口
     * */
    private void getData() {

        HttpManager.getInstance().post(AppNetConfig.FINANCE_GFBT_GETIOUSTRANSACTIONDETAILS, getRefreshParams(), new BaseResponse<List<TransactionDetailsBean>>() {
            @Override
            public void onSuccess(String content, BaseBean<List<TransactionDetailsBean>> obj, List<TransactionDetailsBean> data) {
                completion();
                if (obj != null && obj.isSuccess() && data != null) {
                    setData(data);
                } else {
                    adapter.setNewData(beans);
                }
            }

            @Override
            public void onFailure(NetError error) {
                completion();
                if (rvData != null) {
                    adapter.setNewData(beans);
                }
            }
        });

    }

    private void completion() {
        if (rvData != null) {
            rvData.setRefreshing(false);
        }
    }

    /*
     * 设置数据
     * */
    private void setData(List<TransactionDetailsBean> data) {
        if (rvData == null) {
            return;
        }
        beans = getListData(data);
        adapter.setNewData(beans);
        //更新悬浮条
        boolean isShow = isShowHead(beans);
        if (isShow && showHead) {
            headBg.setAlpha(0);
            showHead = false;
        }
        headBg.setVisibility(isShow ? View.VISIBLE : View.GONE);
        updateSuspensionBar();
    }

    private List<TransactionDetailsChildrenBean> getListData(List<TransactionDetailsBean> data) {
        if (beans == null) {
            beans = new ArrayList<>();
        }
        List<TransactionDetailsChildrenBean> beans = new ArrayList<>();

        if (data != null && data.size() > 0) {

            for (TransactionDetailsBean detailsBean : data) {
                if (detailsBean == null) {
                    continue;
                }
                addProduct(detailsBean, beans);
            }
        }
        return beans;
    }


    private void addProduct(TransactionDetailsBean listBean, List<TransactionDetailsChildrenBean> beans) {
        /*-head-*/
        TransactionDetailsChildrenBean bean = new TransactionDetailsChildrenBean();
        bean.setItemType(TransactionDetailsChildrenBean.ITEMTYPE_HEAD);
        bean.setDate(listBean.getDate());
        bean.setSize(listBean.getSize());
        bean.setTotalAmount(listBean.getTotalAmount());
        beans.add(bean);
        List<TransactionDetailsChildrenBean> childrenBeanList = listBean.getDetails();
        if (childrenBeanList != null && childrenBeanList.size() > 0) {

            for (TransactionDetailsChildrenBean childrenBean : childrenBeanList) {
                if (childrenBean == null) {
                    continue;
                }
                addChildren(childrenBean, beans);
            }

        }
    }

    private void addChildren(TransactionDetailsChildrenBean childrenBean, List<TransactionDetailsChildrenBean> beans) {
        /*-content-*/
        childrenBean.setItemType(TransactionDetailsChildrenBean.ITEMTYPE_CONTENT);
        beans.add(childrenBean);
    }

    private boolean isShowHead(List<TransactionDetailsChildrenBean> beans) {
        int index = 0;
        int parentPostPos = index;
        boolean isShow = false;
        if (beans != null && beans.size() > 0) {
            for (TransactionDetailsChildrenBean itemBean : beans) {
                if (itemBean.getItemType() == TransactionDetailsChildrenBean.ITEMTYPE_HEAD) {
                    itemBean.setParentPostPos(index);
                    parentPostPos = index;
                    isShow = true;
                } else {
                    itemBean.setParentPostPos(parentPostPos);
                }
                index++;
            }
        }
        return isShow;
    }

    /**
     * 参数信息
     */
    private RequestParams getRefreshParams() {
        String merchantId = SpUtil.getMerchantid();
        RequestParams params = new RequestParams();
        params.put("merchantId", merchantId);
        params.put("financeCode", financeCode);//金融产品编码
        return params;
    }

}
