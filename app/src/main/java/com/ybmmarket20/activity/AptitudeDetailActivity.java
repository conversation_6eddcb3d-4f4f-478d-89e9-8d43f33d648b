package com.ybmmarket20.activity;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.os.Bundle;

import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.request.animation.GlideAnimation;
import com.bumptech.glide.request.target.SimpleTarget;
import com.github.mzule.activityrouter.annotation.Router;
import com.luck.picture.lib.tools.DoubleUtils;
import com.tbruyelle.rxpermissions2.RxPermissions;
import com.ybm.app.bean.NetError;
import com.ybm.app.common.SmartExecutorManager;
import com.ybm.app.utils.PermissionDialogUtil;
import com.ybm.app.view.WrapLinearLayoutManager;
import com.ybmmarket20.R;
import com.ybmmarket20.adapter.LicensePicListAdapter;
import com.ybmmarket20.bean.*;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.eventbus.Event;
import com.ybmmarket20.common.eventbus.EventBusUtil;
import com.ybmmarket20.common.util.ToastUtils;
import com.ybmmarket20.common.widget.RoundTextView;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.utils.DateTimeUtil;
import com.ybmmarket20.utils.FileUtil;
import com.ybmmarket20.utils.ImageLoader;
import com.ybmmarket20.utils.RoutersUtils;
import com.ybmmarket20.utils.UiUtils;
import com.ybmmarket20.view.ShowBigBitmapPopPublishForLongPic;
import com.ybmmarket20.view.WrapContentLinearLayoutManager;

import java.util.ArrayList;
import java.util.HashMap;

import butterknife.Bind;

import static com.ybmmarket20.activity.AddAptitudeActivity.APTITUDE_DETAIL_ACTIVITY;
import static com.ybmmarket20.activity.AddAptitudeActivity.CODE_KPXX;
import static com.ybmmarket20.activity.AddAptitudeActivity.CODE_XYYWT;
import static com.ybmmarket20.constant.IntentCanst.RX_BUS_REFRESH_LICENCE_AUDIT_LIST;
import static com.ybmmarket20.constant.IntentCanst.RX_BUS_UPDATE_LICENCEDTAIL;

/**
 * 资质详情
 */
@Router({"aptitudedetail", "aptitudedetail/:phone", "aptitudedetail/:merchantId/:licenseAuditId/:type"})
public class AptitudeDetailActivity extends BaseActivity implements View.OnClickListener {
    public static final int APTITUDE_DETAIL_REQUEST_CODE = 1001;
    public static String EDIT_STR = "0";
    @Bind(R.id.tvId)
    TextView tvId;
    @Bind(R.id.tv_org_name)
    TextView tvOrgName;
    @Bind(R.id.tvRemark)
    TextView tvRemark;
    @Bind(R.id.tv_remark_licence_title)
    TextView tvRemarkHint;
    @Bind(R.id.tv_up_time)
    TextView tvUpTime;

    @Bind(R.id.tvStatus)
    TextView tvStatus;

    //必要资质容器
    @Bind(R.id.ll_necessary)
    LinearLayout llNecessary;


    @Bind(R.id.edit)
    RoundTextView edit;

    @Bind(R.id.cl_document_status)
    ConstraintLayout mStatusC;

    //protected String phone;
    private String merchantId;
    private String licenseAuditId;//单据编号applicationNumber
    private String type;//1首营2变更

    private LicenceDetailBean licenceDetailBean;
    private String tempRemark;

    @Override
    protected void initData() {
        setTitle("资质单据");
        setRigthText(this, "审批日志");
        getRigthText().setTextColor(UiUtils.getColor(R.color.color_theme_base_color));

        edit.setOnClickListener(this);
        merchantId = getIntent().getStringExtra("merchantId");
        licenseAuditId = getIntent().getStringExtra("licenseAuditId");
        type = getIntent().getStringExtra("type");
        getExamplePic();
        getAptitudeInfo();
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setLeft(this);
    }

    public void getAptitudeInfo() {
        showProgress();
        RequestParams params = new RequestParams();
        params.put("merchantId", merchantId);
        params.put("applicationNumber", licenseAuditId);
        params.put("type", type);
        HttpManager.getInstance().post(AppNetConfig.QUERY_LICENSE_AUDIT_DETAIL, params, new BaseResponse<LicenceDetailBean>() {

            @Override
            public void onFailure(NetError error) {
                dismissProgress();
            }

            @Override
            public void onSuccess(String content, BaseBean<LicenceDetailBean> baseBean, LicenceDetailBean data) {
                dismissProgress();
                if (baseBean != null && baseBean.isSuccess() && data != null && data.licenseAudit != null) {
                    licenceDetailBean = data;
                    initViewWithData(data);
                    if (data.licenseAudit != null) {
                        // merchantId = data.licenseAudit.merchantId;
                        if (data.isEdit.equals(EDIT_STR)) {
                            edit.setVisibility(View.VISIBLE);
                        }
                    }
                } else {
//                    ToastUtils.showLong("页面解析数据异常");
//                    finish();
                }
            }
        });
    }

    private void initViewWithData(LicenceDetailBean bean) {
        tvId.setText(bean.licenseAudit.applicationNumber);
        tvOrgName.setText(bean.licenseAudit.orgName);
        tvStatus.setText(bean.licenseAudit.auditStatusName);
        tvUpTime.setText(DateTimeUtil.getLogDateTime(bean.licenseAudit.createTime));
        tvStatus.setTextColor(UiUtils.getColorFromAptitudeStatus(bean.licenseAudit.auditStatus));
        mStatusC.setVisibility(View.VISIBLE);
        if (bean.necessaryLicenceList.size() != 0) {
            llNecessary.setVisibility(View.VISIBLE);
            for (LicenceDetailBean.Item item : bean.necessaryLicenceList) {
                addViewAndBindData(item, llNecessary, true);
            }

        }
        tempRemark = licenceDetailBean.tempRemark;
        if (bean.optionalLicenceList.size() != 0 || !TextUtils.isEmpty(tempRemark)) {
            llNecessary.setVisibility(View.VISIBLE);
            for (LicenceDetailBean.Item item : bean.optionalLicenceList) {
                addViewAndBindData(item, llNecessary, false);
            }

            if (!TextUtils.isEmpty(tempRemark)) {
                ViewGroup view = (ViewGroup) getLayoutInflater().inflate(R.layout.item_licence_item, llNecessary, false);
                RelativeLayout rl = view.findViewById(R.id.rl);
                //房子commonrecycleView 抛异常
                rl.removeViewAt(0);
                TextView name = (TextView) view.findViewById(R.id.name);
                name.setText("备注");
                EditText et = (EditText) view.findViewById(R.id.et);
                et.setVisibility(View.VISIBLE);
                et.setFocusable(false);
                et.setEnabled(false);
                et.setText(tempRemark);
                llNecessary.addView(view);
            }
        }


    }

    private void addViewAndBindData(LicenceDetailBean.Item item, ViewGroup parent, boolean b) {

        View view = getLayoutInflater().inflate(R.layout.item_licence_item, parent, false);
        TextView name = (TextView) view.findViewById(R.id.name);
        if (b) {
            Drawable drawable = ContextCompat.getDrawable(this, R.drawable.icon_need_checked);
            drawable.setBounds(0, 0, drawable.getMinimumWidth(), drawable.getMinimumHeight());
            name.setCompoundDrawables(drawable, null, null, null);
        }
        name.setText(item.credentialName);
        //开户和委托书
        TextView tvDownPic = view.findViewById(R.id.tv_download_pic);
        TextView tvTips = view.findViewById(R.id.tv_tips);
        ImageView ivPicture = view.findViewById(R.id.iv_picture);
        ConstraintLayout cl_footer = view.findViewById(R.id.layout_footer);
        tvDownPic.setOnClickListener(v -> {
            RxPermissions rxPermissions = new RxPermissions(this);
            if (rxPermissions.isGranted(android.Manifest.permission.READ_EXTERNAL_STORAGE)
                    && rxPermissions.isGranted(android.Manifest.permission.WRITE_EXTERNAL_STORAGE)) {
                getRootPermissions();
            } else {
                PermissionDialogUtil.showPermissionInfoDialog(this,
                        "药帮忙App需要申请存储权限，用于下载存储图片",
                        () -> getRootPermissions());
            }

        });
        if (CODE_XYYWT.equals(item.licenseCode)) {//小药药委托书
            ImageLoader.loadImage(getMySelf(), ivPicture, AppNetConfig.getCDNHost() + downloadUrl);
            ivPicture.setOnClickListener(v -> {//示例图展示
                new ShowBigBitmapPopPublishForLongPic(AppNetConfig.getCDNHost() + downloadUrl).show(ivPicture);
            });
        } else if (CODE_KPXX.equals(item.licenseCode)) {//开票信息或开户许可证
            int imageRes = R.drawable.ic_invoice_example;
            ImageLoader.loadImage(getMySelf(), ivPicture, imageRes, DiskCacheStrategy.SOURCE);
            ivPicture.setOnClickListener(v -> new ShowBigBitmapPopPublishForLongPic(imageRes).show(ivPicture));
        }

        RecyclerView rc = (RecyclerView) view.findViewById(R.id.recyclerView);
        rc.setVisibility(View.VISIBLE);
        WrapContentLinearLayoutManager linearLayoutManager = new WrapContentLinearLayoutManager(this);
        linearLayoutManager.setOrientation(WrapLinearLayoutManager.HORIZONTAL);
        rc.setLayoutManager(linearLayoutManager);
//        if (item.getImageUrlList().size()>0){
//            rc.setVisibility(View.VISIBLE);
//        }else {
//            rc.setVisibility(View.GONE);
//        }
        LicensePicListAdapter adapter = new LicensePicListAdapter(R.layout.item_image, item.getImageUrlList(), false, linearLayoutManager);
        rc.setAdapter(adapter);
        parent.addView(view);
        if (CODE_XYYWT.equals(item.licenseCategoryCode)) {//小药药委托书
            cl_footer.setVisibility(View.VISIBLE);
            tvDownPic.setVisibility(View.VISIBLE);
            addXyyData(item);
        } else if (CODE_KPXX.equals(item.licenseCategoryCode)) {//开票信息或开户许可证
            cl_footer.setVisibility(View.VISIBLE);
            tvTips.setVisibility(View.VISIBLE);
        }

    }

    private void addXyyData(LicenceDetailBean.Item item) {

        //添加委托书有效期
        View view = getLayoutInflater().inflate(R.layout.layout_aptitude_detail_item_view, llNecessary, false);
        RelativeLayout rl = view.findViewById(R.id.rl_validity);
        rl.setVisibility(View.VISIBLE);
        //TextView name = (TextView) view.findViewById(R.id.name);
        //name.setText("小药药委托书有效期");
        TextView tvTime = view.findViewById(R.id.tv_validity_date);
        tvTime.setVisibility(View.VISIBLE);
        tvTime.setCompoundDrawables(null, null, null, null);
        tvTime.setTextColor(UiUtils.getColor(R.color.text_color_cancel_button));
        tvTime.setText(item.getXyyEntrusValidateTime());
        tvTime.setBackground(null);
        llNecessary.addView(view);


        //添加委托书编号
        View view1 = getLayoutInflater().inflate(R.layout.layout_aptitude_detail_item_view, llNecessary, false);
        RelativeLayout rl1 = view1.findViewById(R.id.rl_no);
        rl1.setVisibility(View.VISIBLE);
//        TextView name1 = (TextView) view1.findViewById(R.id.name);
//        name1.setText("小药药委托书编号");

        EditText tvCode = view1.findViewById(R.id.et_no);
        tvCode.setBackground(null);
        tvCode.setEnabled(false);
        tvCode.setVisibility(View.VISIBLE);
        tvCode.setCompoundDrawables(null, null, null, null);
        tvCode.setText(item.getXyyEntrusCode());
        llNecessary.addView(view1);


    }


    @Override
    public int getContentViewId() {
        return R.layout.activity_new_aptitude_detail;
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.iv_back:
                onBackPressed();
                break;
            case R.id.tv_right:
                //ToastUtils.showShort("日志");
                if (!DoubleUtils.isFastDoubleClick()) {
                    RoutersUtils.open("ybmpage://aptitudelog/" + merchantId + "/" + licenseAuditId + "/" + type);
                }
                break;
            case R.id.edit://编辑详情
                //跳转到修改订单
                builderDataAndLaunch();
                break;
        }
    }

    //构造修改页需要的数据，确保修改页接收参数一致
    private void builderDataAndLaunch() {


        if (!TextUtils.isEmpty(merchantId)) {
            showProgress();
            RequestParams params = new RequestParams();
            params.put("merchantId", merchantId);
            params.put("type", type);
            params.put("orgCode", licenceDetailBean.licenseAudit.ecOrgCode);
            HttpManager.getInstance().post(AppNetConfig.INIT_BILL_DETAIL, params, new BaseResponse<AptitudeInitBean>() {

                @Override
                public void onFailure(NetError error) {
                    dismissProgress();
                }

                @Override
                public void onSuccess(String content, BaseBean<AptitudeInitBean> baseBean, AptitudeInitBean data) {
                    dismissProgress();
                    if (baseBean != null && baseBean.isSuccess() && data != null) {
                        boolean isChange = data.licenseAudit != null;
                        //bean.licenseAudit != null表示可修改，否则只能添加
                        jumpNext(isChange, data);
                    }
                }
            });
        }
    }

    private void jumpNext(boolean isChange, AptitudeInitBean data) {
        if (data == null || data.necessaryLicenceList == null || data.necessaryLicenceList.isEmpty() || data.optionalLicenceList == null || data.optionalLicenceList.isEmpty()) {
            ToastUtils.showShort(getString(R.string.text_aptitude_failure));
            return;
        }
        ArrayList<LicenceBean> mNecessary = new ArrayList<>();
        ArrayList<LicenceBean> mOptional = new ArrayList<>();
        LicenceBean bean;
        //修改状态下，需要把上次已添加的照片同步过来，比如之前保存到了草稿，这次需要修改
        for (AptitudeInitBean.Licence licence : data.necessaryLicenceList) {
            bean = new LicenceBean(licence.code, licence.name, licence.isRequired, licence.templateUrl, licence.status, licence.listImgUrls);
            mNecessary.add(bean);
        }

        for (AptitudeInitBean.Licence licence : data.optionalLicenceList) {
            bean = new LicenceBean(licence.code, licence.name, licence.isRequired, licence.templateUrl, licence.status, licence.listImgUrls);
            mOptional.add(bean);
        }

        if (isChange && data.licenseAuditImgList != null) {
            HashMap<String, AptitudeInitBean.ImageItem> record = new HashMap<>();
            for (AptitudeInitBean.ImageItem item : data.licenseAuditImgList) {
                if (item == null) {
                    continue;
                }
                record.put(item.licenseCode, item);
            }
            AptitudeInitBean.ImageItem image;
            for (LicenceBean licence : mOptional) {
                image = record.get(licence.categoryCode);
                if (image != null) {
                    licence.xyyEntrusCode = image.xyyEntrusCode;
                    licence.xyyEntrusValidateTime = image.xyyEntrusValidateTime;


                }
            }
        }
        String aptitudeId = isChange ? data.licenseAudit.applicationNumber : "-1";

        String code = tvId.getText().toString().trim();
        String status = tvStatus.getText().toString().trim();
        int statusCode = 0;
        boolean addType = false;
        if (licenceDetailBean != null && licenceDetailBean.licenseAudit != null) {

            statusCode = licenceDetailBean.licenseAudit.auditStatus;

            addType = licenceDetailBean.licenseAudit.firstCommit();
        }
        String time = tvUpTime.getText().toString().trim();
        String remark = tvRemark.getText().toString().trim();
        if (licenceDetailBean != null && licenceDetailBean.licenseAudit != null) {
            AptitudeDetailEditData extra = new AptitudeDetailEditData(
                    aptitudeId,
                    licenceDetailBean.licenseAudit.ecOrgCode,
                    addType,
                    true,
                    remark,
                    tempRemark,
                    code,
                    status,
                    statusCode,
                    licenseAuditId,
                    time,
                    APTITUDE_DETAIL_ACTIVITY,
                    mNecessary,
                    mOptional);
            Intent intent = new Intent(AptitudeDetailActivity.this, AddAptitudeBasicInfoActivity.class);
            intent.putExtra("licenseStatus", "1");
            intent.putExtra("isEdit", "1");
            intent.putExtra("ecOrgCode", licenceDetailBean.licenseAudit.ecOrgCode);
            intent.putExtra("aptitudeDetailEditData", extra);
            startActivityForResult(intent, APTITUDE_DETAIL_REQUEST_CODE);
        }
    }

    private boolean isDownload;

    private void download() {
        if (isDownload) {
            ToastUtils.showLong("正在下载示例图片中，图片比较大请耐心等候");
            return;
        }
        if (!TextUtils.isEmpty(downloadUrl)) {
            isDownload = true;
            Glide.with(getMySelf())
                    .load(AppNetConfig.getCDNHost() + downloadUrl)
                    .asBitmap()
                    .toBytes()
                    .into(new SimpleTarget<byte[]>() {
                        @Override
                        public void onResourceReady(byte[] bytes, GlideAnimation<? super byte[]> glideAnimation) {
                            // 下载成功回调函数
                            // 数据处理方法，保存bytes到文件
                            String file = FileUtil.getAptitudeExamplePicFilePath();
                            SmartExecutorManager.getInstance().execute(new Runnable() {
                                @Override
                                public void run() {
                                    FileUtil.copy(file, bytes);
                                }
                            });
                            // 最后通知图库更新
                            sendBroadcast(new Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE,
                                    Uri.parse("file://" + file)));
                            ToastUtils.showLong("下载成功，已保存到本地相册");
                            isDownload = false;

                        }

                        @Override
                        public void onLoadFailed(Exception e, Drawable errorDrawable) {
                            // 下载失败回调
                            ToastUtils.showLong("下载失败");
                            isDownload = false;
                        }
                    });
        } else {
            ToastUtils.showLong("示例图片下载地址不存在");
            isDownload = false;
        }
    }

    @Override
    protected boolean isRegisterEventBus() {
        return true;
    }

    @Override
    protected void receiveEvent(Event event) {
        //Log.e("YBM", "event=============" + event.toString());
        if (event.getCode() == RX_BUS_UPDATE_LICENCEDTAIL) {
            boolean isSubscribe = (boolean) event.getData();
            if (isSubscribe) {//刷新数据
                llNecessary.setVisibility(View.GONE);
                if (llNecessary.getChildCount() > 0) {
                    llNecessary.removeAllViews();
                }
                getAptitudeInfo();
            }
        }
    }

    private String downloadUrl;

    private void getExamplePic() {
        RequestParams params = new RequestParams();
        params.put("merchantId", merchantId);
        HttpManager.getInstance().post(AppNetConfig.LICENSE_DOWNLOAD_VIEW_EXAMPLES_IMG, params, new BaseResponse<UrlStringBean>() {

            @Override
            public void onFailure(NetError error) {
                dismissProgress();
            }

            @Override
            public void onSuccess(String content, BaseBean<UrlStringBean> baseBean, UrlStringBean data) {
                dismissProgress();
                if (baseBean != null && baseBean.isSuccess() && data != null) {
                    downloadUrl = data.url;
                }
            }
        });
    }

    /**
     * 获取6.0读取文件的权限
     */
    @SuppressLint("CheckResult")
    private void getRootPermissions() {
        RxPermissions rxPermissions = new RxPermissions(this); // where this is an Activity instance
        rxPermissions.request(
                android.Manifest.permission.READ_EXTERNAL_STORAGE,
                android.Manifest.permission.WRITE_EXTERNAL_STORAGE
        ).subscribe(granted -> {
            if (granted) { // 在android 6.0之前会默认返回true
                download();
            } else {
                // 未获取权限
                Toast.makeText(getMySelf(), "您没有授权该权限，请在设置中打开授权", Toast.LENGTH_LONG).show();
            }
        }, throwable -> {

        });

    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
        //刷新资质变更列表
        Event<Boolean> event = new Event<>(RX_BUS_REFRESH_LICENCE_AUDIT_LIST, true);
        EventBusUtil.sendEvent(event);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == APTITUDE_DETAIL_REQUEST_CODE && resultCode == RESULT_OK) {
            finish();
        }
    }
}