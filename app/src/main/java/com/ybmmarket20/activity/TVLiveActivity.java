package com.ybmmarket20.activity;

import android.content.Intent;
import android.text.TextUtils;

import com.github.mzule.activityrouter.annotation.Router;
import com.ybm.app.bean.NetError;
import com.ybmmarket20.R;
import com.videolive.ECAudienceActivity;
import com.videolive.ECPlaybackActivity;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.TVLiveAccountInfo;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.constant.ECLiveConstants;
import com.ybmmarket20.utils.SpUtil;

import static com.ybmmarket20.constant.ECLiveConstants.EC_LIVE_ID;

/**
 * 直播分发页面 处理直播点播分发和进直播间前的逻辑处理
 */
@Router({"applive", "applive/:ec_live_id"})
public class TVLiveActivity extends BaseActivity {
    private String ecLiveId;

    @Override
    protected int getContentViewId() {
        return R.layout.activity_tvlive;
    }

    @Override
    protected void initData() {
        ecLiveId = getIntent().getStringExtra("ec_live_id");
        if (TextUtils.isEmpty(ecLiveId)) {
            finish();
        }
        getLiveInfo();
    }

    private void getLiveInfo() {
        showProgress(false);
        RequestParams params = new RequestParams();
        params.put("ecLiveId", ecLiveId);
        HttpManager.getInstance().post(AppNetConfig.GET_LIVE_INFO, params, new BaseResponse<TVLiveAccountInfo>() {

            @Override
            public void onFailure(NetError error) {
                dismissProgress();
                finish();
            }

            @Override
            public void onSuccess(String content, BaseBean<TVLiveAccountInfo> baseBean, TVLiveAccountInfo data) {
                if (baseBean != null && baseBean.isSuccess() && data != null && data.liveInfo!=null) {
                    SpUtil.writeString(ECLiveConstants.IM_USER_ID, data.liveInfo.txImUser);
                    SpUtil.writeString(ECLiveConstants.IM_USER_SIG, data.liveInfo.txImSig);
                    startLivePlay(data.liveInfo);
                } else {
                    finish();
                }
            }
        });
    }

    /**
     * 开始播放视频
     *
     * @param item 视频数据
     */
    private void startLivePlay(TVLiveAccountInfo.LiveInfo item) {
        Intent intent;
        if (item.liveStatus == 0 || item.liveStatus == 1 || item.liveStatus == 3) {
            intent = new Intent(this, ECAudienceActivity.class);
            intent.putExtra(ECLiveConstants.PLAY_URL, item.mixedPlayURL);
        } else if (item.liveStatus == 4) {
            intent = new Intent(this, ECPlaybackActivity.class);
            intent.putExtra(ECLiveConstants.PLAY_URL, item.mixedPlayURL);
        } else {
            return;
        }
        intent.putExtra(ECLiveConstants.LIVE_STATUS, item.liveStatus);
        intent.putExtra(ECLiveConstants.LIVE_ROOM_NAME, item.roomName);
        intent.putExtra(ECLiveConstants.MEMBER_COUNT, item.audienceCount);
        intent.putExtra(ECLiveConstants.GROUP_ID, item.roomID);
        intent.putExtra(ECLiveConstants.IM_USER_ID, item.txImUser);
        intent.putExtra(ECLiveConstants.IM_USER_SIG, item.txImSig);
        intent.putExtra(ECLiveConstants.AVATAR, item.avatar);
        intent.putExtra(ECLiveConstants.EC_LIVE_ID, ecLiveId);
        startActivityForResult(intent, 100);
        finish();
    }


}
