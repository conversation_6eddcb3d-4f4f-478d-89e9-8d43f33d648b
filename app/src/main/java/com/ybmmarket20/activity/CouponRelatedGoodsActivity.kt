package com.ybmmarket20.activity

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.text.TextUtils
import android.view.View
import android.view.inputmethod.EditorInfo
import androidx.activity.viewModels
import androidx.core.widget.doAfterTextChanged
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.recyclerview.widget.LinearLayoutManager
import com.github.mzule.activityrouter.annotation.Router
import com.ybmmarket20.R
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.CartVoucher
import com.ybmmarket20.bean.CouponInfoBean
import com.ybmmarket20.bean.CouponRelatedGoodsPriceBean
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.bean.SearchResultOPBean
import com.ybmmarket20.constant.IntentCanst
import com.ybmmarket20.utils.AdapterUtils
import com.ybmmarket20.utils.DateTimeUtil
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.SpUtil
import com.ybmmarket20.utils.StringUtil
import com.ybmmarket20.utils.UiUtils
import com.ybmmarket20.utils.setTitleAndTag
import com.ybmmarket20.viewmodel.BaseViewModel
import com.ybmmarket20.viewmodel.CouponRelatedGoodsViewModel
import com.ybmmarketkotlin.adapter.GoodListAdapterNew
import kotlinx.android.synthetic.main.activity_coupon_related_goods.crgFilter
import kotlinx.android.synthetic.main.activity_coupon_related_goods.crgPriceFilter
import kotlinx.android.synthetic.main.activity_coupon_related_goods.etSearch
import kotlinx.android.synthetic.main.activity_coupon_related_goods.ivEtClear
import kotlinx.android.synthetic.main.activity_coupon_related_goods.rv
import kotlinx.android.synthetic.main.activity_coupon_related_goods.tvCouponRelatedGoodsAmount
import kotlinx.android.synthetic.main.activity_coupon_related_goods.tvCouponRelatedGoodsDes
import kotlinx.android.synthetic.main.activity_coupon_related_goods.tvToCart
import kotlinx.android.synthetic.main.activity_coupon_to_use.tv_coupon_amount
import kotlinx.android.synthetic.main.activity_coupon_to_use.tv_coupon_full_reduce
import kotlinx.android.synthetic.main.activity_coupon_to_use.tv_coupon_full_reduce_max
import kotlinx.android.synthetic.main.activity_coupon_to_use.tv_coupon_subtitle
import kotlinx.android.synthetic.main.activity_coupon_to_use.tv_coupon_title
import kotlinx.android.synthetic.main.activity_coupon_to_use.tv_coupon_to_use_date
import kotlinx.android.synthetic.main.activity_coupon_to_use.tv_coupon_to_use_des
import kotlinx.android.synthetic.main.activity_coupon_to_use.tv_discount_unit
import kotlinx.android.synthetic.main.activity_coupon_to_use.tv_rice_unit

/**
 * 凑单
 */

//跨店券并且是指定店铺
const val COUPON_TYPE_SHOPS_VOUCHER_SPECIFIC_SHOP = "1"

@Router("coupoinrelatedgoods")
class CouponRelatedGoodsActivity: CouponAnalysisGoodsActivity() {

    private val mViewModel: CouponRelatedGoodsViewModel by viewModels()
    private var mAdapter: GoodListAdapterNew? = null
    private val mGoodsList = mutableListOf<RowsBean>()
    private lateinit var broadcastReceiver: BroadcastReceiver
    //优惠券id
    private var voucherTemplateId: String? = null
    //是否是跨店券并且是指定店铺
    private var isDesignateShop = false
    //优惠券是否领取
    private var hasReceived = ""

    override fun getContentViewId(): Int = R.layout.activity_coupon_related_goods

    override fun initData() {
        super.initData()
        voucherTemplateId = intent.getStringExtra("voucherTemplateId")
        isDesignateShop = intent.getStringExtra("isDesignateShop") == COUPON_TYPE_SHOPS_VOUCHER_SPECIFIC_SHOP
        hasReceived = intent.getStringExtra("hasReceived")?:""
        initObserver()
        initRv()
        initSearchEdit()
        initReceiver()
        initGoCart()
        getPriceFilterList()
        getCouponInfo()
        crgFilter.apply {
            //跨店券指定店铺显示商家筛选
            setItemList(if (isDesignateShop) getFilterItemList() else getFilterItemListWithoutBusiness(), voucherTemplateId, isDesignateShop)
            setOnResultCallback(::getGoodsListByAppendParam)
        }
    }

    private fun initGoCart() {
        tvToCart.setOnClickListener {
            RoutersUtils.open("ybmpage://main?tab=2&name=可用商品&id=${RoutersUtils.encodeRAWUrl("ybmpage://couponavailableactivity/$voucherTemplateId")}")
        }
    }

    private fun initSearchEdit() {
        etSearch.doAfterTextChanged {
            ivEtClear.visibility = if (it.toString().isEmpty()) View.GONE else View.VISIBLE

            if (it?.toString().isNullOrEmpty()) {
                getGoodsListByAppendParam(hashMapOf(
                        "queryWord" to ""
                ))
            }
        }
        ivEtClear.setOnClickListener {
            etSearch.setText("")
        }
        etSearch.setOnEditorActionListener { _, actionId, _ ->
            if (actionId == EditorInfo.IME_ACTION_SEARCH) {
                getGoodsListByAppendParam(mapOf(
                        "queryWord" to etSearch.text.toString()
                ))
                hideSoftInput()
                return@setOnEditorActionListener true
            }
            return@setOnEditorActionListener false
        }
    }

    private fun initRv() {
        mAdapter = GoodListAdapterNew(R.layout.item_goods_new, mGoodsList)
        mAdapter?.setEmptyView(this, R.layout.layout_empty_view, R.drawable.icon_empty, "暂无商品")
        rv.layoutManager = LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false)
        rv.adapter = mAdapter
        mAdapter?.isHiddenPromotionMore = true
        mAdapter?.setEnableLoadMore(true)
        mAdapter?.setOnLoadMoreListener({
            getGoodsListByLoadMore()
        }, rv)
        getGoodsListByAppendParam(mapOf("voucherTemplateId" to (voucherTemplateId?: "")))
    }

    private fun initObserver() {
        //价格筛选列表
        mViewModel.priceFilterListLiveData.observe(this, ::handlePriceFilter)
        //优惠券信息
        mViewModel.couponInfoLiveData.observe(this, ::handleCouponInfo)
        //商品列表
        mViewModel.couponRelatedGoodsListLiveData.observe(this, ::handleGoodsList)
        //商品列表-加载更多
        mViewModel.loadMoreGoodsListLiveData.observe(this, ::handleLoadMoreGoodsList)
        //获取凑单信息
        mViewModel.cartVoucherBeanLiveData.observe(this, ::handleCartVoucher)
    }

    /**
     * 处理价格筛选列表逻辑
     */
    private fun handlePriceFilter(priceFilterBean: BaseBean<List<CouponRelatedGoodsPriceBean>>) {
        if (priceFilterBean.isSuccess && priceFilterBean.data != null) {
            if (priceFilterBean.data.isNullOrEmpty()) {
                crgPriceFilter.visibility = View.GONE
                return
            }
            crgPriceFilter.setPriceList(priceFilterBean.data)
            crgPriceFilter.visibility = View.VISIBLE
            crgPriceFilter.setOnItemClickListener {
                getGoodsListByAppendParam(mapOf(
                    "minPrice" to (it.minPrice?: ""),
                    "maxPrice" to (it.maxPrice?: ""),
                    "priceRangeStatus" to (it.priceRangeStatus?: "")
                ))
            }
        }
    }

    /**
     * 处理优惠券信息逻辑
     */
    private fun handleCouponInfo(couponInfoBean: BaseBean<CouponInfoBean>) {
        if (couponInfoBean.isSuccess && couponInfoBean.data != null) {
            val bean = couponInfoBean.data
            makeUpOrderCouponExposure(bean, COUPON_MAKE_UP_ORDER_TYPE_NEW)
            tv_coupon_to_use_des.text = bean.voucherInstructions
            //券名称
            tv_coupon_subtitle.text = bean.voucherTitle
            //店铺名
            setTitleAndTag(mySelf, bean.voucherType, bean.shopName
                ?: "", bean.voucherTypeDesc ?: "", tv_coupon_title)
            //券有效期
            if (bean.validDate != null && bean.expireDate != null) {
                var time = (DateTimeUtil.getCouponDateTime2(bean.validDate)
                        + "-" + DateTimeUtil.getCouponDateTime2(bean.expireDate))
                if (time.contains("1970")) time = ""
                tv_coupon_to_use_date.text = time
            }

            tv_coupon_full_reduce.text = bean.minMoneyToEnableDesc
            if (bean.voucherState == 1) {
                tv_rice_unit.visibility = View.GONE
                tv_discount_unit.visibility = View.VISIBLE
                val amount = UiUtils.transform2Int(bean.discount)
                tv_coupon_amount.text = StringUtil.setDotAfterSize(amount, 19)
                tv_discount_unit.text = "折"
            } else {
                tv_rice_unit.visibility = View.VISIBLE
                tv_discount_unit.visibility = View.GONE
                tv_coupon_amount.text = UiUtils.transformInt(bean.moneyInVoucher)
                tv_rice_unit.text = "¥"
            }

            if (!TextUtils.isEmpty(bean.maxMoneyInVoucherDesc)) {
                tv_coupon_full_reduce_max.visibility = View.VISIBLE
                tv_coupon_full_reduce_max.text = bean.maxMoneyInVoucherDesc
            } else {
                tv_coupon_full_reduce_max.visibility = View.GONE
            }
        }
    }

    /**
     * 处理商品列表加载逻辑
     */
    private fun handleGoodsList(goodsListBean: BaseBean<SearchResultOPBean>) {
        if (goodsListBean.isSuccess && goodsListBean.data != null) {
            mGoodsList.clear()
            mGoodsList.addAll(goodsListBean.data.dataList)
            AdapterUtils.addLocalTimeForRows(mGoodsList)
            mAdapter = GoodListAdapterNew(R.layout.item_goods_new, mGoodsList).apply {
                isHiddenPromotionMore = true
                setEmptyView(this@CouponRelatedGoodsActivity, R.layout.layout_empty_view, R.drawable.icon_empty, "暂无商品")
                rv.adapter = this
                setEnableLoadMore(true)
                setOnLoadMoreListener({ getGoodsListByLoadMore() }, rv)
                AdapterUtils.getAfterDiscountPrice(goodsListBean.data.dataList.toMutableList(),
                    this, true)
                notifyDataChangedAfterLoadMore(!goodsListBean.data.isEnd)
            }
        }
        dismissProgress()
    }

    /**
     * 处理商品列表加载更多逻辑
     */
    private fun handleLoadMoreGoodsList(goodsListBean: BaseBean<SearchResultOPBean>) {
        if (goodsListBean.isSuccess && goodsListBean.data != null && goodsListBean.data.rows != null) {
            mGoodsList.addAll(goodsListBean.data.dataList)
            AdapterUtils.addLocalTimeForRows(mGoodsList)
            mAdapter?.notifyDataChangedAfterLoadMore(!goodsListBean.data.isEnd)
            mAdapter?.let {
                AdapterUtils.getAfterDiscountPrice(goodsListBean.data.dataList.toMutableList(),
                    it, true)
            }
        } else {
//            mAdapter?.notifyDataChangedAfterLoadMore(false)
            mAdapter?.loadMoreFail()
        }
    }

    /**
     * 处理凑单信息
     */
    private fun handleCartVoucher(cartVoucher: BaseBean<CartVoucher>) {
        if (cartVoucher.isSuccess && cartVoucher.data != null) {
            val bean = cartVoucher.data?.list?.takeIf { it.size > 0 }?.get(0)
            if (bean == null) return
            val amount = "小计: ¥${StringUtil.DecimalFormat2Double(bean.selectSkuAmount)}"
            val desc = if (bean.noEnoughMoney <= 0) {
                //已经满足优惠条件,可立减¥xxx
                "${resources.getString(R.string.coupon_amount_des1)}${StringUtil.DecimalFormat2Double(bean.moneyInVoucher)}"
            } else {
                //再买¥xxx 可使用优惠券
                "${resources.getString(R.string.buy_again_with_symbol)}${StringUtil.DecimalFormat2Double(bean.noEnoughMoney)}${resources.getString(R.string.can_use_coupon)}"
            }
            tvCouponRelatedGoodsAmount.text = amount
            tvCouponRelatedGoodsDes.text = desc
        }
    }

    /**
     * 获取价格筛选列表
     */
    private fun getPriceFilterList() {
        mViewModel.getPriceFilterList(mapOf("voucherTemplateId" to (voucherTemplateId?: "")))
    }

    /**
     * 获取优惠券信息
     */
    private fun getCouponInfo() {
        val params: HashMap<String, String> = hashMapOf()
        params["templateIds"] = voucherTemplateId?: ""
        when (hasReceived){
            "0" -> {
                params["hasReceived"] = "0"
            }
            "1" ->{
                params["hasReceived"] = "1"
            }
            else-> {

            }
        }
        mViewModel.getCouponInfo(params)
        mViewModel.getCartVoucherBean(SpUtil.getMerchantid(), voucherTemplateId?: "")
    }

    /**
     * 通过追加参数获取商品列表
     */
    private fun getGoodsListByAppendParam(params: Map<String, String>) {
        showProgress()
        mViewModel.getGoodsListByAppendParam(params)
    }

    /**
     * 获取商品列表-加载更多
     */
    private fun getGoodsListByLoadMore() {
        mViewModel.getGoodsListByLoadMore()
    }

    /**
     * 初始化广播接收器
     */
    private fun initReceiver() {
        broadcastReceiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context, intent: Intent?) {
                intent?.takeIf { it.action == IntentCanst.ACTION_ADD_PRODUCT || it.action == IntentCanst.ACTION_ADD_PRODUCT_FROM_DETAIL_TO_COUPON || it.action == IntentCanst.ACTION_RELATED_GOODS_ADD_GOODS }?.let {
                    mViewModel.getCartVoucherBean(SpUtil.getMerchantid(), voucherTemplateId?: "")
                }
            }
        }
        LocalBroadcastManager.getInstance(applicationContext).registerReceiver(broadcastReceiver, IntentFilter().apply {
            addAction(IntentCanst.ACTION_ADD_PRODUCT)
            addAction(IntentCanst.ACTION_RELATED_GOODS_ADD_GOODS)
            addAction(IntentCanst.ACTION_ADD_PRODUCT_FROM_DETAIL_TO_COUPON)  // 接收详情页加购消息更新小计
        })
    }

    override fun getBaseViewModel(): BaseViewModel = mViewModel

    override fun onDestroy() {
        super.onDestroy()
        LocalBroadcastManager.getInstance(this).unregisterReceiver(broadcastReceiver)
    }
}