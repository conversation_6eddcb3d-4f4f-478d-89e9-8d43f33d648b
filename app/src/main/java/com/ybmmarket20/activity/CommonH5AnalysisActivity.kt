package com.ybmmarket20.activity

import com.ybmmarket20.view.X5WebView
import com.ybmmarket20.xyyreport.SpmLogUtil

abstract class CommonH5AnalysisActivity: BaseProductActivity() {

    companion object {
        //H5导航按钮点击-返回
        const val COMMON_H5_NAVIGATE_CLICK_BACK = "1"
        //H5导航按钮点击-关闭
        const val COMMON_H5_NAVIGATE_CLICK_CLOSE = "2"
        //H5导航按钮点击-分享
        const val COMMON_H5_NAVIGATE_CLICK_SHARE = "3"
        //H5导航按钮点击-购物车
        const val COMMON_H5_NAVIGATE_CLICK_CART = "4"
    }

    abstract fun getX5WebView(): X5WebView

    private fun trackCallJs(position: String?, positionName: String?) {
        getX5WebView().loadUrl("javascript:clickOnTheTopOfTheApp('btn@$position', 'text-$positionName')")
    }

    fun trackBack() {
        SpmLogUtil.print("CommonH5-点击-返回")
        trackCallJs(COMMON_H5_NAVIGATE_CLICK_BACK, "返回")
    }

    fun trackClose() {
        SpmLogUtil.print("CommonH5-点击-关闭")
        trackCallJs(COMMON_H5_NAVIGATE_CLICK_CLOSE, "关闭")
    }

    fun trackShare() {
        SpmLogUtil.print("CommonH5-点击-分享")
        trackCallJs(COMMON_H5_NAVIGATE_CLICK_SHARE, "分享")
    }

    fun trackCart() {
        SpmLogUtil.print("CommonH5-点击-购物车")
        trackCallJs(COMMON_H5_NAVIGATE_CLICK_CART, "购物车")
    }
}