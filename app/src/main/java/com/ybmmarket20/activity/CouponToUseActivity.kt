package com.ybmmarket20.activity

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Bundle
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import android.text.Editable
import android.text.TextUtils
import android.text.TextWatcher
import android.view.View
import android.view.inputmethod.EditorInfo
import com.flyco.tablayout.listener.CustomTabEntity
import com.flyco.tablayout.listener.OnTabSelectListener
import com.github.mzule.activityrouter.annotation.Router
import com.ybm.app.bean.NetError
import com.ybm.app.view.WrapGridLayoutManager
import com.ybm.app.view.WrapLinearLayoutManager
import com.ybmmarket20.R
import com.ybmmarket20.bean.*
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.common.BaseResponse
import com.ybmmarket20.network.HttpManager
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.constant.IntentCanst
import com.ybmmarket20.utils.*
import com.ybmmarket20.view.CategoryLevel1AllPopWindow
import kotlinx.android.synthetic.main.activity_coupon_to_use.*

/**
 * 优惠券-去使用
 */
@Router("couponavailableactivity", "couponavailableactivity/:coupon_id", "couponavailableactivity/:coupon_id/:fromCart", "couponavailableactivity/:coupon_id/:activityType", "couponavailableactivity/:coupon_id/:activityType/:shopCode")
class CouponToUseActivity : BaseProductActivity(), View.OnClickListener {

    private var categoryData: ArrayList<CustomTabEntity> = ArrayList()
    private var categoryAllData: List<OneRowsBean> = ArrayList()
    private var popWindowCategoryLevel1All: CategoryLevel1AllPopWindow? = null
    private var contentFragment: CouponToUseFragment = CouponToUseFragment()
    private var categoryId: String = ""
    private var categoryEntity: List<CouponGoodsBean> = ArrayList()
    private var currentLayoutManager: RecyclerView.LayoutManager = WrapLinearLayoutManager(this, LinearLayoutManager.VERTICAL, false)
    private var currentTabPosition = 0
    private lateinit var broadcastReceiver: BroadcastReceiver
    private var needRefresh: Boolean = false
    private var activityType: String? = null//非必填 活动类型 0商品劵 1下单返劵活动 下单返券紧急需求添加
    private var isActivityOrderReturnType: Boolean = false//是否活动下单返券
    private var isFromCart: Int = 0 //是否来自购物车凑单

    override fun getContentViewId(): Int = R.layout.activity_coupon_to_use

    override fun onResume() {
        super.onResume()
        if (needRefresh) {
            showProgress()
            getCouponData()
            contentFragment.switchData(et_to_use_coupon.text.toString().trim(), categoryId, getTemplateId(), true)
        }

    }

    override fun getRawAction(): String = ""

    override fun initData() {
        super.initData()
        activityType = intent.getStringExtra("activityType")
        val fromCartTemp = intent.getStringExtra("fromCart");
        if (!TextUtils.isEmpty(fromCartTemp)) {
            isFromCart = fromCartTemp!!.toInt()
        }
        if (activityType != null && activityType!!.isNotEmpty()) {
            cl_to_use_coupon_head.visibility = View.GONE
            contentFragment.arguments = Bundle().apply { putString("activityType", activityType) }
            isActivityOrderReturnType = true
            //下单返券搜索栏默认提示
            et_to_use_coupon.hint = getString(R.string.order_return_coupon_search_hint)
        }
        showProgress()
        //获取活动优惠信息和购物车小计
        getActivityData()

        getCategory(getTemplateId())
        getCouponInfo()
        getCouponData()
        initReceiver()
        title_left_search.setOnClickListener(this)
        ib_to_user_coupon_category_all.setOnClickListener(this)
        tv_coupon_to_cart.setOnClickListener(this)
        et_to_use_coupon.setOnClickListener(this)
        title_right_btn.setOnClickListener(this)
        //监听标签切换
        ctl_coupon_to_use.setOnTabSelectListener(object : OnTabSelectListener {
            override fun onTabSelect(position: Int) {
                categoryId = categoryEntity[position].id ?: ""
                contentFragment.clearRows()
                hideSoftInput()
                showProgress()
                contentFragment.switchData(et_to_use_coupon.text.toString().trim(), categoryId, getTemplateId(), true)
                currentTabPosition = position
            }

            override fun onTabReselect(position: Int) {
            }
        })

        et_to_use_coupon.setOnEditorActionListener { _, actionId, _ ->
            if (actionId == EditorInfo.IME_ACTION_SEARCH) {
                hideSoftInput()
                showProgress()
                contentFragment.switchData(et_to_use_coupon.text.toString().trim(), categoryId, getTemplateId(), true)
            }
            true
        }

        et_to_use_coupon.addTextChangedListener(object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {
                iv_clear.visibility = s?.let { if (s.isEmpty()) View.GONE else View.VISIBLE }
                        ?: View.GONE
            }

            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
            }
        })
    }

    /**
     * 添加fragment
     */
    private fun addFragment() {
        supportFragmentManager.beginTransaction().apply {
            add(R.id.fl_to_use_coupon_content, contentFragment)
            commit()
        }
        contentFragment.switchData(et_to_use_coupon.text.toString().trim(), categoryId, getTemplateId(), false)
        currentTabPosition = 0
    }

    /**
     * 设置全部标签下拉列表
     */
    private fun initPopCategoryLevel1All(currentPosition: Int) {
        popWindowCategoryLevel1All = CategoryLevel1AllPopWindow()
        popWindowCategoryLevel1All?.setNewData(categoryAllData)
        popWindowCategoryLevel1All?.setOnCallbackListener { position ->
            ctl_coupon_to_use.currentTab = position
            currentTabPosition = position
            categoryId = categoryEntity[position].id ?: ""
            showProgress()
            contentFragment.switchData(et_to_use_coupon.text.toString().trim(), categoryId, getTemplateId(), true)
        }
        popWindowCategoryLevel1All?.setSelectedPosition(currentPosition)
    }

    /**
     * 获取分类数据
     */
    private fun getCategory(voucherTemplateId: String) {
        val params: RequestParams = RequestParams().apply {
            put("voucherTemplateId", voucherTemplateId)
            put("merchantId", SpUtil.getMerchantid())
            if (isActivityOrderReturnType) put("activityType", activityType)
        }
        HttpManager.getInstance().post(AppNetConfig.COUPON_TO_USE_GET_CATEGORY, params, object : BaseResponse<List<CouponGoodsBean>>() {
            override fun onSuccess(content: String?, obj: BaseBean<List<CouponGoodsBean>>?, t: List<CouponGoodsBean>?) {
                super.onSuccess(content, obj, t)
                //标签列表为空或null时关闭loading不加载内容列表，否则等列表加载完后关闭loading
                ifNotNull(obj, t) { baseBean, list ->
                    if (list.isEmpty()) dismissProgress()
                    if (baseBean.isSuccess) {
                        categoryEntity = list
                        categoryData = mapToCustomTabEntityList(list)
                        categoryAllData = mapToAllTabEntityList(list)
                        if (categoryData.isNotEmpty()) {
                            categoryId = categoryEntity[0].id ?: ""
                            ctl_coupon_to_use.setTabData(categoryData)
                            //标签加载后添加列表fragment
                            if (list.isNotEmpty()) addFragment()
                        }
                    } else dismissProgress()
                }
            }

            override fun onFailure(error: NetError?) {
                super.onFailure(error)
                dismissProgress()
            }
        })
    }

    /**
     * 获取优惠券信息
     */
    private fun getCouponInfo() {
        if (isActivityOrderReturnType) return
        val requestParams = RequestParams().apply {
            put("templateIds", getTemplateId())
            put("merchantId", SpUtil.getMerchantid())
        }
        HttpManager.getInstance().post(AppNetConfig.GET_COUPON_TEMPLATE_BY_INFO, requestParams, object : BaseResponse<List<CouponInfoBean>>() {
            override fun onSuccess(content: String?, obj: BaseBean<List<CouponInfoBean>>?, t: List<CouponInfoBean>?) {
                super.onSuccess(content, obj, t)
                ifNotNull(obj, t) { baseBean, beanList ->
                    if (baseBean.isSuccess && beanList.isNotEmpty()) {
                        val bean = beanList[0]
                        makeUpOrderCouponExposure(bean, COUPON_MAKE_UP_ORDER_TYPE_OLD)
                        tv_coupon_to_use_des.text = bean.voucherInstructions
                        //券名称
                        tv_coupon_subtitle.text = bean.voucherTitle
                        //店铺名
                        setTitleAndTag(mySelf, bean.voucherType, bean.shopName
                                ?: "", bean.voucherTypeDesc ?: "", tv_coupon_title)
                        //券有效期
                        val time = (DateTimeUtil.getCouponDateTime2(bean.validDate)
                                + "-" + DateTimeUtil.getCouponDateTime2(bean.expireDate))
                        tv_coupon_to_use_date.text = time
                        tv_coupon_full_reduce.text = bean.minMoneyToEnableDesc
                        if (bean.voucherState == 1) {
                            tv_rice_unit.visibility = View.GONE
                            tv_discount_unit.visibility = View.VISIBLE
                            val amount = UiUtils.transform2Int(bean.discount)
                            tv_coupon_amount.text = StringUtil.setDotAfterSize(amount, 19)
                            tv_discount_unit.text = "折"
                        } else {
                            tv_rice_unit.visibility = View.VISIBLE
                            tv_discount_unit.visibility = View.GONE
                            tv_coupon_amount.text = UiUtils.transformInt(bean.moneyInVoucher)
                            tv_rice_unit.text = "¥"
                        }

                        if (!TextUtils.isEmpty(bean.maxMoneyInVoucherDesc)) {
                            tv_coupon_full_reduce_max.visibility = View.VISIBLE
                            tv_coupon_full_reduce_max.text = bean.maxMoneyInVoucherDesc
                        } else {
                            tv_coupon_full_reduce_max.visibility = View.GONE
                        }
                    }
                }
            }
        })
    }

    /**
     * 获取模板id
     */
    private fun getTemplateId(): String {
        return intent.getStringExtra("coupon_id") ?: ""
    }

    /**
     * 获取shopCode
     */
    private fun getShopCode(): String {
        return intent.getStringExtra("shopCode") ?: ""
    }

    /**
     * 转换为CommonTabLayout可使用的数据
     */
    private fun mapToCustomTabEntityList(couponGoodsBeanList: List<CouponGoodsBean>): ArrayList<CustomTabEntity> {
        return couponGoodsBeanList.map<CouponGoodsBean, CustomTabEntity> {
            TabEntity(it.name)
        } as ArrayList<CustomTabEntity>
    }

    /**
     * 转换为全部分类可使用的数据
     */
    private fun mapToAllTabEntityList(couponGoodsBeanList: List<CouponGoodsBean>): List<OneRowsBean> {
        return couponGoodsBeanList.map {
            OneRowsBean().apply {
                id = try {
                    it.id?.toInt() ?: 0
                } catch (e: Exception) {
                    e.printStackTrace()
                    0
                }
                name = it.name
            }
        }
    }

    /**
     * 获取金额数据
     */
    private fun getCouponData() {
        if (isActivityOrderReturnType) return
        if (TextUtils.isEmpty(getTemplateId())) return
        val merchantId = SpUtil.getMerchantid()
        val params = RequestParams().apply {
            put("merchantId", merchantId)
            put("voucherTemplateId", getTemplateId())
        }
        HttpManager.getInstance().post(AppNetConfig.SELECT_CART_VOUCHER, params, object : BaseResponse<CartVoucher?>() {
            override fun onSuccess(content: String?, baseBean: BaseBean<CartVoucher?>?, data: CartVoucher?) {
                super.onSuccess(content, baseBean, data)
                if (baseBean != null && baseBean.isSuccess && data != null && data.list != null && data.list.size > 0) {
                    val bean = data.list[0]
                    val amount = "小计: ¥${StringUtil.DecimalFormat2Double(bean.selectSkuAmount)}"
                    val desc = if (bean.noEnoughMoney <= 0) {
                        //已经满足优惠条件,可立减¥xxx
                        "${resources.getString(R.string.coupon_amount_des1)}${StringUtil.DecimalFormat2Double(bean.moneyInVoucher)}"
                    } else {
                        //再买¥xxx 可使用优惠券
                        "${resources.getString(R.string.buy_again_with_symbol)}${StringUtil.DecimalFormat2Double(bean.noEnoughMoney)}${resources.getString(R.string.can_use_coupon)}"
                    }
                    tv_coupon_to_use_amount.text = amount
                    tv_coupon_to_use_amount_des.text = desc
                    handleListMarginBottom()
                }
            }

            override fun onFailure(error: NetError) {}
        })
    }

    /**
     * 防止底部遮挡列表，根据底部高度设置列表的marginBottom
     */
    private fun handleListMarginBottom() {
        cl_coupon_to_use.postDelayed({
            val lp: ConstraintLayout.LayoutParams = cl_coupon_to_use.layoutParams as ConstraintLayout.LayoutParams
            lp.bottomMargin = cl_bottom.measuredHeight
            cl_coupon_to_use.layoutParams = lp
        }, 100)
    }

    /**
     * 获取活动返券信息和小计
     */
    private fun getActivityData() {
        if (!isActivityOrderReturnType) return
        if (TextUtils.isEmpty(getTemplateId())) return
        if (TextUtils.isEmpty(getShopCode())) return
        val merchantId = SpUtil.getMerchantid()
        val params = RequestParams().apply {
            put("merchantId", merchantId)
            put("actId", getTemplateId())
            put("shopCode", getShopCode())
        }
        HttpManager.getInstance().post(AppNetConfig.SELECT_CART_ACTIVITY_VOUCHER, params, object : BaseResponse<CartActivityVoucher?>() {
            override fun onSuccess(content: String?, baseBean: BaseBean<CartActivityVoucher?>?, data: CartActivityVoucher?) {
                super.onSuccess(content, baseBean, data)
                if (baseBean != null && baseBean.isSuccess && data != null) {
                    val amount = "小计: ¥${data.totalAmount ?: ""}"
                    tv_coupon_to_use_amount.text = amount
                    tv_coupon_to_use_amount_des.text = data.message
                    if (!TextUtils.isEmpty(data.message)) {
                        tv_coupon_to_use_amount_des.visibility = View.VISIBLE
                    } else {
                        tv_coupon_to_use_amount_des.visibility = View.GONE
                    }
                    handleListMarginBottom()
                }
            }

            override fun onFailure(error: NetError) {}
        })
    }

    override fun onDestroy() {
        super.onDestroy()
        if (isFromCart == 1) {
            LocalBroadcastManager.getInstance(this).sendBroadcast(Intent(IntentCanst.ACTION_CART_TO_COUPON_RESULT))
        }
        LocalBroadcastManager.getInstance(applicationContext).unregisterReceiver(broadcastReceiver)
    }

    /**
     * 初始化广播接收器
     */
    private fun initReceiver() {
        broadcastReceiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context, intent: Intent?) {
                intent?.let {
                    when {
                        IntentCanst.ACTION_ADD_PRODUCT == it.action -> {
                            if (isActivityOrderReturnType) getActivityData() else getCouponData()
                        }
                        // 接收详情页加购消息更新小计
                        IntentCanst.ACTION_ADD_PRODUCT_FROM_DETAIL_TO_COUPON == it.action -> {
                            getCouponData()
                        }
                    }
                }
            }
        }
        LocalBroadcastManager.getInstance(applicationContext).registerReceiver(broadcastReceiver, IntentFilter().apply {
            addAction(IntentCanst.ACTION_ADD_PRODUCT)
            addAction(IntentCanst.ACTION_ADD_PRODUCT_FROM_DETAIL_TO_COUPON)
        })
    }

    override fun onClick(v: View?) {
        when (v?.id) {
            //返回
            R.id.title_left_search -> finish()
            //显示全部标签
            R.id.ib_to_user_coupon_category_all -> {
                initPopCategoryLevel1All(currentTabPosition)
                popWindowCategoryLevel1All?.show(cl_to_use_coupon_head)
            }
            // 凑单页搜索
            R.id.title_right_btn -> {
                hideSoftInput()
                //无分类数据时不添加fragment，则RecyclerView为null
                if (contentFragment.recyclerView == null) return
//                contentFragment.switchLayoutManager()
                showProgress()
                contentFragment.switchData(et_to_use_coupon.text.toString().trim(), categoryId, getTemplateId(), true)
            }
            //跳转到购物车
            R.id.tv_coupon_to_cart -> {
                RoutersUtils.open("ybmpage://main?tab=2&name=可用商品&id=${RoutersUtils.encodeRAWUrl("ybmpage://couponavailableactivity/${getTemplateId()}")}")
            }
            //清空搜索框
            R.id.et_to_use_coupon -> {
                et_to_use_coupon.setText("")
            }
        }
    }
}