package com.ybmmarket20.activity;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.Space;
import android.widget.TextView;

import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProvider;
import com.github.mzule.activityrouter.annotation.Router;
import com.ybm.app.bean.NetError;
import com.ybm.app.common.BaseYBMApp;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.EmptyBean;
import com.ybmmarket20.bean.OrderRefundBankDto;
import com.ybmmarket20.bean.OrderRefundExpressDto;
import com.ybmmarket20.bean.RefundDetail;
import com.ybmmarket20.bean.RefundDetailBean;
import com.ybmmarket20.bean.RefundDetailCurrentBean;
import com.ybmmarket20.bean.RefundOrderStatusBean;
import com.ybmmarket20.common.*;
import com.ybmmarket20.common.util.ToastUtils;
import com.ybmmarket20.common.widget.RoundLinearLayout;
import com.ybmmarket20.common.widget.RoundedImageView;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.fragments.AddImageFragment;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.utils.ImageUtil;
import com.ybmmarket20.utils.RoutersUtils;
import com.ybmmarket20.utils.SpUtil;
import com.ybmmarket20.utils.YbmCommand;
import com.ybmmarket20.view.RefundDetailsOptimizationLayout;

import java.util.ArrayList;
import java.util.List;

import butterknife.Bind;
import butterknife.OnClick;
import com.ybmmarket20.viewmodel.RejectRefundViewModel;

/**
 * 退款详情
 * 若退款单状态为“待审核”状态（第一步客服审核未介入），底部展示“编辑收款账户”按钮，其他状态隐藏该按钮。
 * 点击“编辑收款账户”，校验当前的退款单状态
 * 若退款单状态已改变，则弹框提示：“当前退款单客服已受理，
 * 如需修改收款账户请联系客服人员”，点击“确认”则关闭弹框并刷新当前页面
 * 若退款单状态未改变，则跳转至右侧的“编辑收款账户信息”页面，将之前保存的收款账户记录带入，用户可修改；
 * 当退款单状态为“待审核”且退款发起方为客户自行发起，则在底部展示“取消退款”按钮，其他状态隐藏。点击“取消退款”时，判断当前退款单客服是否已审核：
 * 若已审核则弹框提示：“当前退款单客服已受理，如需取消请联系客服人员”。
 * 若未审核则弹框提示：“确认取消当前退款单吗？”。确认取消则关闭当前退款单，再想想关闭弹框。
 */
@Router({"refunddetail", "refunddetail/:orderId", "refunddetail/:orderRefundId", "refunddetail/:orderId/:refundOrderNo", "refunddetail/:orderRefundId/:refundOrderNo"})
public class RefundDetailActivity extends BaseActivity {

    @Bind(R.id.tv_status)
    TextView tvStatus;
    @Bind(R.id.tv_check_sales_commodity)
    TextView tvCheckSalesCommodity;
    @Bind(R.id.tv_result_title)
    TextView tvResultTitle;
    @Bind(R.id.tv_money)
    TextView tvMoney;
    @Bind(R.id.tv_balance)
    TextView tvBalance;
    @Bind(R.id.tv_refund_route)
    TextView tvRefundRoute;
    @Bind(R.id.tv_refund_time)
    TextView tvRefundTime;
    @Bind(R.id.tv_close_reason)
    TextView tvCloseReason;
    @Bind(R.id.ll_refund_result)
    LinearLayout llRefundResult;
    @Bind(R.id.tv_apply_money)
    TextView tvApplyMoney;
    @Bind(R.id.tv_small_payment)
    TextView tvSmallPaymentApply;
    @Bind(R.id.tv_apply_time)
    TextView tvApplyTime;
    @Bind(R.id.tv_reason)
    TextView tvReason;
    @Bind(R.id.tv_info)
    TextView tvInfo;
    @Bind(R.id.btn_kefu)
    TextView btnKefu;
    @Bind(R.id.fragment)
    RelativeLayout fragment;
    @Bind(R.id.ll_card_refund_info)
    LinearLayout llCardRefundInfo;
    @Bind(R.id.tv_address_name)
    TextView tvAddressName;
    @Bind(R.id.tv_address_phone)
    TextView tvAddressPhone;
    @Bind(R.id.tv_address)
    TextView tvAddress;
    @Bind(R.id.tv_address_title)
    TextView tvAddressTitle;
    @Bind(R.id.tv_delivery_instructions)
    TextView tvDeliveryInstructions;
    @Bind(R.id.ll_refund_address)
    LinearLayout llRefundAddress;
    @Bind(R.id.rdol)
    RefundDetailsOptimizationLayout rdol;
    @Bind(R.id.space)
    Space space;
    @Bind(R.id.tv_small_detail)
    TextView tvSmallDetail;

    @Bind(R.id.tv_edit_gathering_id)
    TextView tvEditGatheringId;
    @Bind(R.id.btn_cancel_the_refund)
    TextView btnCancelTheRefund;
    @Bind(R.id.tv_opening_bank)
    TextView tvOpeningBank;
    @Bind(R.id.tv_credit_card_numbers)
    TextView tvCreditCardNumbers;
    @Bind(R.id.tv_account_holder)
    TextView tvAccountHolder;
    @Bind(R.id.tv_phone)
    TextView tvPhone;
    @Bind(R.id.ll_bank)
    RoundLinearLayout llBank;
    @Bind(R.id.tv_fill_in_return_logistics)
    TextView tvFillInReturnLogistics;
    @Bind(R.id.ll_expressage)
    RoundLinearLayout llExpressage;
    @Bind(R.id.tv_expressage_name)
    TextView tvExpressageName;
    @Bind(R.id.tv_expressage_number)
    TextView tvExpressageNumber;
    @Bind(R.id.tv_refund_channel)
    TextView tvRefundChannel;

    @Bind(R.id.iv_pay_credential)
    RoundedImageView ivPayCredential;
    @Bind(R.id.tv_express_type)
    TextView tvExpressType;
    @Bind(R.id.ll_invoice_express)
    LinearLayout llInvoiceExpress;
    @Bind(R.id.ll_pay_credential)
    LinearLayout llPayCredential;
    @Bind(R.id.tv_express_type_copy_btn)
    TextView tvExpressTypeCopyBtn;
    @Bind(R.id.iv_pay_credential_symbol)
    TextView ivPayCredentialSymbol;
    @Bind(R.id.tv_virtual_money)
    TextView tvVirtualMoney;
    @Bind(R.id.tv_indemnity_money)
    TextView tvIndemnityMoney;
    @Bind(R.id.tv_indemnity_money2)
    TextView tvIndemnityMoney2;
    @Bind(R.id.llRefundCertificate)
    LinearLayout llRefundCertificate;


    protected AddImageFragment imageFragment;
    private String orderId;
    private String refundOrderNo;
    protected Bundle arg;
    private RefundDetail detail;
    private OrderRefundBankDto orderRefundBankDto;
    private String channelCode = "1";
    private RejectRefundViewModel refundViewModel;
    //客服电话
    private String kfContract;
    //是否是小额赔偿
    private boolean isSmallPayment;

    @Override
    protected void initData() {
        refundViewModel = new ViewModelProvider(this).get(RejectRefundViewModel.class);
        setTitle("退款详情");
        String id = getIntent().getStringExtra("orderId");
        if (!TextUtils.isEmpty(id)) {
            orderId = id;
        }
        id = getIntent().getStringExtra("orderRefundId");
        if (!TextUtils.isEmpty(id)) {
            orderId = id;
        }
        refundOrderNo = getIntent().getStringExtra("refundOrderNo");
//        billType = getIntent().getStringExtra("billType");

        if (TextUtils.isEmpty(orderId)) {
            finish();
            ToastUtils.showShort("参数错误");
            return;
        }

        btnKefu.setText("客服电话：" + RoutersUtils.kefuPhone);
        rdol.setCountDownListener(this::getRefundDetail);
        String finalId = id;
        rdol.setOnRefundClickListener(refundClickType -> {
            if (refundClickType == RefundDetailsOptimizationLayout.RefundButtonClickListener.REFUND_BUTTON_TYPE_REJECT) {
                //拒绝
                if (isSmallPayment) {
                    RoutersUtils.openForResult("ybmpage://rejectrefund?refundId=" + finalId + "&isSmallPayment=1", 100);
                } else {
                    RoutersUtils.openForResult("ybmpage://rejectrefund?refundId=" + finalId, 100);
                }
            } else {
                boolean isSmallPayment = detail != null && detail.isSmallPayment();
                AlertDialogEx dialog = new AlertDialogEx(getMySelf());
                dialog.setTitle("")
                        .setMessage(isSmallPayment? "确定同意小额赔偿吗？": "确定同意退款吗？")
                        .setCancelButton("取消", null)
                        .setConfirmButton("确定", (dialog1, button) -> {
                            //同意
                            showProgress();
                            refundViewModel.auditOrderRefund("1", finalId, "","", "");
                        }).setMessageGravity(Gravity.CENTER)
                        .setCancelable(false)
                        .setCanceledOnTouchOutside(false)
                        .show();
            }
        });
        refundViewModel.getAuditOrderRefundLiveData().observe(this, baseBean -> {
            dismissProgress();
            getRefundDetail();
        });
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == 100 && resultCode == 100) {
            getRefundDetail();
        }
    }

    @Override
    public int getContentViewId() {
        return R.layout.activity_refund_detail;
    }

    @OnClick({R.id.btn_kefu, R.id.ll_refund_status, R.id.tv_check_sales_commodity, R.id.tv_edit_gathering_id
            , R.id.tv_fill_in_return_logistics, R.id.btn_cancel_the_refund})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.btn_kefu:
                //boolean showChannel = "2".equals(channelCode);
                if (!TextUtils.isEmpty(kfContract)) {
                    RoutersUtils.telKefu(true, kfContract, "呼叫客服：");
                }
                break;
            case R.id.ll_refund_status:
            case R.id.tv_check_sales_commodity:
                String routerUrl = "";
                if (isSmallPayment) {
                    routerUrl = "ybmpage://refunddetailproductlist/" + orderId + "/1";
                } else {
                    routerUrl = "ybmpage://refunddetailproductlist/" + orderId;
                }
                RoutersUtils.open(routerUrl);
                break;
            case R.id.tv_edit_gathering_id://编辑收款账号
                checkRefundOrderState(refundOrderNo, R.id.tv_edit_gathering_id);
                break;
            case R.id.tv_fill_in_return_logistics://填写退货物流
                checkRefundOrderState(refundOrderNo, R.id.tv_fill_in_return_logistics);
                break;
            case R.id.btn_cancel_the_refund://取消退款
                checkRefundOrderState(refundOrderNo, R.id.btn_cancel_the_refund);
                break;
        }
    }

    public void getRefundDetailOptimization(int id) {

        RequestParams params = RequestParams.newBuilder().url(AppNetConfig.REFUND_STATUS_DETAIL).addParam("refundId", id + "").addParam("merchantId", merchant_id).build();
        HttpManager.getInstance().post(params, new BaseResponse<List<RefundDetailCurrentBean>>() {

            @Override
            public void onSuccess(String content, BaseBean<List<RefundDetailCurrentBean>> data, List<RefundDetailCurrentBean> bean) {
                if (data != null && data.isSuccess() && bean != null) {//设置数据
                    setRefundDetailOptimization(bean);
                }
            }

            @Override
            public void onFailure(NetError error) {
                super.onFailure(error);
            }
        });
    }

    public void setRefundDetailOptimization(List<RefundDetailCurrentBean> current) {
        if (rdol == null) {
            return;
        }
        rdol.setItemData(current);
        isSmallPayment = detail.isSmallPayment();
        if (isSmallPayment) {
            tvCheckSalesCommodity.setVisibility(View.GONE);
        }
        if (detail.isSmallPayment()) {
            rdol.setAgreeText("同意");
            rdol.setRejectText("拒绝");
            rdol.setStatusTitle("小额赔偿处理");
            rdol.showStatusItemsView(false);
        }
    }

    /*
     * 退款单状态判断
     * */
    private void checkRefundOrderState(String refundOrderNo, final int id) {

        String merchantid = SpUtil.getMerchantid();
        RequestParams params = new RequestParams();
        params.put("merchantId", merchantid);
        if (!TextUtils.isEmpty(refundOrderNo)) {
            params.put("refundOrderNo", refundOrderNo);
        }
        HttpManager.getInstance().post(AppNetConfig.FIND_REFUND_ORDER_STATUS, params, new BaseResponse<RefundOrderStatusBean>() {

            @Override
            public void onSuccess(String content, BaseBean<RefundOrderStatusBean> obj, RefundOrderStatusBean data) {

                if (obj != null && obj.isSuccess()) {

                    if (data != null) {

                        if (id == R.id.tv_edit_gathering_id) {

                            //是否可以跳转编辑用户账号界面
                            if (data.checkBankState != 0) {
                                if (detail != null && detail.orderRefundBankBusinessDto != null) {
                                    OrderRefundBankDto dto = detail.orderRefundBankBusinessDto;
                                    EditGatheringActivity.getInstance(RefundDetailActivity.this, orderId, dto.id + "", detail.refundFee, detail.refundBalance, dto.bankName, dto.bankCard, dto.owner, dto.cellphone);
//                                    RoutersUtils.open("ybmpage://editgathering/" + orderId + "/" + dto.id + "/" + detail.refundFee + "/" + detail.refundBalance + "/" + dto.bankName + "/" + dto.bankCard + "/" + dto.owner + "/" + dto.cellphone);
                                }
                            } else {
                                showHintDialog(new AlertDialogEx.OnClickListener() {
                                    @Override
                                    public void onClick(AlertDialogEx dialog, int button) {
                                        Intent intent = new Intent(Intent.ACTION_DIAL, Uri.parse("tel:" + RoutersUtils.kefuPhone));
                                        BaseYBMApp.getApp().getCurrActivity().startActivity(intent);
                                    }
                                }, "当前退款单客服已受理，如需修改收款账户请联系客服人员!");
                            }

                        } else if (id == R.id.tv_fill_in_return_logistics) {

                            //是否可以跳转填写退货物流信息
                            if (data.checkExpressState != 0) {
                                if (detail != null && detail.orderRefundExpressBusinessDto != null) {
                                    OrderRefundExpressDto dto = detail.orderRefundExpressBusinessDto;
                                    FillReturnLogisticsActivity.getInstance(RefundDetailActivity.this, orderId, dto.id + "", detail.refundFee, detail.refundBalance, dto.expressName, dto.expressNo, dto.expressEvidence);
//                                    RoutersUtils.open("ybmpage://fillreturnlogistics/" + orderId + "/" + dto.id + "/" + detail.refundFee + "/" + detail.refundBalance + "/" + dto.expressName + "/" + dto.expressNo + "/" + dto.expressEvidence);
                                }
                            } else {
                                showHintDialog(new AlertDialogEx.OnClickListener() {
                                    @Override
                                    public void onClick(AlertDialogEx dialog, int button) {
                                        Intent intent = new Intent(Intent.ACTION_DIAL, Uri.parse("tel:" + RoutersUtils.kefuPhone));
                                        BaseYBMApp.getApp().getCurrActivity().startActivity(intent);
                                    }
                                }, "当前退款单退货仓库已入库，如需提供退货物流信息或有其他问题请联系客服人员!");
                            }

                        } else if (id == R.id.btn_cancel_the_refund) {

                            //是否可以取消退款
                            if (data.cancelRefundOrderState != 0) {
                                showSaveDialog(new AlertDialogEx.OnClickListener() {
                                    @Override
                                    public void onClick(AlertDialogEx dialog, int button) {
                                        cancelRefundOrder(refundOrderNo);
                                    }
                                });
                            } else {
                                showHintDialog(new AlertDialogEx.OnClickListener() {
                                    @Override
                                    public void onClick(AlertDialogEx dialog, int button) {
                                        Intent intent = new Intent(Intent.ACTION_DIAL, Uri.parse("tel:" + RoutersUtils.kefuPhone));
                                        BaseYBMApp.getApp().getCurrActivity().startActivity(intent);
                                    }
                                }, "当前退款单客服已受理，如需取消请联系客服人员!");
                            }

                        }


                    }

                    if (!TextUtils.isEmpty(obj.msg)) {
                        ToastUtils.showShort(obj.msg);
                    }
                }
            }

            @Override
            public void onFailure(NetError error) {

            }
        });

    }

    /*
     * 取消退款单
     * */
    private void cancelRefundOrder(String refundOrderNo) {
        RequestParams params = new RequestParams();
        params.put("merchantId", merchant_id);
        params.put("refundOrderNo", refundOrderNo);
        HttpManager.getInstance().post(AppNetConfig.CANCELREFUNDORDER, params, new BaseResponse<EmptyBean>() {

            @Override
            public void onSuccess(String content, BaseBean<EmptyBean> data, EmptyBean obj) {

                if (data != null && data.isSuccess()) {

                    if (!TextUtils.isEmpty(data.msg)) {
                        ToastUtils.showShort(data.msg);
                    }
                    getRefundDetail();
                }
            }

            @Override
            public void onFailure(NetError error) {

            }
        });

    }

    private void showSaveDialog(AlertDialogEx.OnClickListener listener) {
        AlertDialogEx dialogEx = new AlertDialogEx(this);
        dialogEx.setMessage("确认取消当前退款单吗？").setCancelButton("再想想", new AlertDialogEx.OnClickListener() {
            @Override
            public void onClick(AlertDialogEx dialog, int button) {
                dialog.dismiss();
            }
        }).setCancelable(false).setConfirmButton("确认取消", listener).setCanceledOnTouchOutside(false).setTitle(null).show();
    }

    private void showHintDialog(AlertDialogEx.OnClickListener listener, String title) {
        AlertDialogEx dialogEx = new AlertDialogEx(this);
        dialogEx.setMessage(title).setCancelButton("我知道了", new AlertDialogEx.OnClickListener() {
            @Override
            public void onClick(AlertDialogEx dialog, int button) {
                dialog.dismiss();
                getRefundDetail();
            }
        }).setCancelable(false).setConfirmButton("联系客服", listener).setCanceledOnTouchOutside(false).setTitle(null).show();
    }

    private void getRefundDetail() {
        RequestParams params = RequestParams.newBuilder().url(AppNetConfig.REFUND_DETAIL).addParam("id", orderId).addParam("merchantId", merchant_id).build();
        HttpManager.getInstance().post(params, new BaseResponse<RefundDetailBean>() {

            @Override
            public void onSuccess(String content, BaseBean<RefundDetailBean> data, RefundDetailBean bean) {
                if (data != null && data.isSuccess() && bean != null) {//设置数据
                    dismissProgress();
                    RefundDetailActivity.this.detail = bean.refundOrder;
                    setRefundDetail(bean.refundOrder);
                    if (bean.refundOrder != null) {
                        getRefundDetailOptimization(bean.refundOrder.id);
                        if (!TextUtils.isEmpty(bean.refundOrder.kfContact)) {
                            btnKefu.setText("客服电话:" + bean.refundOrder.kfContact);
                            btnKefu.setVisibility(View.VISIBLE);
                            kfContract = bean.refundOrder.kfContact;
                        }

                        if (bean.refundOrder.showRefundOrderBtn() && !isSmallPayment){ //后台说的 后台控制不显示按钮
                            tvCheckSalesCommodity.setVisibility(View.VISIBLE);
                        }else {
                            tvCheckSalesCommodity.setVisibility(View.GONE);
                        }
                    }
                } else {
                    finish();
                }
            }

            @Override
            public void onFailure(NetError error) {
                super.onFailure(error);
                finish();
            }
        });

    }

    /**
     * 设置退款地址
     * @param detail
     */
    private void setRefundAddressInfo(RefundDetail detail) {
        //设置退款地址
        tvAddressName.setText("收货人：" + detail.refundOrderName);
        tvAddress.setText("收货地址：" + detail.refundOrderAdress);
        tvAddressPhone.setText("" + detail.refundOrderPhone);
        tvDeliveryInstructions.setText("快递说明：" + detail.refundOrderExpressDelivery);
        if (TextUtils.isEmpty(detail.refundOrderAdress)){
            llCardRefundInfo.setVisibility(View.GONE);
        }
    }

    /**
     * 设置收款信息
     * @param detail
     */
    private void setCollectionInfo(RefundDetail detail) {
        if (detail.orderRefundBankBusinessDto != null) {
            OrderRefundBankDto dto = detail.orderRefundBankBusinessDto;
            if (!TextUtils.isEmpty(dto.bankName)) {
                tvOpeningBank.setText("开户行及支行：" + dto.bankName);
            }
            if (!TextUtils.isEmpty(dto.bankCard)) {
                tvCreditCardNumbers.setText("银行卡号：" + dto.bankCard);
            }
            if (!TextUtils.isEmpty(dto.owner)) {
                tvAccountHolder.setText("开户人：" + dto.owner);
            }
            if (!TextUtils.isEmpty(dto.cellphone)) {
                tvPhone.setText("联系电话：" + dto.cellphone);
            }
        }
    }

    /**
     * 退货物流信息
     * @param detail
     */
    private void setRefundExpress(RefundDetail detail) {
        if (detail.orderRefundExpressBusinessDto != null) {
            OrderRefundExpressDto dRo = detail.orderRefundExpressBusinessDto;
            if (!TextUtils.isEmpty(dRo.expressName)) {
                tvExpressageName.setText("快递名称：" + dRo.expressName);
                tvFillInReturnLogistics.setText("编辑退货物流");
            } else {
                tvFillInReturnLogistics.setText("填写退货物流");
            }
            if (!TextUtils.isEmpty(dRo.expressNo)) {
                tvExpressageNumber.setText("快递单号：" + dRo.expressNo);
            }

            //运单截图
            ArrayList expressEvidence = new ArrayList();
            if (!TextUtils.isEmpty(dRo.expressEvidence) && dRo.expressEvidence.length() > 2) {
                expressEvidence.add(dRo.expressEvidence);
            }

            imageFragment = new AddImageFragment();
            arg = AddImageFragment.getBundle2Me(1, true, false, true);
            arg.putBoolean("allowe_add", false);
            arg.putCharSequence("hint", "");
            arg.putStringArrayList(AddImageFragment.EXTRA_DATA, expressEvidence);
            imageFragment.setArguments(arg);
            getSupportFragmentManager().beginTransaction().replace(R.id.fragment_expressage, imageFragment).commitNowAllowingStateLoss();
            //运单信息显示或者隐藏
            llExpressage.setVisibility(TextUtils.isEmpty(dRo.expressName) || TextUtils.isEmpty(dRo.expressNo) ? View.GONE : View.VISIBLE);
        }
        tvFillInReturnLogistics.setVisibility(detail.showExpressState == 0 ? View.GONE : View.VISIBLE);
    }

    /**
     * 设置开户信息
     * @param detail
     */
    private void setOpenAccountInfo(RefundDetail detail) {
        if (detail.payType == 3) {
            //如果运单展示的话开户的按钮就不展示了，后台告诉我的
            if (detail.showExpressState == 0) {
                tvEditGatheringId.setVisibility(detail.showBankState == 0 ? View.GONE : View.VISIBLE);
            } else {
                tvEditGatheringId.setVisibility(View.GONE);
            }
            if (detail.orderRefundBankBusinessDto != null) {
                OrderRefundBankDto dto = detail.orderRefundBankBusinessDto;
                llBank.setVisibility(TextUtils.isEmpty(dto.bankName) || TextUtils.isEmpty(dto.bankCard) ? View.GONE : View.VISIBLE);
            } else {
                llBank.setVisibility(View.GONE);
            }
        } else {
            tvEditGatheringId.setVisibility(View.GONE);
            llBank.setVisibility(View.GONE);
        }
    }

    /**
     * 设置结果数据
     * @param detail
     */
    private void setResultData(RefundDetail detail) {
        if (detail.auditState != 0) {
            tvMoney.setText("退款金额：" + "¥" + detail.cashPayAmount);
            tvBalance.setText("退回余额：" + "¥" + detail.refundBalance);
            if (!TextUtils.isEmpty(detail.virtualGold)) {
                tvVirtualMoney.setVisibility(View.VISIBLE);
                tvVirtualMoney.setText("退回购物金：" + "￥" + detail.virtualGold);
            }
            tvResultTitle.setVisibility(View.VISIBLE);
            llRefundResult.setVisibility(View.VISIBLE);
            space.setVisibility(View.VISIBLE);
            if (TextUtils.isEmpty(detail.refundRouteTips)) {
                tvRefundRoute.setVisibility(View.GONE);
            } else {
                tvRefundRoute.setVisibility(View.VISIBLE);
                tvRefundRoute.setText("退款去向：" + detail.refundRouteTips);
            }
            if (detail.auditState == 1) {
                tvStatus.setEnabled(true);
                tvRefundTime.setText("退款时间：" + detail.auditRefundTime);

                tvCloseReason.setVisibility(View.GONE);
                tvRefundTime.setVisibility(View.VISIBLE);
            } else {
                tvStatus.setActivated(true);
                //tvRefundTime.setText("拒绝时间：" + detail.auditRefundTime);
                tvCloseReason.setText("关闭原因：" + detail.closeReason);
                tvRefundTime.setText("关闭时间：" + detail.closeTime);

                tvCloseReason.setVisibility(!TextUtils.isEmpty(detail.closeReason) ? View.VISIBLE : View.GONE);
                tvRefundTime.setVisibility(!TextUtils.isEmpty(detail.closeTime) ? View.VISIBLE : View.GONE);
            }
            //额外赔偿
            setIndemnityMoney(tvIndemnityMoney, detail);
        } else {
            llRefundResult.setVisibility(View.GONE);
            tvResultTitle.setVisibility(View.GONE);
            space.setVisibility(View.GONE);
            tvStatus.setEnabled(false);
            tvStatus.setActivated(false);
        }
    }

    /**
     * 设置额外金额
     * @param tv
     * @param detail
     */
    private void setIndemnityMoney(TextView tv, RefundDetail detail) {
        if (detail.isIndemnityMoney()) {
            tv.setVisibility(View.VISIBLE);
            tv.setText("额外赔偿：" + detail.indemnityMoney + " (赔偿至购物金)");
        } else {
            tv.setVisibility(View.GONE);
        }
    }

    /**
     * 设置申请信息
     * @param detail
     */
    private void setApplyInfo(RefundDetail detail) {
        //额外赔偿
        setIndemnityMoney(tvIndemnityMoney2, detail);

        tvApplyTime.setText("申请时间：" + detail.applyRefundTime);
        tvReason.setText("退款原因：" + detail.refundReason);

        if (detail.freightAmount > 0){
            tvApplyMoney.setText("退款金额：" + "¥" + detail.refundMoney +"("+"含运费: "+"¥"+detail.freightAmount+")");
        }else {
            tvApplyMoney.setText("退款金额：" + "¥" + detail.refundMoney);
        }

        if (TextUtils.isEmpty(detail.refundExplain)) {
            detail.refundExplain = "";
        }
        tvInfo.setText("退款说明：" + detail.refundExplain);
        if (detail.imgList != null && !detail.imgList.isEmpty()) {
            ArrayList list = new ArrayList(5);
            if (detail.imgList != null) {
                list.addAll(detail.imgList);
            }
            imageFragment = new AddImageFragment();
            arg = AddImageFragment.getBundle2Me(9, true, false, true);
            arg.putBoolean("allowe_add", false);
            arg.putCharSequence("hint", "");
            arg.putStringArrayList(AddImageFragment.EXTRA_DATA, list);
            imageFragment.setArguments(arg);
            getSupportFragmentManager().beginTransaction().replace(R.id.fragment, imageFragment).commitNowAllowingStateLoss();
        }

        try {
            if (detail.billType == 1 || detail.billType == 4) {
                //电子普通发票 或 增值税电子专用发票
                llInvoiceExpress.setVisibility(View.GONE);
            } else {
                //快递信息
                if (TextUtils.isEmpty(detail.invoiceDeliveryInfo)) {
                    tvExpressTypeCopyBtn.setVisibility(View.GONE);
                    tvExpressType.setText("-");
                } else {
                    tvExpressType.setText(detail.invoiceDeliveryInfo);
                    tvExpressTypeCopyBtn.setOnClickListener(v -> {
                        YbmCommand.setClipboardMsg(detail.invoiceDeliveryInfo);
                        ToastUtils.showShort("复制成功");
                    });
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        //打款凭证
        if (detail.payType == 1) {
            //在线支付
            llPayCredential.setVisibility(View.GONE);
            return;
        }
        llPayCredential.setVisibility(View.VISIBLE);
        if (TextUtils.isEmpty(detail.refundPayEvidence)) {
            ivPayCredential.setVisibility(View.GONE);
            ivPayCredentialSymbol.setVisibility(View.VISIBLE);
        } else {
            ivPayCredentialSymbol.setVisibility(View.GONE);
            llPayCredential.setVisibility(View.VISIBLE);
            ImageUtil.load(
                    this,
                    ImageUtil.getImageUrl(detail.refundPayEvidence),
                    ivPayCredential
            );
            ivPayCredential.setOnClickListener(new View.OnClickListener() {
                @SuppressLint("UnsafeIntentLaunch")
                @Override
                public void onClick(View v) {
                    startActivity(BigPicActivity.getIntent(RefundDetailActivity.this
                            , new String[]{ImageUtil.getImageUrl(detail.refundPayEvidence)}, 0, null, "大图"));
                }
            });
        }
    }

    private void setRefundDetail(RefundDetail detail) {
        if (detail == null) {
            return;
        }
        //设置退款地址
        setRefundAddressInfo(detail);
        //收款账户信息
        setCollectionInfo(detail);
        //退货物流信息
        setRefundExpress(detail);
        //退款按钮显示或者隐藏
        btnCancelTheRefund.setVisibility(detail.cancelRefundOrderState == 0 ? View.GONE : View.VISIBLE);
        //开户信息显示或者隐藏
        setOpenAccountInfo(detail);
        //物流信息显示或者隐藏
        tvFillInReturnLogistics.setVisibility(detail.showExpressState == 0 ? View.GONE : View.VISIBLE);
        //设置退款状态
        tvStatus.setText(detail.auditStatusName);
        //设置结果数据
        setResultData(detail);
        //设置申请信息
        setApplyInfo(detail);
        setSmallPayment();
    }

    private String getChannelCodeStr(String code) {
        if (TextUtils.isEmpty(code)) {
            return "药帮忙";
        }
        switch (code) {
            default:
            case "1":
                return "药帮忙";
            case "2":
                return "宜块钱";
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        getRefundDetail();
    }

    /**
     * 设置小额赔偿数据
     */
    private void setSmallPayment() {
        if (detail != null && detail.isSmallPayment()) {
            tvSmallPaymentApply.setVisibility(View.VISIBLE);
            tvSmallPaymentApply.setText("小额赔偿:￥" + detail.indemnityMoney + "(赔偿至购物金)");
            tvSmallDetail.setVisibility(View.VISIBLE);
            tvSmallDetail.setText("小额赔偿:￥" + detail.indemnityMoney + "(赔偿至购物金)");
            tvApplyMoney.setVisibility(View.GONE);
            llPayCredential.setVisibility(View.GONE);
            llCardRefundInfo.setVisibility(View.GONE);
            llRefundCertificate.setVisibility(View.GONE);
            if (detail.auditState == 1 || detail.auditState == -1) {
                tvMoney.setVisibility(View.GONE);
                tvBalance.setVisibility(View.GONE);
                tvVirtualMoney.setVisibility(View.GONE);
                tvRefundRoute.setVisibility(View.GONE);
                tvRefundTime.setVisibility(View.GONE);
                tvCloseReason.setVisibility(View.GONE);
                llInvoiceExpress.setVisibility(View.GONE);
                llPayCredential.setVisibility(View.GONE);
                tvIndemnityMoney.setVisibility(View.GONE);
            }
        }
    }

}
