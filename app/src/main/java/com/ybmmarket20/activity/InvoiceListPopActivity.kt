package com.ybmmarket20.activity

import android.app.Dialog
import android.graphics.Color
import android.text.InputType
import android.text.TextUtils
import android.view.View
import android.view.inputmethod.InputMethodManager
import android.widget.EditText
import android.widget.TextView
import androidx.activity.viewModels
import androidx.core.content.ContextCompat
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import com.github.mzule.activityrouter.annotation.Router
import com.ybm.app.view.WrapLinearLayoutManager
import com.ybmmarket20.R
import com.ybmmarket20.activity.afterSales.activity.TIPS_TYPE_INVOICE
import com.ybmmarket20.adapter.InvoiceListPopAdapter
import com.ybmmarket20.bean.CheckOrderDetailBean
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.common.util.ToastUtils
import com.ybmmarket20.utils.DialogUtil
import com.ybmmarket20.utils.DialogUtil.DialogPerfectClickListener
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.UiUtils
import com.ybmmarket20.utils.analysis.XyyIoUtil
import com.ybmmarket20.viewmodel.InvoiceInfoViewModel
import com.ybmmarket20.viewmodel.OrderDetailViewModel
import kotlinx.android.synthetic.main.activity_invoice_list_pop.*
import kotlinx.android.synthetic.main.common_header_items.*

/**
 * pop发票
 */
@Router("invocelistpopactivity", "invoicelist/:orderid/:number/:time/:paytime")
class InvoiceListPopActivity: BaseActivity() {

    lateinit var mViewModel: InvoiceInfoViewModel
    private val afterSaleViewModel: OrderDetailViewModel by viewModels()

    private var emailTemp = ""

    private var mOrderNo = ""

    private var mOrderId = ""

    private var mAptitudeStatus = ""

    private var mOrgId = ""
    private var mOrgName = ""
    private var mAfterSalesNo = ""

    override fun getContentViewId(): Int = R.layout.activity_invoice_list_pop

    override fun initData() {
        mOrderId = intent.getStringExtra("orderid")?: ""
        mAptitudeStatus = intent.getStringExtra("aptitudeStatus")?: ""
        mOrgId = intent.getStringExtra("orgId")?: ""
        mOrgName = intent.getStringExtra("orgName")?: ""
        mAfterSalesNo = intent.getStringExtra("afterSalesNo")?: ""
        setTitle("发票")
        findViewById<TextView>(R.id.tv).text = "发票暂未生成，请稍后再试"
        mViewModel = ViewModelProvider(this).get(InvoiceInfoViewModel::class.java)
        afterSaleViewModel.afterSaleTipsLiveData.observe(this) {
            RoutersUtils.open(it)
            dismissProgress()
        }
        mViewModel.invoiceInfoLiveData.observe(this, Observer {
            dismissProgress()
            if (!it.isSuccess) {
                empty.visibility = View.VISIBLE
                return@Observer
            }
            dismissProgress()
            tv_right.visibility = if (it.data.data.result.isNotEmpty()) View.VISIBLE else View.GONE
            tv_right.text = "下载全部"
            tv_right.setTextColor(ContextCompat.getColor(this, R.color.color_01b377))
            tv_right.setOnClickListener {
//              sendEmail();
                RoutersUtils.open("ybmpage://sendinvoicebyemail?orderId=$mOrderId")
            }
            rv.layoutManager = WrapLinearLayoutManager(this)
            rv.adapter = InvoiceListPopAdapter(it.data.data.result, it.data.data.orderNo)
            if (it.data.data.result.isEmpty()) {
                empty.visibility = View.VISIBLE
            }
        })
        mViewModel.initDataLiveData.observe(this, Observer {
            tv_order_time.text = it.createOrderTime
            tv_receive_time.text = it.payTime
            tv_order_no.text = it.orderNo
            tv_type.text = it.billInfo
            mOrderNo = it.orderNo?: ""
            mViewModel.getInvoiceInfo(it.merchantId?: "", it.orderId?: "")
        })
        showProgress()
        mViewModel.initWithIntent(intent)
        setAptitudeInfo()
    }

    private fun setAptitudeInfo() {
        rtvAptitude.text = if (mAptitudeStatus == "1") "申请发票售后" else "查看发票售后"
        rtvAptitude.setOnClickListener {
            val orderDetail = CheckOrderDetailBean().apply {
                orderNo = mOrderNo
                orgId = mOrgId
                origName = mOrgName
            }
            if (mAptitudeStatus == "1") {
                showProgress()
                afterSaleViewModel.getAfterSalesInfo(orderDetail, TIPS_TYPE_INVOICE)
            } else {
                RoutersUtils.open("ybmpage://aftersalesdetail?afterSalesNo=$mAfterSalesNo")
            }
        }
    }

    /**
     * 发送邮件
     */
    private fun sendMail() {
        val title = "请确认邮箱地址"
        val confirm = "确认"
        val cancel = "取消"

        DialogUtil.sendEmailDialog(
            this,
            InputType.TYPE_CLASS_TEXT,
            title,
            confirm,
            cancel,
            object : DialogPerfectClickListener {
                override fun confirm(dialog: Dialog, etName: EditText, content: String) {
                    emailTemp = content
                    if (TextUtils.isEmpty(content)) {
                        hideSoftInput(etName)
                        ToastUtils.showShort("邮箱为空，请重新输入")
                        return
                    }
                    if (!UiUtils.isEmail(content)) {
                        hideSoftInput(etName)
                        ToastUtils.showShort("邮箱格式不正确，请检查是否存在空格等特殊字符，重新输入")
                        return
                    }
                    if (dialog != null && dialog.isShowing) {
                        dialog.dismiss()
                    }
                    hideSoftInput(etName)
                    sendEmailConfirm(content)
                }

                override fun cancel(dialog: Dialog) {
                    if (dialog != null && dialog.isShowing) {
                        dialog.dismiss()
                    }
                    hideSoftInput(tv_order_no)
                }

                override fun showSoftInput(dialog: Dialog, view: View) {
                    val imm = getSystemService(INPUT_METHOD_SERVICE) as InputMethodManager
                    imm.showSoftInput(view, InputMethodManager.SHOW_IMPLICIT)
                }
            })
    }

    /**
     * 确认发送
     */
    private fun sendEmailConfirm(content: String) {
        showProgress()
        mViewModel.sendMailInfo.observe(this, Observer {
            dismissProgress()
            ToastUtils.showLong(it)
        })
        mViewModel.sendMail(content)
        XyyIoUtil.track("page_OrderInvoiceDetails_allDownload", hashMapOf("order_no" to mOrderNo))
    }
}