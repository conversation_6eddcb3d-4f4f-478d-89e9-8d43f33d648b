package com.ybmmarket20.activity

import android.graphics.Color
import android.os.Bundle
import android.os.PersistableBundle
import android.view.View
import android.view.WindowManager
import android.widget.TextView
import androidx.activity.viewModels
import androidx.core.widget.addTextChangedListener
import androidx.lifecycle.SavedStateViewModelFactory
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybmmarket20.R
import com.ybmmarket20.bean.CompanyLicenseBean
import com.ybmmarket20.bean.CompanyLicenseItem
import com.ybmmarket20.bean.RefundProductListBean
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.viewmodel.LicenseRequirementViewModel
import com.ybmmarket20.viewmodel.PaymentGoodsViewModel
import com.ybmmarket20.viewmodel.viewstore.GlobalViewModelStore
import kotlinx.android.synthetic.main.activity_license_requirement_with_order.clConfirmLayout
import kotlinx.android.synthetic.main.activity_license_requirement_with_order.etLicense
import kotlinx.android.synthetic.main.activity_license_requirement_with_order.flLicense
import kotlinx.android.synthetic.main.activity_license_requirement_with_order.tvCompanyLicenseTitle
import kotlinx.android.synthetic.main.activity_license_requirement_with_order.ivEditClose
import kotlinx.android.synthetic.main.activity_license_requirement_with_order.rtvConfirm
import kotlinx.android.synthetic.main.activity_license_requirement_with_order.rvLicense
import kotlinx.android.synthetic.main.activity_license_requirement_with_order.tvShopName

/**
 * 随货资质需求
 */
abstract class AbstractLicenseRequirementWithOrderActivity: BaseActivity() {
    val licenseRequirementViewModel: LicenseRequirementViewModel by viewModels()
    val paymentGoodsViewModel : PaymentGoodsViewModel by lazy {
        ViewModelProvider(
            GlobalViewModelStore.get().getGlobalViewModelStore(),
            SavedStateViewModelFactory(application, this)
        ).get(PaymentGoodsViewModel::class.java)
    }
    var shopCode: String? = null

    override fun getContentViewId(): Int = R.layout.activity_license_requirement_with_order

    override fun onCreate(savedInstanceState: Bundle?, persistentState: PersistableBundle?) {
        window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_HIDDEN)
        super.onCreate(savedInstanceState, persistentState)
    }

    override fun initData() {
        setTitle("随货资质需求")
        shopCode = intent.getStringExtra("shopCode")
        etLicense.addTextChangedListener {
            if ((it?.length ?: 0) > 0) {
                ivEditClose.visibility = View.VISIBLE
            } else {
                ivEditClose.visibility = View.GONE
            }
            paymentGoodsViewModel.getGoodsList(shopCode, it?.toString())
        }
        ivEditClose.setOnClickListener {
            etLicense.setText("")
        }
        rtvConfirm.setOnClickListener {
            paymentGoodsViewModel.generateLicenseResult(shopCode)
            finish()
        }
        rvLicense.layoutManager = LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false)
        clConfirmLayout.visibility = if (isShowConfirmButton()) View.VISIBLE else View.GONE
        setObserver()
        getCompanyLicense()
    }

    /**
     * 获取企业资质数据
     */
    abstract fun getCompanyLicense()

    /**
     * 获取adapter
     */
    abstract fun getGoodsLicenseAdapter(list: MutableList<RefundProductListBean>): YBMBaseAdapter<RefundProductListBean>

    /**
     * 企业相关资质是否可以点击
     */
    abstract fun canClickCompanyLicense(): Boolean

    /**
     * 是否展示提交按钮
     */
    abstract fun isShowConfirmButton(): Boolean

    open fun onLicenseRequirement(companyLicenseBean: CompanyLicenseBean?){}

    private fun setObserver() {
        licenseRequirementViewModel.licenseRequirementLiveData.observe(this) {
            dismissProgress()
            if (it.isSuccess) {
                val companyLicenseBean = it.data
                tvShopName.text = paymentGoodsViewModel.staticShopName
                handleCompanyLicense(companyLicenseBean.items)
                onLicenseRequirement(it.data)
            }
        }

        paymentGoodsViewModel.goodsListLiveData.observe(this) {
            val adapter = getGoodsLicenseAdapter(it)
            adapter.setEmptyView(this, R.layout.layout_empty_view, R.drawable.icon_empty, "未在当前订单找到相关商品～")
            rvLicense.adapter = adapter
        }
    }

    private fun handleCompanyLicense(items: MutableList<CompanyLicenseItem>?) {
        if (items.isNullOrEmpty()) {
            flLicense.visibility = View.GONE
            tvCompanyLicenseTitle.visibility = View.GONE
            return
        }
        items.map {
            val itemView = View.inflate(this, R.layout.item_company_license_tag, null)
            val tv = itemView.findViewById(R.id.tv) as TextView
            tv.text = it.itemName
            handleCompanyLicenseState(it, tv)
            //是否可点击
            if (canClickCompanyLicense()) {
                itemView.setOnClickListener {_ ->
                    paymentGoodsViewModel.changeCompanyLicenseState(shopCode, it)
                    handleCompanyLicenseState(it, tv)
                }
            }
            itemView
        }.forEach {
            flLicense.addView(it)
        }
    }

    private fun handleCompanyLicenseState(item: CompanyLicenseItem?, itemView: TextView) {
        val isContainsLicense = paymentGoodsViewModel.isSelectCompanyLicense(shopCode, item)
        itemView.isActivated = isContainsLicense
        itemView.setTextColor(Color.parseColor(if(isContainsLicense) "#00B377" else "#292933"))
    }

}