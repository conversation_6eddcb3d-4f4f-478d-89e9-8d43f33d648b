package com.ybmmarket20.activity;

import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.text.Html;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.github.mzule.activityrouter.annotation.Router;
import com.google.gson.Gson;
import com.ybm.app.bean.NetError;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.EmptyBean;
import com.ybmmarket20.bean.RefundCardInfoBean;
import com.ybmmarket20.bean.RefundMoneyBean;
import com.ybmmarket20.common.AlertDialogEx;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.util.ConvertUtils;
import com.ybmmarket20.common.util.ToastUtils;
import com.ybmmarket20.common.widget.RoundEditText;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.fragments.AddImage3Fragment;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.utils.RoutersUtils;
import com.ybmmarket20.utils.StringUtil;
import com.ybmmarket20.utils.UiUtils;
import com.ybmmarket20.view.MyImageSpan;
import com.ybmmarket20.view.RefundReasonPopWindow;

import java.util.ArrayList;
import java.util.List;

import butterknife.Bind;
import butterknife.OnClick;

/**
 * 申请退款
 * type 2全部退款 1 部分退款
 */
@Router({"applyrefund", "applyrefund/:type/:orderId/:param", "applyrefund/:type/:orderId", "applyrefund/:type/:orderId/:param/:payType/:contactor/:mobile"})
public class ApplyRefundActivity extends BaseActivity {

    @Bind(R.id.tv_amount)
    TextView tvAmount;
    @Bind(R.id.tv_balance)
    TextView tvBalance;
    @Bind(R.id.tv_info)
    EditText tvInfo;
    @Bind(R.id.btn_ok)
    Button btnOk;
    @Bind(R.id.tv_reason)
    TextView tvReason;
    @Bind(R.id.tv_hint)
    TextView tvHint;
    @Bind(R.id.et_bank_name)
    RoundEditText etBankName;
    @Bind(R.id.et_bank_card)
    RoundEditText etBankCard;
    @Bind(R.id.et_owner)
    RoundEditText etOwner;
    @Bind(R.id.et_cell_phone)
    RoundEditText etCellPhone;
    @Bind(R.id.ll_bank_info)
    LinearLayout llBankInfo;
    @Bind(R.id.tv_virtual_money)
    TextView tvVirtualMoney;
    @Bind(R.id.v_line_virtual_money)
    View vLineVirtualMoney;
    @Bind(R.id.ll_virtual_money)
    LinearLayout llVirtualMoney;

    @Bind(R.id.ll_back_money)
    LinearLayout llBackMoney;
    @Bind(R.id.tv_refund_tip)
    TextView tvRefundTip;

    protected AddImage3Fragment imageFragment;

    private int reasonId = -1;
    private String type;   // 退款或退货类型：1 部分 2 全部
    private String param;
    private String orderId;
    private String payType;
    private String contactor;
    private String mobile;
    private String status;
    private String orderNo;//订单编号
    private String refundMode;
    private int billType;

    private boolean hasRefundFreight;
    private String reasonStr = "";
    protected Bundle arg;
    private RefundReasonPopWindow popWindow;
    private String refundReasonStr;

    @Override
    protected void initData() {
        type = getIntent().getStringExtra("type");
        orderId = getIntent().getStringExtra("orderId");
        param = getIntent().getStringExtra("param");
        payType = getIntent().getStringExtra("payType");
        contactor = getIntent().getStringExtra("contactor");
        mobile = getIntent().getStringExtra("mobile");
        status = getIntent().getStringExtra("status");
        orderNo = getIntent().getStringExtra("orderNo");
        refundMode = getIntent().getStringExtra("refundMode");
        billType = getIntent().getIntExtra("billType", 0);
        hasRefundFreight = getIntent().getBooleanExtra("hasRefundFreight", false);

        if (TextUtils.isEmpty(type)) {
            ToastUtils.showShort("参数错误");
            finish();
            return;
        }
        setTitle("申请退款");
//        if ("3".equals(payType)) {
//            llBankInfo.setVisibility(View.VISIBLE);
//            getAccountInfo();
//        } else {
//            llBankInfo.setVisibility(View.GONE);
//        }
        if (!TextUtils.isEmpty(contactor)) {
            etOwner.setText(contactor);
        }
        if (!TextUtils.isEmpty(mobile)) {
            etCellPhone.setText(mobile);
        }
        tvAmount.setText(Html.fromHtml(String.format(getResources().getString(R.string.text_refund_money), "　¥0.0")));
        getRefundMoney();
        imageFragment = new AddImage3Fragment();
        arg = AddImage3Fragment.getBundle2Me(9, true, false, true);
        arg.putBoolean("allowe_add", true);
        arg.putCharSequence("hint", "");
        tvInfo.setEnabled(true);

        imageFragment.setArguments(arg);
        getSupportFragmentManager().beginTransaction().replace(R.id.fragment, imageFragment).commit();

        SpannableStringBuilder shopName = getName(getResources().getString(R.string.refund_optimize_hint1), R.drawable.icon_refund_optimize_hint);
        if (!TextUtils.isEmpty(shopName)) tvHint.setText(shopName);
    }

    private SpannableStringBuilder getName(String shopName, Integer icons) {
        SpannableStringBuilder spannableString = new SpannableStringBuilder(shopName);
        Drawable drawable = getResources().getDrawable(icons);
        drawable.setBounds(0, 0, ConvertUtils.dp2px(15), ConvertUtils.dp2px(15));
        MyImageSpan imageSpan = new MyImageSpan(drawable, 2);
        //占个位置
        spannableString.insert(0, "-");
        spannableString.setSpan(imageSpan, 0, 1, Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
        ForegroundColorSpan foregroundColorSpan = new ForegroundColorSpan(Color.parseColor("#fa6400"));
        spannableString.setSpan(foregroundColorSpan, shopName.indexOf("1"), shopName.indexOf("2"), Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
        return spannableString;
    }

    @Override
    public int getContentViewId() {
        return R.layout.activity_apply_refund;
    }

    @OnClick({R.id.btn_ok, R.id.tv_reason})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.btn_ok:
                if (billType == 3 || billType == 4) {
                    //纸质普通发票 或 增值税专用发票
                    new AlertDialogEx(this)
                            .setConfirmButton("我知道了", (AlertDialogEx.OnClickListener) (dialog, button) -> applyRefund())
                            .setTitle("")
                            .setMessage("订单为纸质发票,请将发票随退货一并寄回")
                            .show();
                } else {
                    applyRefund();
                }
                break;
            case R.id.tv_reason:
                if (tvReason == null) {
                    return;
                }

                String reasonStr = tvReason.getText().toString();
                if (!reasonStr.equals("") && !reasonStr.equals("请选择退款原因") && hasRefundFreight) {
                    //退运费情况：后台已经返回退运费文案 如果为空再去弹窗获取  否则不让点击
                    return;
                }

                popWindow = new RefundReasonPopWindow(orderNo, status, this, param);
                popWindow.setOnRefundReasonSelectListener((parentContent, childContent, desc, uploadTips) -> {
                    tvReason.setText(childContent == null ? parentContent : childContent);
                    if (TextUtils.equals(parentContent, "其他")) {
                        tvInfo.setText(desc);
                    }
                    refundReasonStr = childContent != null ? parentContent + "-" + childContent : parentContent;
                    tvRefundTip.setVisibility(View.VISIBLE);
                    if (!TextUtils.isEmpty(uploadTips)) {
                        tvRefundTip.setText(uploadTips);
                    } else {
                        tvRefundTip.setVisibility(View.GONE);
                    }
                });
                popWindow.show(tvReason);
                break;
        }
    }


    private void applyRefund() {
        if (tvReason == null) {
            return;
        }
        if (tvInfo.getText().length() <= 0) {
            tvInfo.setText("");
        }

        String etBankNameStr = etBankName.getText().toString().trim();
        String etBankCardStr = etBankCard.getText().toString().trim();
        String etOwnerStr = etOwner.getText().toString().trim();
        String etCellPhoneStr = etCellPhone.getText().toString().trim();

        if (TextUtils.isEmpty(refundReasonStr)) {
            UiUtils.toast("请选择退款原因");
            return;
        }
        if (imageFragment.confirm()) {
            btnOk.setEnabled(false);
            List<String> list = imageFragment.getFileNameList();
            if (list == null) {
                list = new ArrayList<>();
            }

            if (list.size() < 3) {
                list.add("");
                list.add("");
                list.add("");
            }
            showProgress();
            RequestParams params = RequestParams.newBuilder().url(AppNetConfig.REFUND)
                    .addParam("orderId", orderId).addParam("refundType", type)
                    .addParam("refundReason", refundReasonStr)
                    .addParam("bankName", etBankNameStr)
                    .addParam("bankCard", etBankCardStr)
                    .addParam("owner", etOwnerStr)
                    .addParam("cellphone", etCellPhoneStr)
                    .addParam("refundExplain", tvInfo.getText().toString())
                    .build();
            if (refundMode != null) {
                params.put("applyOrderRefund", refundMode);
            }
            params.put("evidence1", list.get(0));
            params.put("evidence2", list.get(1));
            params.put("evidence3", list.get(2));
            Gson gson = new Gson();
            List<String> imgListResult = new ArrayList<>();
            for (String s : list) {
                if (!TextUtils.isEmpty(s)) imgListResult.add(s);
            }
            params.put("imgList", gson.toJson(imgListResult));
            if (type.equals("1")) {
                params.put("refundOrderDetailList", param);
            }
            HttpManager.getInstance().post(params, new BaseResponse<EmptyBean>() {

                @Override
                public void onSuccess(String content, BaseBean<EmptyBean> data, EmptyBean baseBean) {
                    dismissProgress();
                    if (btnOk == null) {
                        return;
                    }
                    btnOk.setEnabled(true);
                    if (data != null && data.isSuccess()) {
//                        RoutersUtils.open("ybmpage://refundlist/" + orderId);
                        RoutersUtils.open("ybmpage://refundoraftersales?orderNo=" + orderNo);
                        finish();
                    }
                }

                @Override
                public void onFailure(NetError error) {
                    dismissProgress();
                    btnOk.setEnabled(true);
                    super.onFailure(error);
                }
            });
        }

    }


    //查看订单详情
    private void getRefundMoney() {
        String refundFreightType;
        if (hasRefundFreight) refundFreightType = "1";
        else refundFreightType = "0";
        RequestParams params = new RequestParams();
        params.put("merchantId", merchant_id);
        params.put("refundType", type);
        params.put("orderId", orderId);
        params.put("refundFreightType", refundFreightType);
        if (type.equals("1")) {
            params.put("refundOrderDetailList", param);
        }

        HttpManager.getInstance().post(AppNetConfig.REFUND_MONEY, params, new BaseResponse<RefundMoneyBean>() {

            @Override
            public void onSuccess(String content, BaseBean<RefundMoneyBean> data, RefundMoneyBean bean) {
                if (data != null && data.isSuccess() && bean != null) {
                    RefundMoneyBean.RefundOrderBean refundOrderBean = bean.refundOrder;
                    String fee, balance, virtualMoney;
                    if (refundOrderBean != null) {
                        if (refundOrderBean.virtualGold != null) {
                            virtualMoney = "¥" + refundOrderBean.virtualGold;
                        } else {
                            virtualMoney = "¥0.0";
                        }
                        if (refundOrderBean.refundBalance != null) {
                            balance = "¥" + refundOrderBean.refundBalance;
                        } else {
                            balance = "";
                        }
                        if (refundOrderBean.cashPayAmount != null) {
                            fee = "￥" + refundOrderBean.cashPayAmount;
                        } else {
                            fee = "";
                        }
                    } else {
                        fee = "¥" + bean.data;
                        balance = "";
                        virtualMoney = "";
                    }
                    try {
                        if (!virtualMoney.isEmpty() && Float.valueOf(refundOrderBean.virtualGold) != 0) {
                            vLineVirtualMoney.setVisibility(View.VISIBLE);
                            llVirtualMoney.setVisibility(View.VISIBLE);
                        } else {
                            llVirtualMoney.setVisibility(View.GONE);
                        }
                        if (balance.isEmpty() && Float.valueOf(balance) != 0){
                            llBackMoney.setVisibility(View.VISIBLE);
                        } else {
                            llBackMoney.setVisibility(View.GONE);
                        }

                    } catch (Exception e) {
                        e.printStackTrace();
                        llVirtualMoney.setVisibility(View.GONE);
                        llBackMoney.setVisibility(View.GONE);
                    }
                    tvAmount.setText(Html.fromHtml(String.format(getResources().getString(R.string.text_refund_money), fee)));
                    tvBalance.setText(Html.fromHtml(String.format(getResources().getString(R.string.text_refund_balance), balance)));
                    tvVirtualMoney.setText(Html.fromHtml(String.format(getResources().getString(R.string.text_refund_virtual), virtualMoney)));
                    if (refundOrderBean.refundReason != null) {
                        tvReason.setText(refundOrderBean.refundReason);
                        refundReasonStr = refundOrderBean.refundReason;
                    }
                }
            }
        });
    }

    /**
     * 线下转账状态带入上次使用的信息
     */
    private void getAccountInfo() {
        showProgress();
        RequestParams params = new RequestParams();
        params.put("merchantId", merchant_id);
        HttpManager.getInstance().post(AppNetConfig.REFUND_CARD_INFO, params, new BaseResponse<RefundCardInfoBean>() {
            @Override
            public void onSuccess(String content, BaseBean<RefundCardInfoBean> data, RefundCardInfoBean bean) {
                dismissProgress();
                if (data != null && data.isSuccess() && bean != null) {
                    etBankName.setText(bean.getBankName());
                    etBankCard.setText(bean.getBankCard());
                    if (!StringUtil.isEmpty(bean.getOwner())) {
                        etOwner.setText(bean.getOwner());
                    }
                    if (!StringUtil.isEmpty(bean.getCellphone())) {
                        etCellPhone.setText(bean.getCellphone());
                    }
                }
            }

            @Override
            public void onFailure(NetError error) {
                dismissProgress();
            }
        });
    }


}
