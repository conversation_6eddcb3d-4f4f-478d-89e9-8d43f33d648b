package com.ybmmarket20.activity;

import android.view.View;
import android.widget.RelativeLayout;

import com.github.mzule.activityrouter.annotation.Router;
import com.ybm.app.adapter.YBMBaseAdapter;
import com.ybm.app.adapter.YBMBaseHolder;
import com.ybm.app.bean.NetError;
import com.ybm.app.utils.BugUtil;
import com.ybm.app.view.CommonRecyclerView;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.CheckOrderListBean;
import com.ybmmarket20.bean.CheckOrderRowsBean;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.widget.RoundTextView;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.utils.RoutersUtils;
import com.ybmmarket20.utils.SpUtil;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;

import butterknife.Bind;
import butterknife.OnClick;

/*
 * 保驾护航申请记录
 * */
@Router({"protectpricerecord"})
public class ProtectPriceRecordActivity extends BaseActivity {

    @Bind(R.id.rl_protect_price)
    RelativeLayout rlProtectPrice;
    @Bind(R.id.list)
    CommonRecyclerView list;
    @Bind(R.id.btn_protect_price)
    RoundTextView btnProtectPrice;

    private YBMBaseAdapter adapter;
    private List<CheckOrderRowsBean> rows = new ArrayList<>();
    private int bottom = com.ybm.app.utils.UiUtils.dp2px(6);
    private SimpleDateFormat dateFormat;
    private int pageSize = 10;
    private int pager = 0;

    @Override
    protected int getContentViewId() {
        return R.layout.activity_protect_price_record;
    }

    @Override
    protected void initData() {

        setTitle("保价护航申请记录");

        dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault());
        adapter = new YBMBaseAdapter<CheckOrderRowsBean>(R.layout.item_protect_price, rows) {

            @Override
            protected void bindItemView(YBMBaseHolder baseViewHolder, CheckOrderRowsBean bean) {

                String format = dateFormat.format(new Date(bean.createTime));

                baseViewHolder.setImageUrl(R.id.iv_order, AppNetConfig.LORD_IMAGE + bean.imageUrl, R.drawable.jiazaitu_min)
                        .setText(R.id.tv_order_no, "订单编号：" + bean.orderNo)
                        .setText(R.id.tv_order_total, bean.productName)
                        .setText(R.id.tv_order_time, "提交时间：" + format)
                        .getConvertView().setOnClickListener(new View.OnClickListener() {
                            @Override
                            public void onClick(View v) {
                                RoutersUtils.open("ybmpage://applyforrecorddetails/" + bean.id);
                            }
                });

            }

        };
        list.setListener(new CommonRecyclerView.Listener() {
            @Override
            public void onRefresh() {
                getData(pager = 0);
            }

            @Override
            public void onLoadMore() {
                getData(pager);
            }
        });
        list.setEnabled(false);
        list.setAdapter(adapter);
        list.setEmptyView(R.layout.layout_empty_view, R.drawable.icon_empty, "暂无申请记录");
        adapter.openLoadMore(10, true);
//        list.addItemDecoration(new RecyclerView.ItemDecoration() {
//            @Override
//            public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
//                outRect.bottom = bottom;
//            }
//        });

    }

    /*
     * 获取保驾护航申请记录数据
     * */
    public void getData(final int page) {
        String merchantid = SpUtil.getMerchantid();
        RequestParams params = new RequestParams();
        params.put("merchantId", merchantid);
        params.put("limit", String.valueOf(pageSize));
        params.put("offset", String.valueOf(String.valueOf(page)));
        HttpManager.getInstance().post(AppNetConfig.ESCORT_INDEX, params, new BaseResponse<CheckOrderListBean>() {

            @Override
            public void onSuccess(String content, BaseBean<CheckOrderListBean> brandBean, CheckOrderListBean rowsBeans) {
                completion();
                if (brandBean != null) {
                    if (brandBean.isSuccess()) {

                        if (rowsBeans != null) {

                            if (rowsBeans.getRows() != null && rowsBeans.getRows().size() > 0) {
                                if (page <= 0) {
                                    ProtectPriceRecordActivity.this.pager = 1;
                                } else {
                                    ProtectPriceRecordActivity.this.pager++;
                                }
                            }
                            if (page <= 0) {

                                if (rows == null) {
                                    rows = new ArrayList<>();
                                }

                                if (rows.size() <= 0 && rowsBeans.getRows() != null) {
                                    rows.addAll(rowsBeans.getRows());
                                } else {
                                    if (rowsBeans.getRows() == null || rowsBeans.getRows().isEmpty()) {

                                    } else {

                                        rows.addAll(0, rowsBeans.getRows());
                                    }
                                }
                                adapter.setNewData(rows);
                                adapter.notifyDataChangedAfterLoadMore(rows.size() >= pageSize);
                            } else {
                                if (rowsBeans.getRows() == null || rowsBeans.getRows().size() <= 0) {
                                    adapter.notifyDataChangedAfterLoadMore(false);
                                } else {
                                    rows.addAll(rowsBeans.getRows());
                                    adapter.setNewData(rows);
                                    adapter.notifyDataChangedAfterLoadMore(rowsBeans.getRows().size() >= pageSize);
                                }
                            }
                        }
                    } else {
                        adapter.setNewData(rows);
                    }
                }
            }

            @Override
            public void onFailure(NetError error) {
                completion();
                if (list != null) {
                    list.postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            try {
                                if (list != null) {
                                    adapter.setNewData(rows);
                                }
                            } catch (Throwable e) {
                                BugUtil.sendBug(e);
                            }
                        }
                    }, 300);
                }
            }
        });

    }

    private void completion() {
        if (list != null) {
            try {
                list.setRefreshing(false);
            } catch (Throwable e) {
                BugUtil.sendBug(e);
            }
        }
    }

    @OnClick({R.id.rl_protect_price, R.id.btn_protect_price})
    public void clickTab(View view) {
        switch (view.getId()) {
            case R.id.rl_protect_price:

                RoutersUtils.open("ybmpage://commonh5activity?cache=0&url=https://app-v4.ybm100.com/static/xyyvue/temp/baojiahuhangrule.html");
                break;
            case R.id.btn_protect_price:
                RoutersUtils.open("ybmpage://selectapplyfororder");
                break;
        }
    }

}
