package com.ybmmarket20.activity;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.os.Process;
import android.text.InputFilter;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextPaint;
import android.text.TextUtils;
import android.text.method.HideReturnsTransformationMethod;
import android.text.method.PasswordTransformationMethod;
import android.text.style.ClickableSpan;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.widget.TextView;

import androidx.activity.result.ActivityResult;
import androidx.activity.result.ActivityResultCallback;
import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.NonNull;
import androidx.appcompat.widget.AppCompatCheckBox;
import androidx.appcompat.widget.AppCompatEditText;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.lifecycle.ViewModelProvider;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.analysys.ANSAutoPageTracker;
import com.apkfuns.logutils.LogUtils;
import com.github.mzule.activityrouter.annotation.Router;
import com.google.android.material.textfield.TextInputLayout;
import com.google.gson.Gson;
import com.tbruyelle.rxpermissions2.RxPermissions;
import com.ybmmarket20.BuildConfig;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.LoginInfo;
import com.ybmmarket20.bean.WechatOauthInfo;
import com.ybmmarket20.bugly.BuglyLogger;
import com.ybmmarket20.common.AlertDialogEx;
import com.ybmmarket20.common.AppUtilKt;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.JGTrackManager;
import com.ybmmarket20.common.JGTrackTopLevelKt;
import com.ybmmarket20.common.YBMAppLike;
import com.ybmmarket20.common.util.ToastUtils;
import com.ybmmarket20.constant.IntentCanst;
import com.ybmmarket20.utils.DialogUtil;
import com.ybmmarket20.utils.RoutersUtils;
import com.ybmmarket20.utils.ShareUtil;
import com.ybmmarket20.utils.SpUtil;
import com.ybmmarket20.utils.SpanUtils;
import com.ybmmarket20.utils.UiUtils;
import com.ybmmarket20.utils.YBMWxUtil;
import com.ybmmarket20.utils.YbmPushUtil;
import com.ybmmarket20.view.ButtonObserver;
import com.ybmmarket20.view.CheckPrivacyView;
import com.ybmmarket20.view.CommonDialogLayout;
import com.ybmmarket20.viewmodel.AccountInfoViewModel;
import com.ybmmarketkotlin.activity.BindAccountActivity;
import com.ybmmarketkotlin.activity.LoginVertificationActivity;
import com.ybmmarketkotlin.utils.ChCrypto;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import butterknife.Bind;
import butterknife.OnClick;
import kotlin.Unit;
import kotlin.jvm.functions.Function0;

/**
 * 进入登陆页面全部activity 都要关闭。目前通过本场广播实现
 * 测试账号  密码为 123456
 * ***********
 * ***********
 * ***********
 * ***********
 * ***********
 * ***********
 * ***********
 * ***********
 * ***********
 * ***********
 * ***********
 * ***********
 * ***********
 * ***********
 * ***********
 * ***********
 * ***********
 * ***********
 * ***********
 * ***********
 * ***********
 * ***********
 * ***********
 * ***********
 * ***********
 * ***********
 * ***********
 * ***********
 * ***********
 * ***********
 */
@Router("login")
public class LoginActivity extends BaseActivity implements ANSAutoPageTracker {

    public static String LOGIN = "login";
    // 定义一个变量，来标识是否退出
    private static boolean isExit = false;
    private static Handler mHandler = new Handler(Looper.getMainLooper()) {

        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            isExit = false;
        }
    };
    @Bind(R.id.registEvent)
    AppCompatTextView registEvent;
    @Bind(R.id.login_et1)
    AppCompatEditText mLoginEt1;
    @Bind(R.id.login_et2)
    AppCompatEditText mLoginEt2;
    @Bind(R.id.login_btn)
    ButtonObserver mLoginBtn;
    @Bind(R.id.login_forget)
    TextView mLoginForget;
    @Bind(R.id.login_user_name_wrapper)
    TextInputLayout mLoginUserNameWrapper;
    @Bind(R.id.login_password_wrapper)
    TextInputLayout mLoginPasswordWrapper;
    @Bind(R.id.checkPrivacy)
    CheckPrivacyView checkPrivacyView;
    @Bind(R.id.cb_forget_passwd)
    AppCompatCheckBox cbForgetPasswd;
    private LoginInfo loginData;
    private WechatOauthInfo oauthInfo;
    private int startActivityCount;
    private AccountInfoViewModel loginViewModel;
    //勾选协议时间
    private String checkTime;
    private AlertDialogEx dialogPrivacy;
    private ActivityResultLauncher launcher = registerForActivityResult(new ActivityResultContracts.StartActivityForResult(), new ActivityResultCallback<ActivityResult>() {
        @Override
        public void onActivityResult(ActivityResult result) {
            if (result.getData() != null && result.getData().getBooleanExtra(IntentCanst.LOGINVERTIFICATIONRESULT, false)) {
                // LogUtils.e("xyd 接收到验证页得回传信息");
                processLoginData(loginData);
            }
        }
    });
    private ActivityResultLauncher launcherBind = registerForActivityResult(new ActivityResultContracts.StartActivityForResult(), new ActivityResultCallback<ActivityResult>() {
        @Override
        public void onActivityResult(ActivityResult result) {
            if (result.getData() != null && result.getData().getBooleanExtra(IntentCanst.BIND_ACCOUNT, false)) {
                // 绑定成功登录
                ToastUtils.showShort("绑定成功");
                showProgress();
                loginViewModel.loginByWechat(oauthInfo.getAccess_token(),oauthInfo.getOpenid(),oauthInfo.getUnionid());
            }
        }
    });

    @Override
    protected void initData() {
        SpUtil.clearMerchantid();
        loginViewModel = new ViewModelProvider(this).get(AccountInfoViewModel.class);

        initObserver();
        checkPrivacyView.setCheckPrivacyTextForLogin(
                new Function0<Unit>() {
                    @Override
                    public Unit invoke() {
                        loginViewModel.getLoginAgreement(2);
                        return null;
                    }
                }, new Function0<Unit>() {
                    @Override
                    public Unit invoke() {
                        loginViewModel.getLoginAgreement(1);
                        return null;
                    }
                }
        );
        checkPrivacyView.setCheck(false);
        registEvent.setOnClickListener(v -> {
            hideSoftInput();
            gotoAtivity(RegisterV2Activity.class, null);
            JGTrackTopLevelKt.jgTrackLoginBtnClick(this, "注册");
        });
        LocalBroadcastManager.getInstance(getApplicationContext()).sendBroadcast(new Intent(IntentCanst.ACTION_LOGOUT));
        // 设置的过滤器 只能输入数字
        mLoginEt1.setFilters(new InputFilter[]{
                new InputFilter.LengthFilter(11), // 设置最大长度为11
                new InputFilter() {
                    @Override
                    public CharSequence filter(CharSequence source, int start, int end, Spanned dest, int dstart, int dend) {
                        StringBuilder builder = new StringBuilder();
                        for (int i = start; i < end; i++) {
                            char c = source.charAt(i);
                            if (Character.isDigit(c)) {
                                builder.append(c);
                            }
                        }
                        return builder.toString();
                    }
                }
        });
        mLoginBtn.observer(mLoginEt1, mLoginEt2);
        mLoginBtn.setOnItemClickListener(isFlag -> mLoginBtn.setEnabled(isFlag && checkPrivacyView.isChecked()));
        checkPrivacyView.setCheckBoxListener(isChecked -> {
            // 获取当前时间戳并转换为 yyyy-MM-dd HH:mm:ss 格式
            long currentTimeMillis = System.currentTimeMillis();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            checkTime = sdf.format(new Date(currentTimeMillis));
            mLoginBtn.checkButtonStatus();
            JGTrackTopLevelKt.jgTrackLoginBtnClick(this, "勾选协议");
        });
        cbForgetPasswd.setOnCheckedChangeListener((buttonView, isChecked) -> {
            int selectionPosition = mLoginEt2.getSelectionStart();
            mLoginEt2.setTransformationMethod(isChecked ? HideReturnsTransformationMethod.getInstance() : PasswordTransformationMethod.getInstance());
            mLoginEt2.setSelection(selectionPosition);
        });

        if (BuildConfig.DEBUG || BuildConfig.APPLICATION_ID.equals("com.ybmmarket20.debug")) {
            TextView debug = findViewById(R.id.change_to_debug);
            debug.setVisibility(View.VISIBLE);
            debug.setOnClickListener(v -> startActivity(new Intent(this, DebugActivity.class)));
        }
        //推送注册id为空 再去获取注册
        if (TextUtils.isEmpty(SpUtil.getPushToken())) {
            YbmPushUtil.registerPushtoken(getMySelf());
        }
        loginViewModel.getLoginAgreement(0);
    }

    @Override
    protected void onStart() {
        super.onStart();
        startActivityCount = YBMAppLike.createdActivityCount;
    }

    @Override
    protected void onStop() {
        super.onStop();
        if (!isFinishing() && YBMAppLike.createdActivityCount == startActivityCount) {
            ToastUtils.showShort("药帮忙App已进入后台");
        }
    }

    @Override
    public int getContentViewId() {
        return R.layout.activity_login;
    }

    @OnClick({R.id.login_forget, R.id.login_btn,R.id.lyWechatLogin})
    public void clickTab(View view) {
        switch (view.getId()) {
            case R.id.login_btn://登录
                hideSoftInput();
                loginReq();
                break;
            case R.id.login_forget://忘记密码
                hideSoftInput();
                gotoAtivity(ForgetpwdActivity.class, null);
                break;
            case R.id.lyWechatLogin:// 微信授权登录
                YBMWxUtil.getInstance().ybmCall(new YBMWxUtil.SDKCallBack() {
                    @Override
                    public void sdkOauthBack(String code) {
                    // 1. 授权后，使用code请求接口获取accessToken
                    BuglyLogger.INSTANCE.e("微信授权",code,null);
                    if(!LoginActivity.this.isFinishing() && !LoginActivity.this.isDestroyed()){
                        showProgress();
                        loginViewModel.loginByWechat(code);
                    }
                    }
                });
                if(!ShareUtil.isWeChatInstall()){
                    return;
                }
                if(!checkPrivacyView.isChecked()){
                    showDialogPrivacy();
                    return;
                }
                YBMWxUtil.getInstance().sendOauth();
                break;
        }
    }

    private void showDialogPrivacy(){
        SpannableStringBuilder message = new SpanUtils()
                .append("请阅读并同意")
                .setFontSize(14,true)
                .setForegroundColor(UiUtils.getColor(R.color.text_color_666666))
                .append("《药帮忙隐私政策》")
                .setFontSize(14,true)
                .setClickSpan(new ClickableSpan() {
                    @Override
                    public void onClick(@NonNull View widget) {
                        loginViewModel.getLoginAgreement(1);
                    }

                    @Override
                    public void updateDrawState(@NonNull TextPaint ds) {
                        super.updateDrawState(ds);
                        //设置文件颜色
                        ds.setColor(UiUtils.getColor(R.color.color_00b955));
                        //设置下划线
                        ds.setUnderlineText(false);
                    }
                })
                .append("《药帮忙用户服务协议》")
                .setFontSize(14,true)
                .setClickSpan(new ClickableSpan() {
                    @Override
                    public void onClick(@NonNull View widget) {
                        loginViewModel.getLoginAgreement(2);
                    }

                    @Override
                    public void updateDrawState(@NonNull TextPaint ds) {
                        super.updateDrawState(ds);
                        //设置文件颜色
                        ds.setColor(UiUtils.getColor(R.color.color_00b955));
                        //设置下划线
                        ds.setUnderlineText(false);
                    }
                })
                .create();
        dialogPrivacy = new AlertDialogEx(this)
                .setTitle("")
                .setMessage(message)
                .setMessageGravity(Gravity.CENTER)
                .setCanceledOnTouchOutside(false)
                .setCancelButton("我再想想", (dialog, button) -> {
                    dialog.dismiss();
                })
                .setCancelButtonTextColor(UiUtils.getColor(R.color.text_color_333333))
                .setConfirmButton("同意", (dialog, button) -> {
                    checkPrivacyView.setCheck(true);
                    dialog.dismiss();
                    YBMWxUtil.getInstance().sendOauth();
                })
                .setCorner();
        dialogPrivacy.show();
    }

    private void initObserver() {
        loginViewModel.getOauthLiveData().observe(this, wechatOauthInfoBaseBean -> {
            dismissProgress();
            if(wechatOauthInfoBaseBean.isSuccess()){
                oauthInfo = wechatOauthInfoBaseBean.data;
                launcherBind.launch(BindAccountActivity.Companion.getStartIntent(LoginActivity.this,wechatOauthInfoBaseBean.data.getAccess_token(),wechatOauthInfoBaseBean.data.getOpenid(),wechatOauthInfoBaseBean.data.getUnionid(),"",checkTime));
            }
        });
        loginViewModel.getLoginLiveData().observe(this, loginInfoBaseBean -> {
            dismissProgress();
            if (loginInfoBaseBean.isSuccess()) {
                loginData = loginInfoBaseBean.data;
                if (loginData.isCrawler()) {
                    launcher.launch(LoginVertificationActivity.Companion.getStartIntent(LoginActivity.this, TextUtils.isEmpty(loginData.getMobileNo())? ChCrypto.aesDecrypt(loginData.getAccount()) : loginData.getMobileNo(), loginData.getMerchantId()));
                } else {
                    processLoginData(loginData);
                }
                saveLoginAgreement();
            }
        });
    }

    private void saveLoginAgreement() {
        final String login_phone = mLoginEt1.getText().toString().trim();
        HashMap<String, String> map = new HashMap<>();
        String json = new Gson().toJson(loginViewModel.getAgreementsList());
        map.put("agreements", json);
        map.put("mobile", login_phone);
        map.put("checkTime", checkTime);
        map.put("operateType", "2");
        loginViewModel.saveLoginAgreement(map);
    }

    private void loginReq() {
        final String login_phone = mLoginEt1.getText().toString().trim();
        final String login_password = mLoginEt2.getText().toString().trim();
        if (TextUtils.isEmpty(login_phone)) {
            ToastUtils.showShort("手机号不能为空");
            return;
        }

        if (!UiUtils.isMobileNO(login_phone)) {
            ToastUtils.showShort(R.string.validate_mobile_error);

            return;
        }
        if (TextUtils.isEmpty(login_password)) {
            ToastUtils.showShort("请输入密码");
            return;
        }

        showProgress();
        loginViewModel.login(login_phone, ChCrypto.aesEncrypt(login_password), false, "");

//        RxPermissions rxPermissions = new RxPermissions(LoginActivity.this);
//        if (rxPermissions.isGranted(android.Manifest.permission.READ_PHONE_STATE)) {
//            getDeviceIdRootPermissions(rxPermissions, login_phone, login_password);
//        } else {
//            PermissionDialogUtil.showPermissionInfoDialog(LoginActivity.this,
//                    "药帮忙App需要申请手机/电话权限，用于读取设备识别码（IMEI、IMSI和MAC），防止账号被盗",
//                    () -> getDeviceIdRootPermissions(rxPermissions, login_phone, login_password));
//        }
    }

    @SuppressLint("CheckResult")
    private void getDeviceIdRootPermissions(RxPermissions rxPermissions, String login_phone, String login_password) {
        rxPermissions.request(
                android.Manifest.permission.READ_PHONE_STATE
        ).subscribe(granted -> {
            showProgress();
            loginViewModel.login(login_phone, ChCrypto.aesEncrypt(login_password), false, "");
        }, throwable -> {
            showProgress();
            loginViewModel.login(login_phone, ChCrypto.aesEncrypt(login_password), false, "");
        });

    }

    private void processLoginData(LoginInfo loginData) {
        LocalBroadcastManager.getInstance(YBMAppLike.getAppContext()).sendBroadcast(new Intent(IntentCanst.ACTION_AD_COLLECT_HINT_POP));
        DialogUtil.showCommonStatus(CommonDialogLayout.CommonTip.success, "登录成功");
        //登陆广播
        LocalBroadcastManager.getInstance(getApplicationContext()).sendBroadcast(new Intent(IntentCanst.ACTION_LOGIN));
        LocalBroadcastManager.getInstance(getApplicationContext()).sendBroadcast(new Intent(IntentCanst.ACTION_CART_STYLE));
        LocalBroadcastManager.getInstance(getApplicationContext()).sendBroadcast(new Intent(IntentCanst.ACTION_CHANGE_HOME_LAYOUT_TYPE));
        //更新购物车数量广播
        LocalBroadcastManager.getInstance(getApplicationContext()).sendBroadcast(new Intent(IntentCanst.ACTION_SHOPNUMBER));
        if (loginData != null && loginData.getLoginSucceedUrl() != null) {
            RoutersUtils.open(loginData.getLoginSucceedUrl());
            if (loginData.getLoginSucceedUrl().startsWith("ybmpage://main")
                    || loginData.getLoginSucceedUrl().startsWith("ybmpage://selectloginshop")
            ) finish();
        }
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            exit();
            return false;
        }
        return super.onKeyDown(keyCode, event);
    }

    private void exit() {
        if (!isExit) {
            isExit = true;
            ToastUtils.showShort("再按一次退出程序");
            // 利用handler延迟发送更改状态信息
            mHandler.sendEmptyMessageDelayed(0, 2000);
        } else {
            LogUtils.d("退出应用");
            mHandler.removeCallbacksAndMessages(null);
            Process.killProcess(Process.myPid());
            System.exit(0);
        }
    }

    @Override
    public boolean dispatchTouchEvent(MotionEvent ev) {
        isScroll(ev);
        return super.dispatchTouchEvent(ev);
    }

    @Override
    public Map<String, Object> registerPageProperties() {
        HashMap<String, Object> map = new HashMap<>();
        map.put(JGTrackManager.FIELD.FIELD_PAGE_ID, JGTrackManager.TrackLogin.PAGE_ID);
        map.put(JGTrackManager.FIELD.FIELD_TITLE, JGTrackManager.TrackLogin.TITLE);
        return map;
    }

    @Override
    public String registerPageUrl() {
        return AppUtilKt.getFullClassName(LoginActivity.this);
    }
}
