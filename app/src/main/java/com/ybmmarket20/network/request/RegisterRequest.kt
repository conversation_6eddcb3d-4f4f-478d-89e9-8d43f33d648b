package com.ybmmarket20.network.request

import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.EmptyBean
import com.ybmmarket20.bean.ImgCodeBean
import com.ybmmarket20.bean.RegisterBeanV2
import com.ybmmarket20.more_account.data.initWithException
import com.ybmmarket20.network.NetworkService
import retrofit2.http.Field
import retrofit2.http.FormUrlEncoded
import retrofit2.http.POST

/**
 * 注册V2
 */
interface IRegisterServiceV2 {
    @FormUrlEncoded
    @POST("v2/register")
    suspend fun register(@Field("mobile")mobile: String, @Field("password")password: String, @Field("photoCode")photoCode: String, @Field("code")code: String, @Field("contactName")userName: String): BaseBean<RegisterBeanV2>

    @POST("getVerifCode")
    suspend fun getImageCode(): BaseBean<ImgCodeBean>

    @FormUrlEncoded
    @POST("sendRegisterVerificationCode")
    suspend fun getPhoneCode(@Field("mobileNumber")mobileNumber: String, @Field("verifCode")verifyCode: String, @Field("code")code: String): BaseBean<EmptyBean>
}

class RegisterRequestV2 {

    /**
     * 注册
     */
    suspend fun register(mobile: String, password: String, photoCode: String, code: String, userName: String): BaseBean<RegisterBeanV2> = try {
        NetworkService.instance.mRetrofit.create(IRegisterServiceV2::class.java).register(mobile, password, photoCode, code, userName)
    } catch (e: Exception) {
        BaseBean<RegisterBeanV2>().initWithException(e)
    }

    /**
     * 获取图片二维码
     */
    suspend fun getImageCode(): BaseBean<ImgCodeBean> = try {
        NetworkService.instance.mRetrofit.create(IRegisterServiceV2::class.java).getImageCode()
    } catch (e: Exception) {
        BaseBean<ImgCodeBean>().initWithException(e)
    }

    /**
     * 获取手机验证码
     */
    suspend fun getPhoneCode(mobileNumber: String, verifyCode: String, code: String): BaseBean<EmptyBean> = try {
        NetworkService.instance.mRetrofit.create(IRegisterServiceV2::class.java).getPhoneCode(mobileNumber, verifyCode, code)
    } catch (e: Exception) {
        BaseBean<EmptyBean>().initWithException(e)
    }

}