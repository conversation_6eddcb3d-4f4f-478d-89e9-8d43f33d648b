package com.ybmmarket20.utils

import android.content.Context
import android.content.SharedPreferences

/**
 * 搜索引导管理器
 * 用于管理搜索页面各种引导的显示状态
 */
object SearchGuideManager {

    // SharedPreferences文件名，用于存储搜索引导相关的状态数据
    private const val PREF_NAME = "search_guide_prefs"
    // 组合购引导是否展示过
    private const val KEY_GROUP_BUY_GUIDE_SHOW_FLAG = "group_buy_guide_show_flag_"

    private fun getPreferences(context: Context): SharedPreferences {
        return context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE)
    }

    /**
     * 获取当前用户的唯一标识
     * 用于区分不同用户账号的引导显示状态，确保每个账号独立管理
     *
     * @return 用户唯一标识，优先使用merchantId，如果为空则使用默认值
     */
    private fun getCurrentUserKey(): String {
        val accountId = SpUtil.getAccountId()
        return if (accountId.isNotEmpty()) accountId else "default_user"
    }

    /**
     * 组合购引导隐藏后记录
     */
    fun markGroupBuyGuideShown(context:Context){
        val userKey = getCurrentUserKey()
        val prefs = getPreferences(context)
        prefs.edit()
            .putInt(KEY_GROUP_BUY_GUIDE_SHOW_FLAG+userKey,1)
            .apply()
    }

    /**
     * 组合购引导是否需要展示
     */
    fun shouldShowGroupBuyGuide(context: Context): Boolean {
        val userKey = getCurrentUserKey()
        val prefs = getPreferences(context)
        val shownFlag = prefs.getInt(KEY_GROUP_BUY_GUIDE_SHOW_FLAG + userKey, 0)
        return 1 != shownFlag
    }
}
