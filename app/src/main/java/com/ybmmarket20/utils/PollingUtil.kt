package com.ybmmarket20.utils

import android.os.Handler
import android.os.HandlerThread
import android.os.Message

/**
 * <AUTHOR>
 * 轮询工具
 */
class PollingUtil {

    private val pollingThread: HandlerThread by lazy {
        HandlerThread("polling")
    }

    private val pollingHandler: Handler by lazy {
        object: Handler(pollingThread.looper) {
            override fun handleMessage(msg: Message) {
                super.handleMessage(msg)

            }
        }
    }

    /**
     * 开始轮询
     */
    fun startPolling() {
        pollingThread.start()
    }

    /**
     * 停止轮询
     */
    fun stopPolling() {

    }

}

/**
 * 轮询监听
 */
interface PollingListener {

}