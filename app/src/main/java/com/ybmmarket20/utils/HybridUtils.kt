package com.ybmmarket20.utils

import android.text.TextUtils
import androidx.lifecycle.SavedStateViewModelFactory
import androidx.lifecycle.ViewModelProvider
import com.google.gson.Gson
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.CartGoodsInfo
import com.ybmmarket20.bean.H5JumpSuiXinPinBean
import com.ybmmarket20.bean.SettleBean
import com.ybmmarket20.bean.SpellGroupGoodsItem
import com.ybmmarket20.bean.SpellGroupRecommendGoodsBean
import com.ybmmarket20.common.AlertDialogEx
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.common.BaseResponse
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.network.HttpManager
import com.ybmmarket20.viewmodel.CURRENT_PAGE_DEFAULT
import com.ybmmarket20.viewmodel.SpellGroupRecommendGoodsViewModel
import com.ybmmarket20.viewmodel.viewstore.GlobalViewModelStore

fun handleSpellGroupJump(rowsJson: String, activity: BaseActivity, btnType: Int) {
    // FIXME: 李江 待修改处理 加入QT埋点内容
    val mViewModel = ViewModelProvider(
        GlobalViewModelStore.get().getGlobalViewModelStore(),
        SavedStateViewModelFactory(activity.application, activity)
    ).get(SpellGroupRecommendGoodsViewModel::class.java)
    try {
        if (!TextUtils.isEmpty(rowsJson)) {
            val h5JumpSuiXinPinBean = Gson().fromJson(rowsJson, H5JumpSuiXinPinBean::class.java)
            mViewModel.apply {
                shopCode = h5JumpSuiXinPinBean.shopCode?: ""
                orgId = h5JumpSuiXinPinBean.orgId?: ""
                mainGoodsSkuId = "${h5JumpSuiXinPinBean.id}"
                isThirdCompany = h5JumpSuiXinPinBean.isThirdCompany
                mainGoodsCount = ""
                mainGoodsPId = h5JumpSuiXinPinBean.pId
                registerJumpType(btnType)

                val mainCartGoodsInfo = SpellGroupGoodsItem()
                mainCartGoodsInfo.goodsSelectedCount = h5JumpSuiXinPinBean!!.qty
                mainGoodsCount = h5JumpSuiXinPinBean.qty.toString()
                mainCartGoodsInfo.skuId = h5JumpSuiXinPinBean.id.toString()
                mainCartGoodsInfo.goodsUrl = h5JumpSuiXinPinBean.imageUrl
                mainCartGoodsInfo.goodsTitle = h5JumpSuiXinPinBean.showName
                mainCartGoodsInfo.goodsUnit = h5JumpSuiXinPinBean.productUnit
                mainCartGoodsInfo.goodsPrice = h5JumpSuiXinPinBean.price
                mainCartGoodsInfo.totalPrice = h5JumpSuiXinPinBean.totalAmount
                val spellGroupRecommendGoodsBean = SpellGroupRecommendGoodsBean(
                    ArrayList(),
                    mainCartGoodsInfo,
                    HashMap(),
                    CartGoodsInfo(),
                    false,
                )
                updateData(spellGroupRecommendGoodsBean, false)
            }
            checkoutGotoSettle(h5JumpSuiXinPinBean, activity)
        }
    } catch (e: Exception) {
        e.printStackTrace()
    }
}

private fun getRefreshParams(rowsBean: H5JumpSuiXinPinBean): RequestParams {
    val merchantId = SpUtil.getMerchantid()
    val params = RequestParams()
    params.put("merchantId", merchantId)
    params.put("skuId", rowsBean.id.toString())
    params.put("productNum", rowsBean.qty.toString())
    return params
}

/**
 * 校验订单是否可以跳转到待确认订单页
 */
private fun checkoutGotoSettle(rowsBean: H5JumpSuiXinPinBean, activity: BaseActivity) {
    val isSupportSuiXinPin = true
    HttpManager.getInstance()
        .post(
            AppNetConfig.ORDER_V1_GROUPPURCHASEPRESETTLE,
            getRefreshParams(rowsBean),
            object : BaseResponse<SettleBean?>() {
                override fun onSuccess(
                    content: String?,
                    data: BaseBean<SettleBean?>?,
                    bean: SettleBean?
                ) {
                    if (data != null) {
                        if (data.isSuccess) {
                            if (bean!!.isShowDialog == 1) {
                                AlertDialogEx(activity)
                                    .setMessage("您的资质已过期，请及时更新，以免影响发货")
                                    .setCanceledOnTouchOutside(false)
                                    .setConfirmButton(
                                        "我知道了"
                                    ) { _: AlertDialogEx?, _: Int ->
                                        RoutersUtils.open(getJumpUrl(rowsBean, activity))
                                    }.show()
                            } else {
                                RoutersUtils.open(getJumpUrl(rowsBean, activity))
                            }
                        }
                    }
                }
            })
}

/**
 * 获取跳转路由
 * @param productDetail
 * @param bean
 */
private fun getJumpUrl(rowsBean: H5JumpSuiXinPinBean, activity: BaseActivity): String? {
    val viewModel = ViewModelProvider(
        GlobalViewModelStore.get().getGlobalViewModelStore(),
        SavedStateViewModelFactory(
            activity.application,
            activity
        )
    ).get(SpellGroupRecommendGoodsViewModel::class.java)
    val isSupportSuiXinPin = true
    //如果支持随心拼则添加主品
    if (isSupportSuiXinPin) {
        val mainCartGoodsInfo = SpellGroupGoodsItem()
        mainCartGoodsInfo.goodsSelectedCount = rowsBean.qty
        viewModel.mainGoodsCount = rowsBean.qty.toString() + ""
        mainCartGoodsInfo.skuId = rowsBean.id.toString() + ""
        mainCartGoodsInfo.goodsUrl = rowsBean.imageUrl
        mainCartGoodsInfo.goodsTitle = rowsBean.showName
        mainCartGoodsInfo.goodsUnit = rowsBean.productUnit
        mainCartGoodsInfo.goodsPrice = rowsBean.price
        mainCartGoodsInfo.totalPrice = rowsBean.totalAmount
        val spellGroupRecommendGoodsBean = SpellGroupRecommendGoodsBean(
            ArrayList(),
            mainCartGoodsInfo,
            HashMap(),
            CartGoodsInfo(),
            false
        )
        viewModel.updateData(spellGroupRecommendGoodsBean, false)
    }
    val params: MutableMap<String, String> = hashMapOf()
    params["tranNo"] = rowsBean.tranNo?: ""
    params["skuId"] = rowsBean.id.toString()
    params["productNum"] = rowsBean.qty.toString()
    if (rowsBean.isWhole == 1) {
        params["isPgby"] = "1"
    }
    params["spType"] = rowsBean.sptype ?: ""
    params["spId"] = rowsBean.spid ?: ""
    params["sId"] = rowsBean.sid ?: ""
    params["isSupportOldSxp"] = "1"
    return viewModel.getJumpRouter(CURRENT_PAGE_DEFAULT, params)
}