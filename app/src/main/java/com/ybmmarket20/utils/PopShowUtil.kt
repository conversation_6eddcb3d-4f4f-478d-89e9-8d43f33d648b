package com.ybmmarket20.utils

import android.content.Context
import android.view.View
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.core.BasePopupView
import com.lxj.xpopup.enums.PopupAnimation
import com.lxj.xpopup.interfaces.SimpleCallback
import com.ybmmarket20.view.PayStateCenterPopWindow
import com.ybmmarket20.view.PayTipsPopWindow

/**
 *    author : 朱勇闯
 *    e-mail : <EMAIL>
 *    date   : 2024/12/12 11:01
 *    desc   :
 */
fun showPayPop(
    mContext: Context,
    title: String?,
    contentStr: String?,
    orderNum: String?,
    amount: String?,
    cancelStr: String?,
    confirmStr: String?,
    cancelCallBack: View.OnClickListener?,
    confirmCallBack: View.OnClickListener?
) {
    val pop = PayStateCenterPopWindow(
        mContext, title, contentStr,
        orderNum, amount, cancelStr,
        confirmStr, cancelCallBack, confirmCallBack
    )
    XPopup.Builder(mContext)
        .dismissOnTouchOutside(false)
        .hasBlurBg(false)
        .setPopupCallback(object : SimpleCallback() {
            override fun onBackPressed(popupView: BasePopupView): Boolean {
                return false
            }
        })
        .popupAnimation(PopupAnimation.NoAnimation)
        .asCustom(pop)
        .show()
}

fun showPayTipsPop(
    mContext: Context,
    contentStr: String?,
    confirmStr: String?,
    canCheck: Boolean,
    confirmCallBack: () -> Unit?
) {
    val pop = PayTipsPopWindow(
        mContext, contentStr,
        confirmStr,canCheck, confirmCallBack
    )
    XPopup.Builder(mContext)
        .dismissOnTouchOutside(false)
        .dismissOnBackPressed(false)
        .asCustom(pop)
        .show()
}