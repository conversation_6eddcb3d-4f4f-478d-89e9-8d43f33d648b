package com.ybmmarket20.utils

import java.io.*

@Throws(IOException::class, ClassNotFoundException::class)
fun <T> List<T>?.deepCopy(): List<T>? {
    if (this == null) return emptyList()
    val byteOut = ByteArrayOutputStream()
    val out = ObjectOutputStream(byteOut)
    out.writeObject(this)
    val byteIn = ByteArrayInputStream(byteOut.toByteArray())
    val ois = ObjectInputStream(byteIn)
    val result = ois.readObject()
    return if (result != null) result as List<T> else null
}