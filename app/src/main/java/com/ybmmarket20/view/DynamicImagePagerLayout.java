package com.ybmmarket20.view;

import android.content.Context;
import androidx.viewpager.widget.PagerAdapter;
import androidx.viewpager.widget.ViewPager;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;

import com.ybm.app.adapter.YBMBaseAdapter;
import com.ybm.app.adapter.YBMBaseHolder;
import com.ybm.app.view.WrapGridLayoutManager;
import com.ybmmarket20.R;
import com.ybmmarket20.home.MainActivity;
import com.ybmmarket20.bean.ModuleViewItem;
import com.ybmmarket20.home.BrandFragment;

import java.util.ArrayList;
import java.util.List;

/**
 * 左右滑动的图片列表
 */

public class DynamicImagePagerLayout extends BaseDynamicLayout<ModuleViewItem> {

    private AutoHeightViewPager vp_arl;
    private RelativeLayout mRlLayout;
    private LinearLayout ll_arl;
    private RecyclerView mListView;
    private BrandFragment.MyItemDecoration itemDecoration;

    private final static int ITEM_GRID_NUM_DEF = 6;
    private final static int ITEM_GRID_NUM_1 = 3;
    private final static int ITEM_GRID_NUM_2 = 9;

    public int item_grid_num = ITEM_GRID_NUM_DEF;//每一页中GridView中item的数量
    public int number_columns = 3;//gridview一行展示的数目

    private int style;
    private boolean isWhiteBg;
    private int pageSize;//总共几页

    private MyPagerAdapter pagerAdapter;
    private ProductGrid4Adapter adapter;

    List<ModuleViewItem> items;
    private List<RecyclerView> gridList = new ArrayList<>();

    public DynamicImagePagerLayout(Context context) {
        super(context);
    }

    public DynamicImagePagerLayout(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public DynamicImagePagerLayout(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    public void initViews() {
        mRlLayout = (RelativeLayout) findViewById(R.id.rl_layout);
        vp_arl = (AutoHeightViewPager) findViewById(R.id.vp_arl);
        ll_arl = (LinearLayout) findViewById(R.id.ll_arl);
        itemDecoration = new BrandFragment.MyItemDecoration();
        vp_arl.setOnTouchListener(new OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                switch (event.getAction()) {
                    case MotionEvent.ACTION_MOVE:
                    case MotionEvent.ACTION_DOWN:
                        break;
                    case MotionEvent.ACTION_UP:
                        break;
                }
                return false;
            }
        });
    }

    @Override
    public int getDefBg() {
        return -1;
    }

    @Override
    public boolean supportSetHei() {
        return false;
    }

    @Override
    public int getLayoutId() {
        return R.layout.dynamic_layout_slide_item;
    }

    @Override
    public void setItemData(List<ModuleViewItem> list) {

        if (list == null || list.isEmpty()) {
            return;
        }
        if (vp_arl == null) {
            return;
        }
        if (items != null && items.size() > 0) {
            items.clear();
        }
        if (gridList != null && gridList.size() > 0) {
            gridList.clear();
        }
        this.items = list;

        //计算viewpager一共显示几页
        int pageSize = (int) Math.ceil(list.size() * 1.0 / item_grid_num);
        this.pageSize = pageSize;
        for (int i = 0; i < pageSize; i++) {
            mListView = new RecyclerView(getContext());
            mListView.addItemDecoration(itemDecoration);
            itemDecoration.setRow(0);
            mListView.setNestedScrollingEnabled(false);
            mListView.setLayoutManager(new WrapGridLayoutManager(getContext(), number_columns));
            mListView.scrollToPosition(0);//滑动停止
            adapter = new ProductGrid4Adapter(list, i, item_grid_num);
            mListView.setAdapter(adapter);
            gridList.add(mListView);
        }
        pagerAdapter = new MyPagerAdapter();
        vp_arl.setAdapter(pagerAdapter);
        vp_arl.addOnPageChangeListener(PageListener);
        // 移除上次遗留的所有点
        ll_arl.removeAllViews();
        //重新添加点
        addDots();
        vp_arl.setOffscreenPageLimit(pageSize + 1);
        PageListener.onPageSelected(0);

        mRlLayout.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                return vp_arl.dispatchTouchEvent(event);
            }
        });

    }

    @Override
    public void setStyle(int style) {
        if (style <= 0 && getContext() != null && getContext() instanceof MainActivity) {
            style = 32;
        }
        if (this.style == style && adapter != null && isWhiteBg == isWhiteBg()) {
            return;
        }
        this.style = style;
        isWhiteBg = isWhiteBg();
        switch (style) {
            case 32:
            default:
                item_grid_num = ITEM_GRID_NUM_DEF;
                break;
            case 33:
                item_grid_num = ITEM_GRID_NUM_1;
                break;
            case 34:
                item_grid_num = ITEM_GRID_NUM_2;
                break;
        }
    }

    private boolean isWhiteBg() {
        if (TextUtils.isEmpty(moduleView.bgRes) || "#ffffff".equals(moduleView.bgRes.toLowerCase())
                || "#F7F7F8".equals(moduleView.bgRes.toLowerCase())) {
            return false;
        }
        return true;
    }

    public class MyPagerAdapter extends PagerAdapter {

        @Override
        public int getCount() {
            return gridList == null ? 0 : gridList.size();
        }

        @Override
        public int getItemPosition(Object object) {
            return POSITION_NONE;
        }

        @Override
        public boolean isViewFromObject(View view, Object object) {
            return view == object;
        }

        @Override
        public Object instantiateItem(ViewGroup container, int position) {
            container.addView(gridList.get(position));
            return gridList.get(position);
        }

        @Override
        public void destroyItem(ViewGroup container, int position, Object object) {
            container.removeView((View) object);
        }

    }

    public ViewPager.OnPageChangeListener PageListener = new ViewPager.OnPageChangeListener() {

        @Override
        public void onPageSelected(int position) {
            // 改变点的状态
            if (pageSize > 1) {
                for (int i = 0; i < pageSize; i++) {
                    ll_arl.getChildAt(i).setEnabled(i != position % pageSize);
                }
            }
        }

        @Override
        public void onPageScrolled(int arg0, float arg1, int arg2) {

        }

        @Override
        public void onPageScrollStateChanged(int arg0) {

        }
    };

    private void addDots() {
        if (items == null || ll_arl == null) {
            return;
        }
        if (pageSize == 1) {
            ll_arl.setVisibility(GONE);
            return;
        }
        ll_arl.setVisibility(VISIBLE);
        for (int i = 0; i < pageSize; i++) {
            // 把10dp 转成对应的像素
            View view = new View(getContext());

            int dotWidth = (int) TypedValue.applyDimension(
                    TypedValue.COMPLEX_UNIT_DIP, 5, getResources()
                            .getDisplayMetrics());

            // 设置宽高、marging
            LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(
                    dotWidth, dotWidth);
            params.setMargins(0, 0, dotWidth, 0);
            view.setLayoutParams(params);
            // 指定背景是选择器，在pagechangelistener中只去改变状态，更加面向对象，易于控制
            view.setBackgroundResource(isWhiteBg ?
                    R.drawable.detail_arl_ball_bg_selector2 : R.drawable.detail_arl_ball_bg_selector3);
            ll_arl.addView(view);
        }

    }

    public class ProductGrid4Adapter extends YBMBaseAdapter<ModuleViewItem> {

        private List<ModuleViewItem> dataList;

        public ProductGrid4Adapter(List<ModuleViewItem> items, int page, int pageNum) {
            super(R.layout.dynamic_layout_image_list_item2, items);
            initData(items, page, pageNum);
        }

        public void initData(List<ModuleViewItem> items, int page, int pageNum) {
            if (dataList == null) {
                dataList = new ArrayList<>();
            }
            dataList.clear();
            //start end分别代表要显示的数组在总数据List中的开始和结束位置
            int start = page * pageNum;
            int end = start + pageNum;
            while ((start < items.size()) && (start < end)) {
                dataList.add(items.get(start));
                start++;
            }
            super.setNewData(dataList);
        }

        @Override
        protected void bindItemView(YBMBaseHolder ybmBaseHolder, ModuleViewItem moduleViewItem) {

            ImageView iv = ybmBaseHolder.getView(R.id.iv);
            setImageView(iv, moduleViewItem);
            iv.setTag(R.id.tag_action, moduleViewItem.action);
            iv.setOnClickListener(itemClick);
        }

    }
}
