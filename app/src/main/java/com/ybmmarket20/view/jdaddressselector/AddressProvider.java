package com.ybmmarket20.view.jdaddressselector;


import com.ybmmarket20.bean.Province;

import java.util.List;


public interface AddressProvider {
    void provideProvinces(AddressReceiver<Province> addressReceiver);

    void provideCitiesWith(String provinceId, AddressReceiver<Province> addressReceiver);

    void provideCountiesWith(String cityId, AddressReceiver<Province> addressReceiver);

    void provideStreetsWith(String countyId, AddressReceiver<Province> addressReceiver);

    interface AddressReceiver<T> {
        void send(List<T> data);
    }
}