package com.ybmmarket20.view;

import android.content.Context;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.util.AttributeSet;

import com.ybmmarket20.R;
import com.ybmmarket20.home.MainActivity;
import com.ybmmarket20.adapter.ProductMultiAdapter;
import com.ybmmarket20.bean.RowsBean;
import com.ybmmarket20.utils.analysis.XyyIoUtil;
import com.ybmmarket20.home.BrandFragment;
import com.ybmmarket20.utils.RoutersUtils;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.List;

/**
 * 首页多种不同item类型的商品列表
 */
public class DynamicProductMultiLayout extends BaseDynamicLayout<RowsBean> {

    private RecyclerView listView;
    private ProductMultiAdapter adapter;
    private BrandFragment.MyItemDecoration itemDecoration;
    GridLayoutManager manager;

    private int style;
    private boolean isWhiteBg;

    public DynamicProductMultiLayout(Context context) {
        super(context);
    }

    public DynamicProductMultiLayout(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public DynamicProductMultiLayout(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    public void initViews() {

        listView = (RecyclerView) findViewById(R.id.rv_list);
        itemDecoration = new BrandFragment.MyItemDecoration();
        listView.addItemDecoration(itemDecoration);
        listView.setNestedScrollingEnabled(false);

    }

    @Override
    public boolean supportSetHei() {
        return false;
    }

    @Override
    public int getLayoutId() {
        return R.layout.dynamic_layout_product_multi;
    }

    @Override
    public void setItemData(List<RowsBean> items) {
        if (items == null || items.size() <= 0) {
            return;
        }

        for (int i = 0; i < items.size(); i++) {

            if (i == 0) {
                RowsBean rowsBean = items.get(i);
                rowsBean.setItemType(RowsBean.content_11);
                rowsBean.spanSize = RowsBean.CONTENT_11_SPAN_SIZE;
            } else {
                RowsBean rowsBean = items.get(i);
                rowsBean.setItemType(RowsBean.content_31);
                rowsBean.spanSize = RowsBean.CONTENT_31_TEXT_SPAN_SIZE;
            }
            if (getContext().getClass().getName().equals("com.ybmmarket20.home.MainActivity")) {
                items.get(i).zhugeEventName = XyyIoUtil.ACTION_HOME_ITEMSHOW_PRODUCT;
            }else if (getContext().getClass().getName().equals("com.ybmmarket20.activity.ClinicActivity")){
                items.get(i).zhugeEventName = XyyIoUtil.ACTION_CLINIC_PRODUCT;
            }


        }

        adapter = new ProductMultiAdapter(items);
        adapter.setEnableLoadMore(false);
        manager = new GridLayoutManager(getContext(), 3);
        adapter.setOnItemClickListener(new ProductMultiAdapter.OnListViewItemClickListener() {
            @Override
            public void onItemClick(RowsBean rows) {
                RoutersUtils.open("ybmpage://productdetail/" + rows.getId());
                JSONObject jsonObject = new JSONObject();
                try {
                    jsonObject.put("id", rows.getId());
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                if (!TextUtils.isEmpty(rows.zhugeEventName)) {
                    // 埋点 单品展示
                    XyyIoUtil.track(rows.zhugeEventName, jsonObject);
                }
            }
        });
        adapter.setSpanSizeLookup(new ProductMultiAdapter.SpanSizeLookup() {
            @Override
            public int getSpanSize(GridLayoutManager gridLayoutManager, int position) {
                return items.get(position).spanSize;
            }
        });

        listView.setLayoutManager(manager);
        listView.setAdapter(adapter);

    }

    @Override
    public void setStyle(int style) {

        if (style <= 0 && getContext() != null && getContext() instanceof MainActivity) {
            style = 32;
        }
        if (this.style == style && adapter != null && isWhiteBg == isWhiteBg()) {
            return;
        }
        this.style = style;

    }

    private boolean isWhiteBg() {
        if (TextUtils.isEmpty(moduleView.bgRes) || "#ffffff".equals(moduleView.bgRes.toLowerCase())) {
            return false;
        }
        return true;
    }


    @Override
    public void onRefresh() {
        super.onRefresh();
        if (adapter != null && adapter.getData() != null && !adapter.getData().isEmpty()) {
            if (adapter.getCurrPosition() > 0 && adapter.getCurrPosition() < adapter.getData().size()) {
                adapter.notifyItemChanged(adapter.getCurrPosition());
            }
        }
    }

}
