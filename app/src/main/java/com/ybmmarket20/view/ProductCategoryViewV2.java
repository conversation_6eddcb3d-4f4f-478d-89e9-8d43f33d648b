package com.ybmmarket20.view;

import android.annotation.SuppressLint;
import android.content.Context;
import android.util.AttributeSet;
import android.util.Log;
import android.util.SparseArray;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.StaggeredGridLayoutManager;

import com.apkfuns.logutils.LogUtils;
import com.ybm.app.adapter.YBMBaseAdapter;
import com.ybm.app.adapter.YBMBaseHolder;
import com.ybmmarketkotlin.adapter.YBMBaseMultiItemAdapter;
import com.ybm.app.bean.NetError;
import com.ybm.app.common.BaseYBMApp;
import com.ybm.app.common.OkHttpManager;
import com.ybm.app.view.WrapLinearLayoutManager;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.OneRowsBean;
import com.ybmmarket20.bean.SortBean;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.utils.SpUtil;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Set;


public class ProductCategoryViewV2 extends LinearLayout {

    public static final int header = 1;//头部 0 高度
    public static final int content = 2;//内容
    private int spanCount = 2;//二级行数
    private RecyclerView firstLevelListView;
    private RecyclerView secondLevelListView;
    private List<OneRowsBean> firstList = new ArrayList<OneRowsBean>();
    private List<OneRowsBean> currSecondList = new ArrayList<OneRowsBean>();
    private SparseArray<List<OneRowsBean>> allSecondList = new SparseArray<List<OneRowsBean>>();
    private YBMBaseMultiItemAdapter<OneRowsBean> secondLevelAdapter;
    private YBMBaseAdapter firstLevelAdapter;
    private OnSelectListener mOnSelectListener;
    private int secondInFirstPosition = -1;
    private int currFirstPosition = 0;//当前一级
    private int lastsecondPostion = -1;//最后一次的二级,
    private int currSecondPostion = -1;//当前二级
    private LinearLayout contentView;
    private String headeKey = "所有";
    private boolean firstCanSelect = false; //一级不能选择
    private int mIsFragile;//取数量的标识,0：取skuContainFragileNum； 1：取skuNotContainFragileNum
    private LevelItemClickListener mLevelItemClickListener;

    private Set<String> selectedFirstLevelSet = new HashSet<>();

    public ProductCategoryViewV2(Context context) {
        this(context, null);
    }

    public ProductCategoryViewV2(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public ProductCategoryViewV2(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }


    private void init(final Context context) {
        setOrientation(HORIZONTAL);
        LayoutInflater inflater = (LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        contentView = (LinearLayout) inflater.inflate(R.layout.view_product_category, this, true);
        firstLevelListView = (RecyclerView) contentView.findViewById(R.id.rv_1);
        secondLevelListView = (RecyclerView) contentView.findViewById(R.id.rv_2);
        firstLevelAdapter = new YBMBaseAdapter<OneRowsBean>(R.layout.product_first_level, firstList) {
            @Override
            protected void bindItemView(YBMBaseHolder holder, final OneRowsBean bean) {
                final int position = holder.getAdapterPosition();
                TextView tv = holder.getView(R.id.tv_name);
                tv.setText(bean.name);
                tv.setActivated(bean.isSelected);
                tv.setSelected(bean.isSelected);

                tv.setOnClickListener(view -> {
                    if (currFirstPosition == position) return;
                    currFirstPosition = position;
                    currSecondList = getCurrSecondList(position);
                    secondLevelListView.scrollToPosition(0);
                    secondLevelAdapter.setNewData(currSecondList);
                    setSelectedFirstLevelItem();
                    Log.i("selectedFirstLevelSet", selectedFirstLevelSet.toString());
                    bean.isSelected = true;
                    firstLevelAdapter.notifyDataSetChanged();
                });
            }
        };
        firstLevelListView.setLayoutManager(new WrapLinearLayoutManager(getContext()));
        firstLevelListView.setAdapter(firstLevelAdapter);
        firstLevelAdapter.setEnableLoadMore(false);
        secondLevelAdapter = new YBMBaseMultiItemAdapter<OneRowsBean>(currSecondList) {
            @Override
            protected void bindItemView(YBMBaseHolder holder, OneRowsBean bean) {
                switch (holder.getItemViewType()) {
                    case header:
                        this.setFullSpan(holder);
                        bindContent(holder, bean, true);
                        break;
                    case content:
                        bindContent(holder, bean, false);
                        break;
                }
            }
        };
        secondLevelAdapter.addItemType(header, R.layout.product_second_level_header);
        secondLevelAdapter.addItemType(content, R.layout.product_second_level_content);
        secondLevelListView.setLayoutManager(new StaggeredGridLayoutManager(spanCount, StaggeredGridLayoutManager.VERTICAL));
        secondLevelListView.setAdapter(secondLevelAdapter);
    }

    private void bindContent(final YBMBaseHolder holder, final OneRowsBean bean, boolean isHeader) {
        final int position = holder.getAdapterPosition();
        holder.setText(R.id.tv_name, bean.name);
        if (isHeader) {
            holder.getView(R.id.ll).setHovered((bean.name != null && bean.name.contains(headeKey)));
//            holder.setText(R.id.tv_count, "(" + bean.productNum + ")");
            holder.getView(R.id.ll).setActivated(bean.isSelected);
            holder.setOnClickListener(R.id.ll, new SecondClick(position, bean, true));
            //holder.getView(R.id.tv_name).setActivated(position == currSecondPostion);
            //holder.getView(R.id.tv_count).setActivated(position == currSecondPostion);

        } else {
            holder.getView(R.id.tv_name).setActivated(bean.isSelected);
            holder.setOnClickListener(R.id.tv_name, new SecondClick(position, bean, false));
        }
    }


    private class SecondClick implements OnClickListener {
        private int position;
        private OneRowsBean bean;
        private boolean mIsHeader;

        public SecondClick(int position, OneRowsBean bean, boolean isHeader) {
            this.position = position;
            this.bean = bean;
            this.mIsHeader = isHeader;
        }

        @SuppressLint("NotifyDataSetChanged")
        @Override
        public void onClick(View view) {
            view.setActivated(true);
            if(bean.level == 0){
                //全部
                new AllSelectedState().handleSelect(currSecondList.get(0), selectedFirstLevelSet);
            } else if (bean.level == OneRowsBean.FILTER_CATEGORY_LEVEL_SECOND) {
                new SecondLevelSelectedState().handleSelect(bean, selectedFirstLevelSet);
            } else if (bean.level == OneRowsBean.FILTER_CATEGORY_LEVEL_THIRD) {
                new ThirdLevelSelectedState().handleSelect(bean, selectedFirstLevelSet);
            } else return;
            secondLevelAdapter.notifyDataSetChanged();
            currSecondPostion = position;
            lastsecondPostion = currSecondPostion;
            secondInFirstPosition = currFirstPosition;
        }
    }

    public Set<String> getSelectedCategoryIds() {
        Set<String> selectedCategorySet = new HashSet<>();
        for(int i = 0; i < allSecondList.size(); i++) {
            List<OneRowsBean> oneRowsBeansList = allSecondList.valueAt(i);
            if (oneRowsBeansList == null) continue;
            for (OneRowsBean secondRowsBean : oneRowsBeansList) {
                if (secondRowsBean.level == 0 && secondRowsBean.isSelected) {
                    selectedCategorySet.add(secondRowsBean.id + "");
                    break;
                } else if (secondRowsBean.level == OneRowsBean.FILTER_CATEGORY_LEVEL_SECOND && secondRowsBean.isSelected) {
                    selectedCategorySet.add(secondRowsBean.id + "");
                } else if (secondRowsBean.level == OneRowsBean.FILTER_CATEGORY_LEVEL_SECOND) {
                    for (OneRowsBean thirdRowsBean : secondRowsBean.children) {
                        if (thirdRowsBean.isSelected){
                            selectedCategorySet.add(thirdRowsBean.id + "");
                        }
                    }
                }
            }
        }
        return selectedCategorySet;
    }

    abstract class SelectedState {

        abstract void handleSelect(OneRowsBean oneRowsBean, Set<String> selectedFirstSet);

        /**
         * 当前一级分类下是否全部选中
         * @param firstLevelRowsBean
         * @return
         */
        boolean isContainsSelectedItem(OneRowsBean firstLevelRowsBean) {
            for (OneRowsBean secondLevelRowsBean : firstLevelRowsBean.children) {
                if (secondLevelRowsBean.isSelected) return true;
                if (secondLevelRowsBean.children == null) continue;
                for (OneRowsBean thirdRowsBean : secondLevelRowsBean.children) {
                    if (thirdRowsBean.isSelected) return true;
                }
            }
            return false;
        }

        void handleAllData(OneRowsBean oneRowsBean) {
            //设置二级列表头
            for (int i = 0; i < oneRowsBean.children.size(); i++) {
                //略过"全部"选项
                if (i == 0) continue;
                OneRowsBean secondLevelRowsBean = oneRowsBean.children.get(i);
                boolean isSelectedSecondLevel = true;
                //无子项则不处理
                if (secondLevelRowsBean.children.isEmpty()) {
                    continue;
                }
                //查看三级选项，一旦三级选项有未选中项，则二级选项不是选中
                for (OneRowsBean thirdChild : secondLevelRowsBean.children) {
                    if (!thirdChild.isSelected) {
                        isSelectedSecondLevel = false;
                        break;
                    }
                }
                secondLevelRowsBean.isSelected = isSelectedSecondLevel;
            }
            //设置全部选项
            boolean isSelectedAll = true;
            if (oneRowsBean.children == null || oneRowsBean.children.isEmpty()) return;
            for (int i = 0; i < oneRowsBean.children.size(); i++) {
                //略过"全部"选项
                if (i == 0) continue;
                if (!oneRowsBean.children.get(i).isSelected) {
                    //查看除全部的二级选项只要有一项未选中则不是全选
                    isSelectedAll = false;
                    break;
                }
            }
            oneRowsBean.children.get(0).isSelected = isSelectedAll;
        }
    }

    /**
     * 二级列表- 全部
     */
    class AllSelectedState extends SelectedState {

        @Override
        void handleSelect(OneRowsBean allRowsBean, Set<String> selectedFirstSet) {
            OneRowsBean parentBean = allRowsBean.parent;
            boolean selectedStatus = !allRowsBean.isSelected;
            allRowsBean.isSelected = selectedStatus;
            for (OneRowsBean secondChild : parentBean.children) {
                secondChild.isSelected = selectedStatus;
                if (secondChild.children == null) continue;
                for (OneRowsBean thirdChild : secondChild.children) {
                    thirdChild.isSelected = selectedStatus;
                }
            }
            if (selectedStatus) {
                selectedFirstSet.add(allRowsBean.id + "");
            } else {
                selectedFirstSet.remove(allRowsBean.id + "");
            }
        }
    }

    /**
     * 二级列表
     */
    class SecondLevelSelectedState extends SelectedState {

        @Override
        void handleSelect(OneRowsBean secondRowBean, Set<String> selectedFirstSet) {
            boolean selectedStatus = !secondRowBean.isSelected;
            secondRowBean.isSelected = selectedStatus;
            for (OneRowsBean child : secondRowBean.children) {
                child.isSelected = selectedStatus;
            }
            handleAllData(secondRowBean.parent);
            if (isContainsSelectedItem(secondRowBean.parent)) {
                selectedFirstSet.add(secondRowBean.parent.id + "");
            } else {
                selectedFirstSet.remove(secondRowBean.parent.id + "");
            }
        }
    }

    /**
     * 三级列表
     */
    class ThirdLevelSelectedState extends SelectedState {

        @Override
        void handleSelect(OneRowsBean thirdLevelRowsBean, Set<String> selectedFirstSet) {
            thirdLevelRowsBean.isSelected = !thirdLevelRowsBean.isSelected;
            handleAllData(thirdLevelRowsBean.parent.parent);
            if (isContainsSelectedItem(thirdLevelRowsBean.parent.parent)) {
                selectedFirstSet.add(thirdLevelRowsBean.parent.parent.id + "");
            } else {
                selectedFirstSet.remove(thirdLevelRowsBean.parent.parent.id + "");
            }
        }
    }


    /**
     * 得到全部分类
     */
    public void doGetNewsContent(String selectedIdsStr) {
        doGetNewsContent(0, null, null, selectedIdsStr);
    }


    /**
     * 得到全部分类
     */
    public void doGetNewsContent(int fromPage, String key, String value, String selectedIdsStr) {
        String merchantid = SpUtil.getMerchantid();
        RequestParams params = new RequestParams();
        params.put("merchantId", merchantid);
        int cache = OkHttpManager.CACHE_AND_NET;
        if (fromPage == 0) {
            params.setUrl(AppNetConfig.STAIR_SORTNET);
        } else {
            cache = OkHttpManager.NO_CACHE;
            params.setUrl(AppNetConfig.PLAN_SCHEDULE_CATEGORY);
            params.put(key, value);
        }
        HttpManager.getInstance().post(cache, params, new BaseResponse<SortBean>() {

            @Override
            public void onSuccess(String content, BaseBean<SortBean> obj, SortBean sortBean) {

                if (BaseYBMApp.getApp().getCurrActivity() != null && BaseYBMApp.getApp().getCurrActivity() instanceof BaseActivity) {
                    ((BaseActivity) BaseYBMApp.getApp().getCurrActivity()).dismissProgress();
                }
                if (obj != null && obj.isSuccess()) {
                    if (sortBean != null) {
                        mIsFragile = sortBean.getIsFragile();
                        List<OneRowsBean> rows = sortBean.getCategoryList();
                        if (rows != null) {
                            firstList.clear();
                            firstList.addAll(rows);
                            addAllCategoryItemData(firstList);
                            setSelectItemBySelectedItem(selectedIdsStr);
                            if (allSecondList.get(currFirstPosition) != null && selectedIdsStr != null) {
                                allSecondList.remove(currFirstPosition);
                            }
                            currSecondList = getCurrSecondList(currFirstPosition);
                            firstLevelAdapter.setNewData(firstList);
                            secondLevelAdapter.setNewData(currSecondList);
                            establishRelationshipForLevel(sortBean);
                        }
                    }
                }
            }

            @Override
            public void onFailure(NetError error) {
                if (BaseYBMApp.getApp().getCurrActivity() != null && BaseYBMApp.getApp().getCurrActivity() instanceof BaseActivity) {
                    ((BaseActivity) BaseYBMApp.getApp().getCurrActivity()).dismissProgress();
                }
            }
        });
    }

    /**
     * 给所有二级列表添加全部选项
     * @param firstList
     */
    private void addAllCategoryItemData(List<OneRowsBean> firstList) {
        for (OneRowsBean beanOne : firstList) {
            OneRowsBean allBean = new OneRowsBean();
            allBean.setName("全部");
            allBean.setNickname(beanOne.getName());
            allBean.setId(beanOne.getId());
            allBean.parent = beanOne;
            allBean.setProductNum(mIsFragile == 0 ? beanOne.skuContainFragileNum : beanOne.skuNotContainFragileNum);
            allBean.setItemType(header);
            beanOne.children.add(0, allBean);
        }
    }

    /**
     * 根据已选中的id设置当前状态
     * @param selectedIdsStr
     */
    private void setSelectItemBySelectedItem(String selectedIdsStr) {
        if (selectedIdsStr == null) return;
        selectedFirstLevelSet.clear();
        String[] selectedIds = selectedIdsStr.split(",");
        Set<String> selectedIdSet = new HashSet<>(Arrays.asList(selectedIds));
        for (OneRowsBean oneRowsBean : firstList) {
            if (oneRowsBean == null || oneRowsBean.children == null) continue;
            OneRowsBean allRowBean = null;
            for (OneRowsBean secondLevelRowsBean : oneRowsBean.children) {
                if (secondLevelRowsBean == null) continue;
                if (allRowBean == null) {
                    allRowBean = secondLevelRowsBean;
                    boolean isAllSelect = selectedIdSet.contains(allRowBean.id + "");
                    allRowBean.isSelected = isAllSelect;
                    if (isAllSelect) selectedFirstLevelSet.add(allRowBean.id + "");
                    continue;
                }
                if (allRowBean.isSelected) {
                    secondLevelRowsBean.isSelected = true;
                    selectedFirstLevelSet.add(oneRowsBean.id + "");
                } else {
                    secondLevelRowsBean.isSelected = selectedIdSet.contains(secondLevelRowsBean.id + "");
                }
                if (secondLevelRowsBean.children == null) continue;
                for (OneRowsBean thirdChild : secondLevelRowsBean.children) {
                    if (thirdChild == null) continue;
                    if (secondLevelRowsBean.isSelected) {
                        thirdChild.isSelected = true;
                        selectedFirstLevelSet.add(oneRowsBean.id + "");
                    } else {
                        thirdChild.isSelected = selectedIdSet.contains(thirdChild.id + "");
                    }
                }
            }
        }
        setSelectedFirstLevelItem();
    }

    /**
     * 处理以及分类选中状态
     */
    private void setSelectedFirstLevelItem() {
        for (OneRowsBean oneRowsBean : firstList) {
            oneRowsBean.isSelected = selectedFirstLevelSet.contains(oneRowsBean.id + "");
        }
    }

    /**
     * 层级之间建立关系
     * @param sortBean
     */
    private void establishRelationshipForLevel(SortBean sortBean) {
        if (sortBean != null) {
            for (OneRowsBean oneRowsBean : sortBean.getCategoryList()) {
                if (oneRowsBean != null) {
                    oneRowsBean.childrenMap = new HashMap<>();
                    oneRowsBean.childrenMap.put(oneRowsBean.id, oneRowsBean);
                    for (OneRowsBean row : oneRowsBean.getRows()) {
                        if (row != null) {
                            row.childrenMap = new HashMap<>();
                            row.childrenMap.put(row.id, row);
                            row.parent = oneRowsBean;
                            if (row.getRows() == null) continue;
                            for (OneRowsBean rowRow : row.getRows()) {
                                if (rowRow != null) {
                                    rowRow.parent = row;
                                }
                            }
                        }
                    }
                }
            }
        }
    }


    public void getContentByUrl(RequestParams requestParams) {

        HttpManager.getInstance().post(requestParams, new BaseResponse<SortBean>() {

            @Override
            public void onSuccess(String content, BaseBean<SortBean> obj, SortBean sortBean) {

                if (BaseYBMApp.getApp().getCurrActivity() != null && BaseYBMApp.getApp().getCurrActivity() instanceof BaseActivity) {
                    ((BaseActivity) BaseYBMApp.getApp().getCurrActivity()).dismissProgress();
                }
                if (obj != null && obj.isSuccess()) {
                    if (sortBean != null) {
                        mIsFragile = sortBean.getIsFragile();
                        List<OneRowsBean> rows = sortBean.getCategoryList();
                        if (rows != null) {
                            firstList.clear();
                            firstList.addAll(rows);
                            currSecondList = getCurrSecondList(currFirstPosition);
                            firstLevelAdapter.setNewData(firstList);
                            secondLevelAdapter.setNewData(currSecondList);
                        }
                    }
                }
            }

            @Override
            public void onFailure(NetError error) {
                if (BaseYBMApp.getApp().getCurrActivity() != null && BaseYBMApp.getApp().getCurrActivity() instanceof BaseActivity) {
                    ((BaseActivity) BaseYBMApp.getApp().getCurrActivity()).dismissProgress();
                }
            }
        });
    }

    private List<OneRowsBean> getCurrSecondList(int currFirstPosition) {
        if (allSecondList.get(currFirstPosition) != null) {
            return allSecondList.get(currFirstPosition);
        } else {
            if (firstList == null || firstList.size() <= currFirstPosition) {//错误处理
                LogUtils.d("出现错误了------");
            } else {
                if (firstList.get(currFirstPosition).getRows() == null) {
                    allSecondList.put(currFirstPosition, new ArrayList<OneRowsBean>());
                } else {
                    List<OneRowsBean> temp = firstList.get(currFirstPosition).getRows();
                    List<OneRowsBean> secondList = new ArrayList<>(temp.size() + 1);
//                    OneRowsBean beanOne = firstList.get(currFirstPosition);
//                    OneRowsBean AllBean = new OneRowsBean();
//                    AllBean.setName("全部");
//                    AllBean.setNickname(beanOne.getName());
//                    AllBean.setId(beanOne.getId());
//                    AllBean.parent = beanOne;
//                    AllBean.setProductNum(mIsFragile == 0 ? beanOne.skuContainFragileNum : beanOne.skuNotContainFragileNum);
//                    AllBean.setItemType(header);
//                    secondList.add(0, AllBean);
                    OneRowsBean bean = null;
                    for (int a = 0; a < temp.size(); a++) {
                        bean = temp.get(a);
                        bean.setNickname(bean.getName());
                        bean.setProductNum(mIsFragile == 0 ? bean.skuContainFragileNum : bean.skuNotContainFragileNum);
                        bean.setItemType(header);
                        secondList.add(bean);
                        OneRowsBean bean2 = null;
                        if (bean.getRows() != null && bean.getRows().size() > 0) {
                            for (int x = 0; x < bean.getRows().size(); x++) {
                                bean2 = bean.getRows().get(x);
                                bean2.setNickname(bean2.getName());
                                bean2.setItemType(content);
                            }
                            secondList.addAll(bean.getRows());
                        }
                    }
                    allSecondList.put(currFirstPosition, secondList);
                }
                return getCurrSecondList(currFirstPosition);
            }
        }

        return Collections.EMPTY_LIST;
    }


    public void setOnSelectListener(OnSelectListener onSelectListener) {
        mOnSelectListener = onSelectListener;
    }

    public interface OnSelectListener {
        void onConfirm(Set<Integer> categoryIds);
    }

    //重置当前选择项目
    @SuppressLint("NotifyDataSetChanged")
    public void resetAllPosition() {
//        secondInFirstPosition = -1;
//        currFirstPosition = 0;//当前一级
//        lastsecondPostion = -1;//最后一次的二级,
//        currSecondPostion = -1;//当前二级
//        if (firstList == null || firstList.isEmpty() || firstLevelAdapter == null || secondLevelAdapter == null) {
//            return;
//        }
//        currSecondList = getCurrSecondList(currFirstPosition);
//        secondLevelAdapter.notifyDataSetChanged();
//        firstLevelAdapter.notifyDataSetChanged();
//        secondLevelAdapter.setNewData(currSecondList);
        setSelectItemBySelectedItem("");
        currSecondList = getCurrSecondList(currFirstPosition);
        secondLevelAdapter.notifyDataSetChanged();
        firstLevelAdapter.notifyDataSetChanged();
    }

    public void reLoad() {
        if (firstList == null || firstList.isEmpty() || firstLevelAdapter == null || secondLevelAdapter == null) {
            return;
        }
        currSecondList = getCurrSecondList(currFirstPosition);
        firstLevelAdapter.setNewData(firstList);
        secondLevelAdapter.setNewData(currSecondList);
    }

    public void setOnLevelItemClickListener(LevelItemClickListener levelItemClickListener) {
        mLevelItemClickListener = levelItemClickListener;
    }

    public interface LevelItemClickListener {
        void onClickLevel(SearchCategorySelectLevelTrack searchCategorySelectLevelTrack);
    }

}
