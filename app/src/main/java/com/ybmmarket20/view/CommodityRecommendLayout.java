package com.ybmmarket20.view;

import android.content.Context;
import androidx.viewpager.widget.PagerAdapter;
import androidx.viewpager.widget.ViewPager;
import androidx.recyclerview.widget.RecyclerView;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;

import com.ybm.app.view.WrapGridLayoutManager;
import com.ybmmarket20.R;
import com.ybmmarket20.adapter.CommodityGridAdapter;
import com.ybmmarket20.bean.RowsBean;
import com.ybmmarket20.common.JGTrackTopLevelKt;
import com.ybmmarket20.common.JgTrackBean;
import com.ybmmarket20.constant.IntentCanst;
import com.ybmmarket20.utils.RoutersUtils;
import com.ybmmarket20.utils.analysis.BaseFlowData;
import com.ybmmarket20.utils.analysis.FlowDataAnalysisManagerKt;
import com.ybmmarket20.utils.analysis.FlowDataEventAnalysisKt;
import com.ybmmarketkotlin.utils.RouterJump;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

public class CommodityRecommendLayout extends RelativeLayout {

    private AutoHeightViewPager vp_arl;
    private RelativeLayout mRlLayout;
    private LinearLayout ll_arl;
    private RecyclerView mListView;
    private List<RowsBean> items = new ArrayList<>();
    private List<RecyclerView> gridList = new ArrayList<>();
    public int item_grid_num = 6;//每一页中GridView中item的数量
    public int number_columns = 3;//gridview一行展示的数目
    private int pageSize;//总共几页
    private CommodityGridAdapter adapter;
    private MyPagerAdapter pagerAdapter;
    private BaseFlowData flowData; //埋点数据
    private JgTrackBean jgTrackBean;

    public CommodityRecommendLayout(Context context) {
        this(context,null);
    }

    public CommodityRecommendLayout(Context context, AttributeSet attrs) {
        this(context, attrs,0);
    }

    public CommodityRecommendLayout(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initData();
    }

    private void initData() {
        View.inflate(getContext(), R.layout.detail_layout_recommend_item, this);

        mRlLayout = (RelativeLayout) findViewById(R.id.rl_layout);
        vp_arl = (AutoHeightViewPager) findViewById(R.id.vp_arl);
        ll_arl = (LinearLayout) findViewById(R.id.ll_arl);
        //获得viewpager的触摸事件
        vp_arl.setOnTouchListener(new OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                switch (event.getAction()) {
                    case MotionEvent.ACTION_MOVE:
                    case MotionEvent.ACTION_DOWN:
                        break;
                    case MotionEvent.ACTION_UP:
                        break;
                }
                return false;
            }

        });
    }

    /**
     * 设置埋点数据
     * @param flowData
     */
    public void setFlowData(BaseFlowData flowData){
        this.flowData = flowData;
    }

    public void setJgTrackBean(JgTrackBean jgTrackBean){
        this.jgTrackBean = jgTrackBean;
    }

    /**
     * 设置轮播图数据
     *
     * @param
     */
    public void setItemData(List<RowsBean> list) {
        if (list == null || list.isEmpty()) {
            return;
        }
        if (vp_arl == null) {
            return;
        }
        if (items == null) {
            items = new ArrayList<>();
        }
        items.clear();
        items.addAll(list);

        if (gridList == null) {
            gridList = new ArrayList<>();
        }
        gridList.clear();

        //计算viewpager一共显示几页
        int pageSize = (int) Math.ceil(list.size() * 1.0 / item_grid_num);
        this.pageSize = pageSize;
        for (int i = 0; i < pageSize; i++) {
            mListView = new RecyclerView(getContext());
            mListView.setNestedScrollingEnabled(false);
            mListView.setLayoutManager(new WrapGridLayoutManager(getContext(), number_columns));
            mListView.scrollToPosition(0);//滑动停止
            adapter = new CommodityGridAdapter(list, i, item_grid_num);
            adapter.setJgTrackBean(jgTrackBean);
            adapter.setOnItemClickListener(rows -> {
                if (rows != null) {
                    String mUrl = "ybmpage://productdetail/" + rows.getId();
                    HashMap<String,Object> params = new HashMap<>();
                    if (jgTrackBean!=null){
                        if (jgTrackBean.getEntrance()!=null){
                            params.put(IntentCanst.JG_ENTRANCE,jgTrackBean.getEntrance());
                        }
                        if (jgTrackBean.getJgReferrer() != null){
                            params.put(IntentCanst.JG_REFERRER,jgTrackBean.getJgReferrer());
                        }
                        if (jgTrackBean.getJgReferrerTitle() != null){
                            params.put(IntentCanst.JG_REFERRER_TITLE,jgTrackBean.getJgReferrerTitle());
                        }
                        if (jgTrackBean.getJgReferrerModule() != null){
                            params.put(IntentCanst.JG_REFERRER_MODULE,jgTrackBean.getJgReferrerModule());
                        }
                    }

                    mUrl = JGTrackTopLevelKt.splicingUrlWithParams(mUrl,params);
                    RoutersUtils.open(mUrl);
//                    FlowDataAnalysisManagerKt.openUrl(mUrl, flowData);
                }
            });
            mListView.setAdapter(adapter);
            gridList.add(mListView);
        }
        pagerAdapter = new MyPagerAdapter();
        vp_arl.setAdapter(pagerAdapter);
        vp_arl.addOnPageChangeListener(PageListener);
        // 移除上次遗留的所有点
        ll_arl.removeAllViews();
        //重新添加点
        addDots();
        vp_arl.setOffscreenPageLimit(pageSize + 1);
        PageListener.onPageSelected(0);

        mRlLayout.setOnTouchListener((v, event) -> vp_arl.dispatchTouchEvent(event));
    }

    private void addDots() {
        if (items == null) {
            return;
        }
        if (pageSize == 1) {
            ll_arl.setVisibility(GONE);
            return;
        }
        ll_arl.setVisibility(VISIBLE);
        for (int i = 0; i < pageSize; i++) {
            // 把10dp 转成对应的像素
            View view = new View(getContext());

            int dotWidth = (int) TypedValue.applyDimension(
                    TypedValue.COMPLEX_UNIT_DIP, 5, getResources()
                            .getDisplayMetrics());

            // 设置宽高、marging
            LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(
                    dotWidth, dotWidth);
            params.setMargins(0, 0, dotWidth, 0);
            view.setLayoutParams(params);
            view.setBackgroundResource(R.drawable.detail_arl_ball_bg_selector3);
            ll_arl.addView(view);
        }

    }

    public ViewPager.OnPageChangeListener PageListener = new ViewPager.OnPageChangeListener() {

        private int curPosition = 0;
        private List<Integer> loadedViews = new ArrayList();

        @Override
        public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
            if (curPosition == position && positionOffset > 0) {
                //向右滑动
                int showPosition = position + 1;
                if (!loadedViews.contains(showPosition)) {
                    loadedViews.add(showPosition);
                    productExposure(showPosition);
                }
            } else if (curPosition == position && positionOffset==0) {
                if (!loadedViews.contains(position)) {
                    loadedViews.add(position);
                    productExposure(position);
                }
            }
        }

        /**
         * 商品曝光
         * @param position 页码
         */
        private void productExposure(int position) {
            int size = items.size();
            int start = position * item_grid_num;
            int pageLastPosition;
            if (position == pageSize - 1) {
                pageLastPosition = size;
            } else {
                pageLastPosition = (position + 1) * item_grid_num;
            }
            for (int i = start; i < pageLastPosition; i++) {
                RowsBean bean = items.get(i);
                FlowDataEventAnalysisKt.flowDataPageListPageExposure(flowData, bean.getId()+"", bean.getShowName(), bean.sourceType, "");
            }
        }

        @Override
        public void onPageSelected(int position) {
            curPosition = position;
            // 改变点的状态
            if (pageSize > 1) {
                for (int i = 0; i < pageSize; i++) {
                    ll_arl.getChildAt(i).setEnabled(i != position % pageSize);
                }
            }
        }

        @Override
        public void onPageScrollStateChanged(int state) {

        }


    };

    public class MyPagerAdapter extends PagerAdapter {

        @Override
        public int getCount() {
            return gridList == null ? 0 : gridList.size();
        }

        @Override
        public int getItemPosition(Object object) {
            return POSITION_NONE;
        }

        @Override
        public boolean isViewFromObject(View view, Object object) {
            return view == object;
        }

        @Override
        public Object instantiateItem(ViewGroup container, int position) {
            container.addView(gridList.get(position));
            return gridList.get(position);
        }

        @Override
        public void destroyItem(ViewGroup container, int position, Object object) {
            container.removeView((View) object);
        }

    }

}
