package com.ybmmarket20.view

import android.app.AlertDialog
import android.content.Context
import android.graphics.drawable.AnimationDrawable
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import com.ybmmarket20.R
import com.ybmmarket20.common.util.ConvertUtils

class RegisterLoadingDialog {

    private var loadingDrawable: AnimationDrawable? = null
    private var dialog: AlertDialog? = null

    fun showLoading(context: Context, msg: String) {
        val builder = AlertDialog.Builder(context, R.style.AlertDialog)
        builder.setCancelable(false)
        val view = View.inflate(context, R.layout.register_loading, null)
        val iv = view.findViewById<ImageView>(R.id.ivLoading)
        iv.setImageResource(R.drawable.register_loading)
        loadingDrawable = iv.drawable as AnimationDrawable
        val tv = view.findViewById<TextView>(R.id.tvLoading)
        tv.text = msg
        loadingDrawable!!.start()
        dialog = builder.create()
        dialog?.show()
        dialog?.window?.setContentView(view)
        dialog?.setCanceledOnTouchOutside(false)
        val params = dialog?.window!!.attributes
        params.width = ConvertUtils.dp2px(220f)
        params.height = ConvertUtils.dp2px(160f)
//        params.dimAmount = 0.01f
        dialog?.window!!.attributes = params
    }

    fun dismissLoading() {
        if (loadingDrawable != null) {
            loadingDrawable?.stop()
            loadingDrawable = null
        }
        dialog?.dismiss()
    }
}