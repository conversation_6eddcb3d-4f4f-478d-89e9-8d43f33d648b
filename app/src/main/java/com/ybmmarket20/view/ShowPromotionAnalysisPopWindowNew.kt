package com.ybmmarket20.view

import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.bean.VoucherListBean
import com.ybmmarket20.report.coupon.DefaultCouponEntryType
import com.ybmmarket20.report.coupon.ICouponEntryType
import com.ybmmarket20.report.coupon.RowListCouponClick
import com.ybmmarket20.report.coupon.RowListCouponExposure
import com.ybmmarket20.view.homesteady.whenAllNotNull
import com.ydmmarket.report.ReportManager

/**
 * <AUTHOR> Brin
 * @date : 2020/12/11 - 15:28
 * @Description :
 * @version
 */
abstract class ShowPromotionAnalysisPopWindowNew : BaseBottomPopWindow() {

    var mCouponEntryType: ICouponEntryType? = DefaultCouponEntryType()
        set(value) {
            if (value == null) field = DefaultCouponEntryType()
            field = value
        }
    //记录已曝光的券，防止重复曝光
    private val mCouponIdCache: MutableSet<String> = mutableSetOf()
    //券和位置的映射
    private val mCouponIdAndPositionMap: MutableMap<String, Int> = mutableMapOf()

    /**
     * 建立券id和位置的映射
     */
    fun mapCouponIdAndPosition(receivedVoucherList: MutableList<VoucherListBean>?, unReceiveVoucherList: MutableList<VoucherListBean>?) {
        var position = 0
        receivedVoucherList?.forEach {
            mCouponIdAndPositionMap["${it.voucherTemplateId}"] = position
            position ++
        }
        unReceiveVoucherList?.forEach {
            mCouponIdAndPositionMap["${it.voucherTemplateId}"] = position
            position ++
        }
    }

    private fun getPosition(voucherTemplateId: Int): Int {
        val templateId = "$voucherTemplateId"
        return if (!mCouponIdAndPositionMap.containsKey(templateId)) return -1
        else mCouponIdAndPositionMap[templateId]?.plus(1)?: -1
    }

}