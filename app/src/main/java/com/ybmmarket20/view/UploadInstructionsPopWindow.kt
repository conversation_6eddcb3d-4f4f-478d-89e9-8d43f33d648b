package com.ybmmarket20.view

import android.content.Context
import android.widget.TextView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.lxj.xpopup.core.BottomPopupView
import com.lxj.xpopup.util.XPopupUtils
import com.ybmmarket20.R
import kotlinx.android.synthetic.main.pop_layout_upload_instructions.view.ivClose
import kotlinx.android.synthetic.main.pop_layout_upload_instructions.view.iv_certificate
import kotlinx.android.synthetic.main.pop_layout_upload_instructions.view.tv_album
import kotlinx.android.synthetic.main.pop_layout_upload_instructions.view.tv_camera
import kotlinx.android.synthetic.main.pop_layout_upload_instructions.view.tv_requirements

class UploadInstructionsPopWindow(
    var mContext: Context,
    var imageUrl: String,
    var remark: String,
    private var albumCallBack: () -> Unit?,
    private var cameraCallBack: () -> Unit?,
    private var showBigImage: (Int) -> Unit?
) : BottomPopupView(mContext) {

    override fun getImplLayoutId() = R.layout.pop_layout_upload_instructions

    override fun onCreate() {
        super.onCreate()

        tv_album.setOnClickListener {
            dismiss()
            albumCallBack.invoke()
        }

        tv_camera.setOnClickListener {
            dismiss()
            cameraCallBack.invoke()
        }
        ivClose.setOnClickListener {
            dismiss()
        }
        if (imageUrl.isNotEmpty()) {
            Glide.with(mContext).load(imageUrl).thumbnail(0.1f).diskCacheStrategy(DiskCacheStrategy.ALL) .placeholder(R.drawable.icon_goods_detail_shop_logo_default)
                .error(R.drawable.icon_goods_detail_shop_logo_default) .into(iv_certificate)
        }
//        iv_certificate.setImageResource(getResourceIdByCode(code))
//        iv_certificate.setOnClickListener {
//        dismiss()
//            showBigImage.invoke(getResourceIdByCode(code))
//        }
//        setDesc(code, tv_requirements)
        tv_requirements.setText(remark)
    }
    fun getResourceIdByCode(code: String): Int {
        return when (code) {
            "YYZZ" -> R.drawable.icon_aptitude_yyzz
            "YPJY" -> R.drawable.icon_aptitude_ypjy
            "FRSQ" -> R.drawable.icon_aptitude_frsq
            "ZYXK" -> R.drawable.icon_aptitude_zyxk
            "WTRZ" -> R.drawable.icon_aptitude_wtrz
            "KPXX" -> R.drawable.icon_aptitude_kpxx
            // 其他类型...
            else -> -1 // 默认值
        }
    }
    private fun setDesc(code: String, requirements: TextView) {
        when (code) {
            "YYZZ" -> {
                requirements.setText(R.string.string_aptitude_yyzz)
            }
            "YPJY" -> {
                requirements.setText(R.string.string_aptitude_ypjy)
            }

            "FRSQ" -> {
                requirements.setText(R.string.string_aptitude_frsq)
            }

            "ZYXK" -> {
                requirements.setText(R.string.string_aptitude_zyxk)
            }

            "WTRZ" -> {
                requirements.setText(R.string.string_aptitude_wtrz)
            }

            "KPXX" -> {
                requirements.setText(R.string.string_aptitude_kpxx)
            }

            else -> {
                requirements.text = ""
            }
        }
    }

    override fun getMaxWidth(): Int {
        return XPopupUtils.getWindowWidth(mContext)
    }

    override fun getPopupHeight(): Int {
        return (XPopupUtils.getWindowHeight(mContext) * 0.8).toInt()
    }
}