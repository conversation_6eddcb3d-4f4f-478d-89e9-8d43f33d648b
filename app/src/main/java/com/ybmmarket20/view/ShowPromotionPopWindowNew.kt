package com.ybmmarket20.view

import android.content.Context
import android.graphics.Color
import android.text.TextUtils
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybm.app.bean.NetError
import com.ybmmarket20.R
import com.ybmmarket20.adapter.COUPON_TYPE_SPECIFIC_GOODS
import com.ybmmarket20.bean.*
import com.ybmmarket20.bean.promotion.SkuPromotionInfo
import com.ybmmarket20.common.BaseResponse
import com.ybmmarket20.common.JgTrackBean
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.common.jgGetCouponsTrack
import com.ybmmarket20.common.widget.RoundTextView
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.network.HttpManager
import com.ybmmarket20.report.coupon.CouponUtil
import com.ybmmarket20.report.coupon.ICouponEntryType
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.SpUtil
import com.ybmmarket20.utils.UiUtils
import com.ybmmarket20.utils.analysis.AnalysisConst.Cart.ACTION_MOREDISCOUNT_GIFTCARD_CLICK
import com.ybmmarket20.utils.analysis.AnalysisConst.Cart.ACTION_MOREDISCOUNT_GIFTCARD_EXPOSURE
import com.ybmmarket20.utils.analysis.XyyIoUtil
import com.ybmmarket20.utils.setCouponBtnStyle
import com.ybmmarket20.utils.setTitleAndTag
import com.ybmmarketkotlin.adapter.YBMBaseMultiItemAdapter
import com.ybmmarketkotlin.utils.tagStyle

/**
 * <AUTHOR> Brin
 * @date : 2020/12/11 - 15:28
 * @Description :
 * @version
 */
class ShowPromotionPopWindowNew : ShowPromotionAnalysisPopWindowNew {

    private var context: Context? = null

    lateinit var mPromotionDatas: MutableList<MultiItemTagAndCoupon>
    private var mPromotionRv: RecyclerView? = null
    private var mPromotionAdapter: YBMBaseAdapter<MultiItemTagAndCoupon>? = null
    private var mCurGoods: RowsBean? = null

    var mJgTrackBean: JgTrackBean? = null

    constructor(context: Context) : super() {
        this.context = context
        initDadaAndView()
    }

    constructor(context: Context, iCouponEntryType: ICouponEntryType?): this(context) {
        mCouponEntryType = iCouponEntryType
    }

    private fun initDadaAndView() {
        mPromotionDatas = mutableListOf()

        contentView.findViewById<View>(R.id.product_detail_btn).setOnClickListener { dismiss() }
        contentView.findViewById<View>(R.id.bg).setOnClickListener { dismiss() }

        mPromotionRv = getView(R.id.rl_tags)
        mPromotionAdapter = TagAndCouponAdapter(mPromotionDatas)
        mPromotionRv?.layoutManager = LinearLayoutManager(context)
        mPromotionRv?.setAdapter(mPromotionAdapter)
    }

    override fun getLayoutId(): Int {
        return R.layout.show_promotion_pop_new
    }

    override fun initView() {}

    /**
     * @param voucherTemplateId
     * @return
     */
    private fun getVoucherParams(voucherTemplateId: Int): RequestParams? {
        val merchantid = SpUtil.getMerchantid()
        val mVoucherParams = RequestParams()
        mVoucherParams.url = AppNetConfig.RECEIVE_USABLE_VOUCHER
        mVoucherParams.put("merchantId", merchantid)
        //商品id
        if (voucherTemplateId > 0) {
            mVoucherParams.put("voucherTemplateId", voucherTemplateId.toString() + "")
        }
        return mVoucherParams
    }

    /**
     * 点击立即领取，改变当前item的状态为2
     *
     * @param voucherTemplateId
     * @param position
     */
    private fun getVoucher(voucherTemplateId: Int, position: Int) {
        HttpManager.getInstance().post(getVoucherParams(voucherTemplateId), object : BaseResponse<BaseBean<*>?>() {
            override fun onSuccess(content: String?, obj: BaseBean<BaseBean<*>?>?, t: BaseBean<*>?) {
                if (obj != null && obj.isSuccess) {
                    mPromotionDatas[position].coupon?.setState(2)
                    mPromotionAdapter?.notifyItemChanged(position)
                }
            }

            override fun onFailure(error: NetError) {
                super.onFailure(error)
            }
        })
    }

    fun setRowsBean(rowsBean: RowsBean?) {
        mCurGoods = rowsBean
    }

    fun setSkuId(id: String) {

        HttpManager.getInstance().post(AppNetConfig.SKU_PROMOTION, RequestParams().apply {
            put("csuId", id)
            put("merchantId", SpUtil.getMerchantid())
        },
                object : BaseResponse<SkuPromotionInfo>() {
                    override fun onSuccess(content: String?, obj: BaseBean<SkuPromotionInfo>?, t: SkuPromotionInfo?) {
                        mPromotionDatas.takeIf { it.size > 0 }?.let {
                            it.clear()
                        }
                        // 构建标签数据
                        t?.actTags?.forEach {
                            mPromotionDatas.add(MultiItemTagAndCoupon(tag = it).apply { itemType = MultiItemTagAndCoupon.TagBeanType })
                        }
                        t?.avaliableReceiveVoucherList?.takeIf { it.size > 0 }?.let {
                            // 构建未领取红包头数据
                            mPromotionDatas.add(MultiItemTagAndCoupon().apply { itemType = MultiItemTagAndCoupon.UnreceivedCouponHeaderType })
                        }
                        // 构建未领取红包数据
                        t?.avaliableReceiveVoucherList?.forEach {
                            mPromotionDatas.add(MultiItemTagAndCoupon(coupon = it).apply { itemType = MultiItemTagAndCoupon.CouponType })
                        }
                        t?.receivedVoucherList?.takeIf { it.size > 0 }?.let {
                            // 构建已领取红包头数据
                            mPromotionDatas.add(MultiItemTagAndCoupon().apply { itemType = MultiItemTagAndCoupon.ReceivedCouponHeaderType })
                        }
                        // 构建已领取红包数据
                        t?.receivedVoucherList?.forEach {
                            mPromotionDatas.add(MultiItemTagAndCoupon(coupon = it).apply { itemType = MultiItemTagAndCoupon.CouponType })
                        }
                        mapCouponIdAndPosition(t?.avaliableReceiveVoucherList, t?.receivedVoucherList)
                        mPromotionAdapter?.notifyDataSetChanged()
                    }

                })
    }

    inner class ItemEventListener : CSUListAdapter.CSUItemEventListener{
        override fun onItemClick(skuId: String?) {
            if (skuId == null) return
            val m = hashMapOf(Pair("productId", skuId))
            XyyIoUtil.track(ACTION_MOREDISCOUNT_GIFTCARD_CLICK, m)
            <EMAIL>()
        }

        override fun onItemExposure(skuId: String?) {
            if (skuId == null) return
            val m = hashMapOf(Pair("productId", skuId))
            XyyIoUtil.track(ACTION_MOREDISCOUNT_GIFTCARD_EXPOSURE, m)
        }
    }

    private val mItemEventListener : CSUListAdapter.CSUItemEventListener = ItemEventListener()

    inner class TagAndCouponAdapter :
        YBMBaseMultiItemAdapter<MultiItemTagAndCoupon> {
        constructor(data: List<MultiItemTagAndCoupon>) : super(data) {
            addItemType(MultiItemTagAndCoupon.TagBeanType, R.layout.popwindow_show_promotion)
            addItemType(MultiItemTagAndCoupon.UnreceivedCouponHeaderType, R.layout.item_promotion_coupon_header)
            addItemType(MultiItemTagAndCoupon.ReceivedCouponHeaderType, R.layout.item_promotion_coupon_header)
            addItemType(MultiItemTagAndCoupon.CouponType, R.layout.item_show_bottom_car_coupon_dialog_list)
        }

        override fun bindItemView(baseViewHolder: YBMBaseHolder, t: MultiItemTagAndCoupon) {
            when (t.itemType) {
                MultiItemTagAndCoupon.TagBeanType -> bindTagsItemView(baseViewHolder, t.tag)
                MultiItemTagAndCoupon.UnreceivedCouponHeaderType -> bindCouponHeaderItemView(baseViewHolder, t)
                MultiItemTagAndCoupon.ReceivedCouponHeaderType -> bindCouponHeaderItemView(baseViewHolder, t)
                MultiItemTagAndCoupon.CouponType -> bindCouponItem(baseViewHolder, t?.coupon)
            }
        }

        private fun bindCouponHeaderItemView(baseViewHolder: YBMBaseHolder, t: MultiItemTagAndCoupon) {
            when (t?.itemType) {
                MultiItemTagAndCoupon.UnreceivedCouponHeaderType -> baseViewHolder.setText(R.id.tv_coupon_head_title, "可领取优惠券")
                MultiItemTagAndCoupon.ReceivedCouponHeaderType -> baseViewHolder.setText(R.id.tv_coupon_head_title, "已领取优惠券")
            }
        }


        private fun bindCouponItem(baseHolder: YBMBaseHolder, voucherListBean: VoucherListBean?) {
            voucherListBean?.let {
                baseHolder.setGone(R.id.rv_cart_bottom_coupon_goods, false)
                bindBaseItem(baseHolder, it)
            }
        }

        private fun bindBaseItem(ybmBaseHolder: YBMBaseHolder, voucherListBean: VoucherListBean) {
            val tvPriceUnit = ybmBaseHolder.getView<TextView>(R.id.tv_PriceUnit)
            val tvDiscountUnit = ybmBaseHolder.getView<TextView>(R.id.tv_discount_unit)
            val tvCouponAmount = ybmBaseHolder.getView<TextView>(R.id.tv_coupon_amount)
            val tvCouponTitle = ybmBaseHolder.getView<TextView>(R.id.tv_coupon_title)
            val tvCouponSubTitle = ybmBaseHolder.getView<TextView>(R.id.tv_coupon_subtitle)
            val tvCouponLimt = ybmBaseHolder.getView<TextView>(R.id.tv_coupon_limit)
            val tvCouponFullReduceMax = ybmBaseHolder.getView<TextView>(R.id.tv_coupon_full_reduce_max)
            val tvAmountTips = ybmBaseHolder.getView<TextView>(R.id.tv_amount_tips)
            val clGoodsCouponBgLeft = ybmBaseHolder.getView<ConstraintLayout>(R.id.cl_goods_coupon_bg_left)
            val tvCouponFullReduce = ybmBaseHolder.getView<TextView>(R.id.tv_coupon_full_reduce)

            //设置专品券和其他券文字背景不同
            val normalDrawable = ContextCompat.getDrawable(
                mContext,
                R.drawable.shape_item_goods_coupon_left
            )
            val specificGoodsDrawable = ContextCompat.getDrawable(
                mContext,
                R.drawable.shape_item_goods_coupon_left_specific_goods
            )
            clGoodsCouponBgLeft.background = if (voucherListBean.voucherType == COUPON_TYPE_SPECIFIC_GOODS) {
                specificGoodsDrawable
            } else {
                normalDrawable
            }
            val textColor = if (voucherListBean.voucherType == COUPON_TYPE_SPECIFIC_GOODS) {
                R.color.color_ff155d
            } else R.color.color_ff4244
            tvCouponAmount.setTextColor(ContextCompat.getColor(mContext, textColor))
            tvPriceUnit.setTextColor(ContextCompat.getColor(mContext, textColor))
            tvDiscountUnit.setTextColor(ContextCompat.getColor(mContext, textColor))
            tvCouponFullReduce.setTextColor(ContextCompat.getColor(mContext, textColor))
            tvCouponFullReduceMax.setTextColor(ContextCompat.getColor(mContext, textColor))


            setTitleAndTag(mContext, voucherListBean.getVoucherType(), voucherListBean.getShopName(), voucherListBean.getVoucherTypeDesc(), tvCouponTitle)
            tvCouponSubTitle.text = voucherListBean.voucherTitle
            tvCouponLimt.text = voucherListBean.voucherScope

            if (voucherListBean.voucherState == 1) {
                tvPriceUnit.visibility = View.GONE
                tvDiscountUnit.visibility = View.VISIBLE
//                val discount = UiUtils.transform2Int(voucherListBean.discount.toDouble())
                tvCouponAmount.text = voucherListBean.discount
            } else {
                tvPriceUnit.visibility = View.VISIBLE
                tvDiscountUnit.visibility = View.GONE
                tvCouponAmount.text = UiUtils.transformInt(voucherListBean.moneyInVoucher)
            }
            //根据是否是叠加券显示按钮
            val isVoucherDemo = !TextUtils.isEmpty(voucherListBean.voucherDemo)
            // 最高减信息 显示文案
            if (!TextUtils.isEmpty(voucherListBean.maxMoneyInVoucherDesc)) {
                tvCouponFullReduceMax.setVisibility(View.VISIBLE)
                tvCouponFullReduceMax.setText(voucherListBean.maxMoneyInVoucherDesc)
            } else {
                tvCouponFullReduceMax.setVisibility(View.GONE)
            }
            //有效时间-叠加券文案
            if (isVoucherDemo) {
                ybmBaseHolder.setText(R.id.tv_coupon_date, voucherListBean.voucherDemo)
            } else {
                ybmBaseHolder.setText(R.id.tv_coupon_date, voucherListBean.validDateToString + "-" + voucherListBean.expireDateToString)
            }
            //优惠券 适用金额限制条件
            ybmBaseHolder.setText(R.id.tv_coupon_full_reduce, voucherListBean.minMoneyToEnableDesc)

            tvAmountTips.visibility = View.GONE

            tvAmountTips.text = voucherListBean.title
            val rtv = ybmBaseHolder.getView<RoundTextView>(R.id.tv_coupon_immediate_use)
            val tvGetItNow = ybmBaseHolder.getView<TextView>(R.id.tv_get_it_now)
            setCouponBtnStyle(voucherListBean.voucherType, tvGetItNow)
            val isShowUnreceive = voucherListBean.state != VoucherListBean.COUPON_STATUS_RECEIVED
            val clickText = if (isShowUnreceive) tvGetItNow.text.toString() else rtv.text.toString()
            rtv.setOnClickListener { v: View? ->
                //去凑单
                val routerUrl = if (TextUtils.isEmpty(voucherListBean.appUrl)) {
                    "ybmpage://couponavailableactivity/" + voucherListBean.templateId + "/" + 1
                } else {
                    voucherListBean.appUrl
                }.let {
                    CouponUtil.wrapperRouterUrlWithParam(it, mCouponEntryType?.getCouponEntryType()?: "", mCurGoods)
                }

                RoutersUtils.open(routerUrl)
                dismiss()
            }
            tvGetItNow.setOnClickListener { v: View? ->
                //立即领取
                getVoucher(voucherListBean.getVoucherTemplateId(), ybmBaseHolder.layoutPosition)

                var couponPrice = ""
                try {
                    couponPrice = if (voucherListBean.discount != null && voucherListBean.discount.isNotEmpty() && voucherListBean.discount.toDouble() > 0) {
                        voucherListBean.discount + "折"
                    } else {
                        "¥" + voucherListBean.moneyInVoucher
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }


                mContext.jgGetCouponsTrack(
                        mJgTrackBean,
                        voucherListBean.voucherTypeDesc,
                        couponPrice,
                        voucherListBean.validDateToString,
                        voucherListBean.expireDateToString)
            }
            ybmBaseHolder.setGone(R.id.tv_get_it_now, isShowUnreceive)
            ybmBaseHolder.setGone(R.id.tv_coupon_immediate_use, !isShowUnreceive)
        }

        private fun bindTagsItemView(holder: YBMBaseHolder, bean: TagBean?) {
            var tagview: TextView = holder.getView<TextView>(R.id.tv_icon_type_01)
            tagview.tagStyle(bean)

            val iconContent = holder.getView<TextView>(R.id.tv_content_type_01)
            iconContent.setText(bean?.description)

            val csuListView = holder.getView<CSUListView>(R.id.csu_list)
            if(bean?.csuList != null && bean.csuList.size > 0){
                csuListView.setListData(bean.csuList, bean)
                csuListView.setItemEventListener(<EMAIL>)
                csuListView.visibility = View.VISIBLE
            } else {
                csuListView.visibility = View.GONE
            }

            val iv = holder.getView<ImageView>(R.id.iv)
            iv.visibility = if (!TextUtils.isEmpty(bean?.appUrl)) View.VISIBLE else View.INVISIBLE

            holder.getView<View>(R.id.ll_item).setOnClickListener {
                bean?.appUrl?.let {
                    RoutersUtils.open(bean?.appUrl)
                    dismiss()
                }
            }
        }
    }

}