package com.ybmmarket20.view.homesteady

import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.widget.ImageView
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.stx.xhb.xbanner.XBanner
import com.stx.xhb.xbanner.entity.SimpleBannerInfo
import com.ybm.app.common.ImageLoader.ImageHelper
import com.ybmmarket20.R
import com.ybmmarket20.bean.homesteady.BaseBannerBean
import com.ybmmarket20.common.splicingUrlWithParams
import com.ybmmarket20.utils.RoutersUtils

/**
 * <AUTHOR>
 * @date 2020-05-12
 * @description 首页banner
 */
class HomeSteadyBannerView(context: Context, attr: AttributeSet): XBanner(context, attr), IHomeSteady, XBanner.XBannerAdapter, XBanner.OnItemClickListener {

    private var analysisCallback: ((BaseBannerBean, Int) -> Unit)? = null

    init {
        loadImage(this)
        setOnItemClickListener(this)
    }

    override fun loadBanner(banner: XBanner?, model: Any?, view: View?, position: Int) {
        view?.tag = null
        model?.also {
            if (view is ImageView) {
//                view.scaleType = ImageView.ScaleType.CENTER_CROP
                ImageHelper.with(context).load(((it as BannerInfo).xBannerUrl as BaseBannerBean).getImageUrl()).placeholder(R.drawable.icon_home_steady_banner_plasehold)
                        .error(R.drawable.icon_home_steady_banner_plasehold).diskCacheStrategy(DiskCacheStrategy.SOURCE)
                        .into(view)
            }
        }
    }

    override fun onItemClick(banner: XBanner?, model: Any?, view: View?, position: Int) {
        model?.also {
            var action = ((it as BannerInfo).xBannerUrl as BaseBannerBean).getActionLink()?:""
            if (action.isNotEmpty()){
                action = splicingUrlWithParams(action, hashMapOf(Pair("entrance","我的广告位")))
            }

            RoutersUtils.open(action)
            clickEvent((it.xBannerUrl as BaseBannerBean), position)
        }
    }

    override fun initPlaceHold() {
        setData(generateDefault())
    }

    fun <T: BaseBannerBean> setData(data: MutableList<T>?) {
        if (data == null || data.isEmpty()) {
            visibility = View.GONE
            return
        } else {
            visibility = View.VISIBLE
        }
        setBannerData(transformData(data))
    }

    private fun <T: BaseBannerBean> transformData(banners: MutableList<T>?): MutableList<BannerInfo> {
        val resultList = mutableListOf<BannerInfo>()
        banners?.forEach {resultList.add(BannerInfo(it))}
        return resultList
    }

    // 生成默认参数
    private fun generateDefault(): MutableList<BaseBannerBean> = mutableListOf<BaseBannerBean>().apply { add(BaseBannerBean()) }

    /**
     * 点击事件埋点
     */
    private fun clickEvent(baseBannerInfo: BaseBannerBean, position: Int){
        analysisCallback?.invoke(baseBannerInfo, position)
//        val obj = JSONObject()
//        try {
//            obj.also {
//                it.put("action", baseBannerInfo.getActionLink())
//                it.put("offset", position)
//                it.put("image_url", baseBannerInfo.getImageUrl())
//            }
//            XyyIoUtil.track("action_Home_Banner", obj)
//        } catch (e: Exception) {
//            e.printStackTrace()
//        }
    }

    /**
     * 点击回调
     */
    fun setBannerAnalysisCallback(callback: (BaseBannerBean, Int) -> Unit) {
        analysisCallback = callback
    }
}

class BannerInfo(var baseBannerBean: BaseBannerBean): SimpleBannerInfo(){

    override fun getXBannerUrl(): Any = baseBannerBean

}