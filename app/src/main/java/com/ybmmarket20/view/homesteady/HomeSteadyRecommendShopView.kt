package com.ybmmarket20.view.homesteady

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.Rect
import android.util.AttributeSet
import android.view.View
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.ItemDecoration
import com.luck.picture.lib.tools.ScreenUtils
import com.ybm.app.view.WrapGridLayoutManager
import com.ybmmarket20.R
import com.ybmmarket20.bean.RecommendShopInfoModule
import com.ybmmarket20.view.homesteady.callback.IRecommendShopAnalysisCallback
import com.ybmmarket20.view.homesteady.callback.IShopAnalysisCallback
import kotlinx.android.synthetic.main.layout_home_steady_shop.view.*

/**
 * 首页人气好店
 */

class HomeSteadyRecommendShopView(context: Context, attrs: AttributeSet?) : BaseHomeSteadyView(context, attrs) {


    override fun getLayoutId(): Int = R.layout.layout_home_steady_recommend_shop

    private var analysisCallback: IRecommendShopAnalysisCallback? = null

    override fun initPlaceHold() {
    }

    fun setData(recommendShopInfo: RecommendShopInfoModule?, callback: ()-> Unit) {

        tv_shop_moudle_title.text = recommendShopInfo?.content?.title
        moreShop.text = recommendShopInfo?.content?.actionName

        moreShop.setOnClickListener {
            callback()
            analysisCallback?.onHomeSteadyAnalysisRecommendShopMoreClick("店铺列表页", "店铺查看更多")
        }

        val rv = findViewById<RecyclerView>(R.id.rv)
        if (rv.itemDecorationCount > 0) rv.removeItemDecorationAt(0)
        rv.addItemDecoration(HomeSteadyRecommendShopDecoration(context))
        rv.layoutManager = WrapGridLayoutManager(context, 2)
        val homeSteadyShopAdapter = HomeSteadyRecommendShopAdapter(recommendShopInfo?.content?.list ?: mutableListOf(), analysisCallback)
        rv.adapter = homeSteadyShopAdapter
    }

    /**
     * 埋点回调
     */
    fun setAnalysisCallback(callback: IRecommendShopAnalysisCallback) {
        analysisCallback = callback
    }
}

class HomeSteadyRecommendShopDecoration(val mContext: Context) : ItemDecoration() {
    private var mDividerHeight: Float = ScreenUtils.dip2px(mContext, 0.5f).toFloat() //线的高度
    private val mPaint: Paint = Paint() //画笔将自己做出来的分割线矩形画出颜色
    private var margin = 0f //左右偏移量

    fun setMargin(margin: Float): HomeSteadyRecommendShopDecoration {
        this.margin = margin
        return this
    }

    fun setColor(color: Int): HomeSteadyRecommendShopDecoration {
        mPaint.color = color
        return this
    }

    fun setDividerHeight(height: Float): HomeSteadyRecommendShopDecoration {
        mDividerHeight = height
        return this
    }

    override fun getItemOffsets(
        outRect: Rect,
        view: View,
        parent: RecyclerView,
        state: RecyclerView.State
    ) {
        super.getItemOffsets(outRect, view, parent, state)
        if (parent.getChildAdapterPosition(view) != 0 && parent.getChildAdapterPosition(view) != 1) {
            outRect.top = mDividerHeight.toInt() + ScreenUtils.dip2px(mContext, 5f) //指相对itemView顶部的偏移量
        }

        if (parent.getChildAdapterPosition(view) % 2 == 0) {
            outRect.left = 0
            outRect.right = ScreenUtils.dip2px(mContext, 5f)
        } else {
            outRect.left = ScreenUtils.dip2px(mContext, 5f)
            outRect.right = 0
        }
    }

    override fun onDraw(c: Canvas, parent: RecyclerView, state: RecyclerView.State) {
        super.onDraw(c, parent, state)
        val childCount = parent.childCount
        //因为getItemOffsets是针对每一个ItemView，而onDraw方法是针对RecyclerView本身，所以需要循环遍历来设置
        for (i in 0 until childCount) {
            val view = parent.getChildAt(i)
            val index = parent.getChildAdapterPosition(view)
            if (index % 2 == 0) {
                val vDividerTop = view.top.toFloat() + ScreenUtils.dip2px(mContext, 10f)
                val vDividerLeft = view.right - mDividerHeight + ScreenUtils.dip2px(mContext, 5f)
                val vDividerBottom = view.bottom.toFloat() - ScreenUtils.dip2px(mContext, 10f)
                val vDividerRight = view.right.toFloat() + ScreenUtils.dip2px(mContext, 5f)
                c.drawRect(vDividerLeft, vDividerTop, vDividerRight, vDividerBottom, mPaint)
            }

            if (index == 0 || index == 1) {
                continue  //跳过本次循环体中尚未执行的语句，立即进行下一次的循环条件判断
            }
            val dividerTop = view.top - mDividerHeight - ScreenUtils.dip2px(mContext, 5f)
            val dividerLeft = parent.paddingLeft + margin
            val dividerBottom = view.top.toFloat() - ScreenUtils.dip2px(mContext, 5f)
            val dividerRight = parent.width - parent.paddingRight - margin
            c.drawRect(dividerLeft, dividerTop, dividerRight, dividerBottom, mPaint)

        }
    }

    init {
        mPaint.isAntiAlias = true //抗锯齿
        mPaint.color = Color.parseColor("#EEEEEE") //默认颜色
    }
}