package com.ybmmarket20.view.homesteady;

import android.content.Context;
import android.util.AttributeSet;
import android.util.Log;
import android.view.MotionEvent;
import android.view.View;
import androidx.annotation.NonNull;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import com.google.android.material.appbar.AppBarLayout;
import com.ybmmarket20.R;
import org.jetbrains.annotations.NotNull;

public class HomeSteadyScrollingViewBehavior extends AppBarLayout.ScrollingViewBehavior {

    private View tabLayout;
    private View appBarLayout;

    public HomeSteadyScrollingViewBehavior(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    @Override
    public boolean onInterceptTouchEvent(@NonNull @NotNull CoordinatorLayout parent, @NonNull @NotNull View child, @NonNull @NotNull MotionEvent ev) {
//        int[] tabPosition = new int[2];
//        tabLayout.getLocationInWindow(tabPosition);
//        int[] appBarPosition = new int[2];
//        appBarLayout.getLocationInWindow(appBarPosition);
//        Log.i("tabPosition", tabPosition[1]+"");
//        Log.i("appBarPosition", appBarPosition[1]+"");
//        if (tabPosition[1] == appBarPosition[1]){
//            return true;
//        }
        return super.onInterceptTouchEvent(parent, child, ev);
    }

    @Override
    public boolean layoutDependsOn(CoordinatorLayout parent, View child, View dependency) {
//        if (tabLayout == null && dependency instanceof AppBarLayout) {
//            appBarLayout = dependency;
//            tabLayout = appBarLayout.findViewById(R.id.homeTabLayout);
//        }
        return super.layoutDependsOn(parent, child, dependency);
    }
}
