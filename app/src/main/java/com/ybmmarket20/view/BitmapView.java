package com.ybmmarket20.view;

import android.app.Activity;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.media.MediaScannerConnection;
import android.net.Uri;
import android.os.Environment;
import android.provider.MediaStore;
import android.text.TextUtils;
import android.view.View;

import com.ybm.app.common.BaseYBMApp;
import com.ybm.app.common.SmartExecutorManager;
import com.ybm.app.utils.FileStorageUtil;
import com.ybmmarket20.activity.TheInvitationActivity;
import com.ybmmarket20.common.util.ToastUtils;
import com.ybmmarket20.utils.BitmapUtil;
import com.ybmmarket20.utils.FileUtil;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;

public class BitmapView {

    //然后View和其内部的子View都具有了实际大小，也就是完成了布局，相当与添加到了界面上。接着就可以创建位图并在上面绘制了：
    public static void layoutView(View v, int width, int height) {
        // 整个View的大小 参数是左上角 和右下角的坐标
        v.layout(0, 0, width, height);
        int measuredWidth = View.MeasureSpec.makeMeasureSpec(width, View.MeasureSpec.EXACTLY);
        int measuredHeight = View.MeasureSpec.makeMeasureSpec(10000, View.MeasureSpec.AT_MOST);
        /** 当然，measure完后，并不会实际改变View的尺寸，需要调用View.layout方法去进行布局。
         * 按示例调用layout函数后，View的大小将会变成你想要设置成的大小。
         */
        v.measure(measuredWidth, measuredHeight);
        v.layout(0, 0, v.getMeasuredWidth(), v.getMeasuredHeight());
    }

    public static Bitmap viewSaveToImage(View view, Activity act) {

        String path = act.getCacheDir().getAbsolutePath() + "/ybm_" + System.currentTimeMillis() + ".png";

        try {

            // 把一个View转换成图片
            Bitmap cachebmp = loadBitmapFromView(view);

            File file = BitmapUtil.bitmapToFile(cachebmp, path);
            // 首先保存图片
            File pictureFolder = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DCIM).getAbsoluteFile();
            File appDir = new File(pictureFolder, path);
            if (!appDir.exists()) {
                appDir.mkdirs();
            }
            String name = path;
            if (TextUtils.isEmpty(name)) {
                name = System.currentTimeMillis() + ".png";
            }
            if (!name.endsWith(".png") && !name.endsWith(".jpg")) {
                name = name + ".png";
            }
            final File destFile = new File(appDir, name);
            if (destFile.exists()) {
                try {
                    destFile.delete();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            FileStorageUtil.copyFile(file, destFile);
            //其次把文件插入到系统图库
//            try {
//                MediaStore.Images.Media.insertImage(act.getContentResolver(),
//                        destFile.getAbsolutePath(), name, null);
//            } catch (FileNotFoundException e) {
//                e.printStackTrace();
//            }
            // 最后通知图库更新
            BaseYBMApp.getApp().sendBroadcast(new Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE, Uri.fromFile(new File(destFile.getPath()))));
            SmartExecutorManager.getInstance().executeUI(new Runnable() {
                @Override
                public void run() {
                    ToastUtils.showShort("图片保存成功");
                    updateMedia(destFile.getAbsolutePath());
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
            SmartExecutorManager.getInstance().executeUI(new Runnable() {
                @Override
                public void run() {
                    ToastUtils.showShort("图片保存出错");
                }
            });
        }
        return BitmapUtil.compressFile(path);
    }

    public static Bitmap viewSave2ToImage(View view) {
        Bitmap cachebmp = null;
        try {
            // 把一个View转换成图片
            cachebmp = loadBitmapFromView(view);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return cachebmp;
    }


    private static Bitmap loadBitmapFromView(View v) {
        int w = v.getWidth();
        int h = v.getHeight();
        Bitmap bmp = Bitmap.createBitmap(w, h, Bitmap.Config.ARGB_8888);
        Canvas c = new Canvas(bmp);

        /** 如果不设置canvas画布为白色，则生成透明 */
//        c.drawColor(Color.WHITE);

        v.layout(0, 0, w, h);
        v.draw(c);

        return bmp;
    }

    //保存在本地并一键分享
    private static String sharePic(Bitmap cachebmp, String child) {
        final File qrImage = new File(Environment.getExternalStorageDirectory(), child + ".jpg");
        if (qrImage.exists()) {
            qrImage.delete();
        }
        try {
            qrImage.createNewFile();
        } catch (IOException e) {
            e.printStackTrace();
        }
        FileOutputStream fOut = null;
        try {
            fOut = new FileOutputStream(qrImage);
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        }
        if (cachebmp == null) {
            return "";
        }
        cachebmp.compress(Bitmap.CompressFormat.JPEG, 100, fOut);
        try {
            fOut.flush();
            fOut.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return qrImage.getPath();
    }

    public static void updateMedia(String... path) {
        MediaScannerConnection.scanFile(BaseYBMApp.getAppContext(), path, new String[]{"image/jpeg", "image/png"}, null);
    }

}
