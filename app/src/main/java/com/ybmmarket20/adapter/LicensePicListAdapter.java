package com.ybmmarket20.adapter;

import android.content.Context;
import androidx.annotation.Nullable;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.luck.picture.lib.tools.DoubleUtils;
import com.ybm.app.adapter.YBMBaseAdapter;
import com.ybm.app.adapter.YBMBaseHolder;
import com.ybmmarket20.R;
import com.ybmmarket20.common.AlertDialogEx;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.utils.ImageLoader;
import com.ybmmarket20.utils.UiUtils;
import com.ybmmarket20.view.ShowBigBitmapPopPublishForLongPic;
import com.ybmmarket20.view.WrapContentLinearLayoutManager;

import java.util.List;

public class LicensePicListAdapter extends YBMBaseAdapter<LicensePicListAdapter.ImageInfo> {
    public boolean withEdit;
    public String typeStr="";
    public static final String EDIT_FLAG = "#add_item#";
    public int maxSize = 3;
    public int savedStatus = 0;  //标识资质状态 0：正常 1：过期 2：临期
    public WrapContentLinearLayoutManager linearLayoutManager;
    private int editFlagDrawable = R.drawable.ic_add_image;

    public void setEditFlagDrawable(int drawable) {
        editFlagDrawable = drawable;
    }

    public LicensePicListAdapter(int layoutResId, @Nullable List<ImageInfo> data, boolean withEdit,String typeStr, WrapContentLinearLayoutManager linearLayoutManager) {
        super(layoutResId, data);
        this.withEdit = withEdit;
        this.typeStr = typeStr;
        this.linearLayoutManager = linearLayoutManager;
    }

    public LicensePicListAdapter(int layoutResId, @Nullable List<ImageInfo> data, boolean withEdit, WrapContentLinearLayoutManager linearLayoutManager) {
        super(layoutResId, data);
        this.withEdit = withEdit;
        this.linearLayoutManager = linearLayoutManager;
    }

    @Override
    protected void bindItemView(final YBMBaseHolder helper, final ImageInfo item) {
        if (helper == null || item == null) return;
        final ImageView imgCover = helper.getView(R.id.iv_cover);
        ImageView iv_del = helper.getView(R.id.iv_del);
        LinearLayout ll_del = helper.getView(R.id.ll_del);
        TextView tvSavedStatus = helper.getView(R.id.tv_saved_status);

        imgCover.setImageDrawable(null);

        // 资质条目中的图片第一张, 增加过期，即将过期等的提示
        if (getItemCount() > 1 && (savedStatus == 1 || savedStatus == 2) && helper.getAdapterPosition() == 0) {
            tvSavedStatus.setVisibility(View.VISIBLE);
            switch (savedStatus) {
                case 1:
                    tvSavedStatus.setText("已过期");
                    tvSavedStatus.setBackgroundColor(UiUtils.getColor(R.color.color_ff3024));
                    break;
                case 2:
                    tvSavedStatus.setText("即将过期");
                    tvSavedStatus.setBackgroundColor(UiUtils.getColor(R.color.color_FF7200));
                    break;
            }
        } else {
            tvSavedStatus.setVisibility(View.GONE);
        }

        if (withEdit && getItemCount() > 1 && !EDIT_FLAG.equals(item.localPath)) {//删除照片
            ll_del.setVisibility(View.VISIBLE);
            iv_del.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (!DoubleUtils.isFastDoubleClick()) {
                        deleteImage(helper.getAdapterPosition());
                    }
                }
            });
        } else {
            ll_del.setVisibility(View.GONE);
        }
        if (EDIT_FLAG.equals(item.localPath)) {
            imgCover.setImageResource(editFlagDrawable);
            imgCover.setOnLongClickListener(null);
            imgCover.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (listener != null) {
                        listener.addImage(LicensePicListAdapter.this,typeStr);
                    }
                }
            });
        } else {
            ImageLoader.loadImage(mContext, imgCover, item.getPath());
            imgCover.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (TextUtils.isEmpty(item.getPath())) {
                        return;
                    }
                    if (withEdit) {
                        // new ShowBigBitmapPopPublish(item.getPath()).show(imgCover);
                        new ShowBigBitmapPopPublishForLongPic(item.getPath(), 0, new ShowBigBitmapPopPublishForLongPic.RefreshGvListener() {
                            @Override
                            public void refreshGv(int pos) {
                                deleteImage(helper.getAdapterPosition());
                            }
                        }).show(imgCover);
                    } else {
                        new ShowBigBitmapPopPublishForLongPic(item.getPath()).show(imgCover);
                    }
                }
            });
        }
    }


    public void deleteImage(final int position) {
        String message = "确认删除照片?";
        String cancel = "取消";
        String confirm = "确定";
        commonDialog(mContext, null, message, confirm, cancel, new AlertDialogEx.OnClickListener() {
            @Override
            public void onClick(AlertDialogEx dialog, int button) {
                try {
                    remove(position);
                    ImageInfo item = (ImageInfo) getItem(getItemCount() - 1);
                    if (item != null && !EDIT_FLAG.equals(item.localPath)) {
                        showAddView();
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }, null);
    }

    public void showAddView() {
        LicensePicListAdapter.ImageInfo imageInfo = new LicensePicListAdapter.ImageInfo();
        imageInfo.localPath = LicensePicListAdapter.EDIT_FLAG;
        add(getItemCount(), imageInfo);

    }

    public void hideAddView() {
        ImageInfo item = (ImageInfo) getItem(getItemCount() - 1);
        if (EDIT_FLAG.equals(item.localPath)) {
            remove(getItemCount() - 1);

        }
    }

    private Listener listener;

    public void setListener(Listener listener) {
        this.listener = listener;
    }

    public void addSelectPic(List<ImageInfo> list) {
        for (ImageInfo s : list) {
            int position = getItemCount() - 1;
            add(position, s);
        }
        if (getItemCount() == maxSize + 1) {
            hideAddView();
        }
    }

    public void setMaxSize(int maxSize) {
        this.maxSize = maxSize;
    }

    public void setItemStatus(int savedStatus) {
        this.savedStatus = savedStatus;
    }


    public interface Listener {
        void addImage(LicensePicListAdapter adapter,String typeStr);
    }

    public int getAllowAddSize() {
        return maxSize + 1 - getItemCount();
    }

    /**
     * 显示对话框
     */
    private void commonDialog(Context context, String title, String msg, String confirm, String cancel,
                              AlertDialogEx.OnClickListener confirmListener, AlertDialogEx.OnClickListener cancelListener) {
        AlertDialogEx dialog = new AlertDialogEx(context);
        dialog.setTitle(title).setMessage(msg)
                .setCancelButton(cancel, cancelListener)
                .setConfirmButton(confirm, confirmListener)
                .show();
    }


    public static class ImageInfo {
        public String localPath;
        public String newPath;
        public String oldPath;

        public String getOldPath() {
            StringBuilder sb = new StringBuilder(AppNetConfig.getCDNHost());
            if (!TextUtils.isEmpty(oldPath) && oldPath.startsWith("/")) {
                sb.append(oldPath);
            } else if (TextUtils.isEmpty(oldPath)) {
                return "";
            } else if (oldPath.startsWith("http")) {
                return oldPath;
            } else {
                sb.append('/').append(oldPath);
            }
            return sb.toString();
        }

        //优先选择本地的path
        public String getPath() {
            if (TextUtils.isEmpty(localPath)) {
                return getOldPath();
            } else {
                return localPath;
            }
        }
    }
}
