package com.ybmmarket20.adapter

import android.widget.ImageView
import com.bumptech.glide.Glide
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.activity.BigPicActivity

/**
 *    author : 朱勇闯
 *    e-mail : <EMAIL>
 *    date   : 2024/10/28 11:22
 *    desc   :
 */
class ImageAdapter(
    var list: MutableList<String>
) : YBMBaseListAdapter<String>(R.layout.item_img, list) {
    override fun bindItemView(holder: YBMBaseHolder, t: String?) {
        if (!t.isNullOrEmpty()) {
            Glide.with(mContext).load(t).into(holder.getView(R.id.iv_img))
        } else {
            Glide.with(mContext).load(R.drawable.icon_goods_detail_shop_logo_default)
                .into(holder.getView(R.id.iv_img))
        }
        holder.getView<ImageView>(R.id.iv_img).setOnClickListener {
            val intent = BigPicActivity.getIntent(
                mContext,
                list.toTypedArray(),
                0,
                null,
                "大图"
            )
            mContext.startActivity(intent)
        }
    }
}