package com.ybmmarket20.adapter

import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.ReminderHistoryList
import com.ybmmarket20.utils.ifNotNull
import com.ybmmarketkotlin.adapter.YBMBaseMultiItemAdapter

/**
 *    author : 朱勇闯
 *    e-mail : <EMAIL>
 *    date   : 2024/10/25 14:17
 *    desc   :
 */
class RemindProgressAdapter(
    data: List<ReminderHistoryList>
) : YBMBaseMultiItemAdapter<ReminderHistoryList>(data) {
    companion object {
        const val TYPE_TEXT = 1
        const val TYPE_LIST = 2
    }

    init {
        addItemType(TYPE_TEXT, R.layout.item_remind_progress_text)
        addItemType(TYPE_LIST, R.layout.item_remind_progress_list)
    }

    override fun bindItemView(baseViewHolder: YBMBaseHolder?, t: ReminderHistoryList?) {
        ifNotNull(baseViewHolder, t) { holder, data ->
            if (data.itemType == TYPE_TEXT) {
                val view0 = holder.getView<View>(R.id.view0)
                val view1 = holder.getView<View>(R.id.view1)
                val title = holder.getView<TextView>(R.id.tv_title)
                val time = holder.getView<TextView>(R.id.tv_time)
                val content = holder.getView<TextView>(R.id.tv_content)
                val img = holder.getView<ImageView>(R.id.iv_img)
                if (holder.position == 0) {
                    view0.visibility = View.GONE
                    title.isSelected = true
                    img.isSelected = true
                } else {
                    title.isSelected = false
                    img.isSelected = false
                    view0.visibility = View.VISIBLE
                }

                if (holder.position == itemCount - 1) {
                    view1.visibility = View.GONE
                } else {
                    view1.visibility = View.VISIBLE
                }
                title.text = data.eventStatusStr
                time.text = data.historyCreateTime
                if (data.customFields?.prompt.isNullOrEmpty()) {
                    content.visibility = View.GONE
                } else {
                    content.visibility = View.VISIBLE
                    content.text = data.customFields?.prompt
                }
            } else {
                val view0 = holder.getView<View>(R.id.view0)
                val view1 = holder.getView<View>(R.id.view1)
                val title = holder.getView<TextView>(R.id.tv_title)
                val time = holder.getView<TextView>(R.id.tv_time)
                val img = holder.getView<ImageView>(R.id.iv_img)
                val type = holder.getView<TextView>(R.id.tv_type)
                val typeTitle = holder.getView<TextView>(R.id.tv_type_title)
                val tipsTitle = holder.getView<TextView>(R.id.tv_tips_title)
                val tips = holder.getView<TextView>(R.id.tv_tips)
                val imgTitle = holder.getView<TextView>(R.id.tv_img_title)
                val rvImg = holder.getView<RecyclerView>(R.id.rv_img)
                if (holder.position == 0) {
                    img.isSelected = true
                    view0.visibility = View.GONE
                    title.isSelected = true
                } else {
                    img.isSelected = false
                    view0.visibility = View.VISIBLE
                    title.isSelected = false
                }
                if (holder.position == itemCount - 1) {
                    view1.visibility = View.GONE
                } else {
                    view1.visibility = View.VISIBLE
                }
                title.text = data.eventStatusStr
                time.text = data.historyCreateTime
                if (data.customFields?.appealCategory.isNullOrEmpty()) {
                    type.visibility = View.GONE
                    typeTitle.visibility = View.GONE
                } else {
                    typeTitle.visibility = View.VISIBLE
                    type.visibility = View.VISIBLE
                    type.text = data.customFields?.appealCategory
                }
                if (data.customFields?.appealDescription.isNullOrEmpty()) {
                    tips.visibility = View.GONE
                    tipsTitle.visibility = View.GONE
                } else {
                    tips.visibility = View.VISIBLE
                    tipsTitle.visibility = View.VISIBLE
                    tips.text = data.customFields?.appealDescription
                }
                if (data.customFields?.appealEvidence.isNullOrEmpty()) {
                    imgTitle.visibility = View.GONE
                    rvImg.visibility = View.GONE
                } else {
                    imgTitle.visibility = View.VISIBLE
                    rvImg.visibility = View.VISIBLE
                    val imageAdapter =
                        data?.customFields?.appealEvidence?.let { ImageAdapter(it) }
                    rvImg.layoutManager = GridLayoutManager(mContext, 3)
                    rvImg.adapter = imageAdapter
                }
            }
        }
    }
}