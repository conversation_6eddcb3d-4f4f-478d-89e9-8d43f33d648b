package com.ybmmarket20.adapter

import android.widget.ImageView
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybm.app.common.ImageLoader.ImageHelper
import com.ybmmarket20.R
import com.ybmmarket20.bean.ProductDetail
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.utils.RoutersUtils

/**
 * 代下单授权详情Adapter
 */
class AuthorizationDetailAdapter(
        layoutResId: Int,
        data: List<ProductDetail>?
) : YBMBaseAdapter<ProductDetail>(layoutResId, data) {

    override fun bindItemView(baseViewHolder: YBMBaseHolder?, t: ProductDetail?) {
        if (baseViewHolder == null && t == null) return
        val ivAuthorizationDetail = baseViewHolder!!.getView<ImageView>(R.id.iv_authorization_detail)
        ImageHelper.with(mContext).load("${AppNetConfig.LORD_IMAGE}${t!!.photo}")
                .placeholder(R.drawable.jiazaitu_min)
                .diskCacheStrategy(DiskCacheStrategy.SOURCE)
                .dontAnimate()
                .into(ivAuthorizationDetail)
        baseViewHolder.setText(R.id.tv_authorization_title, t.productName)
        baseViewHolder.setText(R.id.tv_authorization_spec, "${mContext.resources.getString(R.string.authorization_goods_spec)}${t.spec}")
        baseViewHolder.setText(R.id.tv_authorization_manufacturer, "${mContext.resources.getString(R.string.authorization_goods_manufacturer)}${t.manufacturer}")
        baseViewHolder.convertView.setOnClickListener{
//            mContext.startActivity(Intent(mContext, ProductDetailActivity::class.java))
            RoutersUtils.open("ybmpage://productdetail/${t.productId}")
        }

    }
}