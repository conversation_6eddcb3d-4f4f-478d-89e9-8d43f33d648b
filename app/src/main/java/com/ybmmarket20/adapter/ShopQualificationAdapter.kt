package com.ybmmarket20.adapter

import android.annotation.SuppressLint
import android.content.Intent
import android.widget.TextView
import com.apkfuns.logutils.LogUtils
import com.google.gson.Gson
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.activity.AptitudeXyyPdfActivity
import com.ybmmarket20.bean.AptitudeXyyBean
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.view.homesteady.whenAllNotNull
import java.util.ArrayList

/**
 * 店铺资质
 */
class ShopQualificationAdapter(val list: MutableList<AptitudeXyyBean.RowsBean>, val isSelf: Boolean) :
    YBMBaseAdapter<AptitudeXyyBean.RowsBean>(R.layout.item_shop_qualification, list) {

    @SuppressLint("SetTextI18n")
    override fun bindItemView(baseViewHolder: YBMBaseHolder?, t: AptitudeXyyBean.RowsBean?) {
        whenAllNotNull(baseViewHolder, t) { holder, bean ->
            baseViewHolder?.getView<TextView>(R.id.tv_title)?.text = "${list.indexOf(bean).plus(1)}.${bean.name}"
            if (holder.layoutPosition == itemCount - 1) {
                holder.itemView.setBackgroundResource(R.drawable.shape_shop_qualification)
            } else {
                holder.itemView.setBackgroundResource(R.drawable.shape_shop_qualification_normal)
            }
        }
    }



}