package com.ybmmarket20.adapter;

import android.graphics.Color;
import android.graphics.Typeface;
import android.graphics.drawable.Drawable;
import androidx.core.content.ContextCompat;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextPaint;
import android.text.TextUtils;
import android.text.method.LinkMovementMethod;
import android.text.style.AbsoluteSizeSpan;
import android.text.style.ClickableSpan;
import android.view.View;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.ImageView;
import android.widget.TextView;

import com.apkfuns.logutils.LogUtils;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.load.resource.drawable.GlideDrawable;
import com.bumptech.glide.request.animation.GlideAnimation;
import com.bumptech.glide.request.target.SimpleTarget;
import com.ybm.app.adapter.YBMBaseAdapter;
import com.ybm.app.adapter.YBMBaseHolder;
import com.ybm.app.common.ImageLoader.ImageHelper;
import com.ybm.app.utils.BugUtil;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.PlanProductInfoBean;
import com.ybmmarket20.bean.RowsBean;
import com.ybmmarket20.common.util.ConvertUtils;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.utils.AuditStatusSyncUtil;
import com.ybmmarket20.utils.RoutersUtils;
import com.ybmmarket20.utils.UiUtils;
import com.ybmmarket20.view.MyImageSpan;
import com.ybmmarket20.view.SwipeMenuLayout;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by mbdn on 2017/4/7.
 */

public class PlanProductAdapter extends YBMBaseAdapter<PlanProductInfoBean> {
    protected OnItemClickListener mItemClickListener;
    protected OnSelectAllListener mSelectAllListener;
    protected OnSwipeListener mSwipeListener;


    public void setOnItemClickListener(OnItemClickListener listener) {
        mItemClickListener = listener;
    }

    public void setOnSelectAllListener(OnSelectAllListener listener) {
        mSelectAllListener = listener;
    }

    public void setOnSwipeListener(OnSwipeListener listener) {
        mSwipeListener = listener;
    }

    public PlanProductAdapter(int layoutResId, List<PlanProductInfoBean> data) {
        super(layoutResId, data);
    }


    @Override
    protected void bindItemView(final YBMBaseHolder holder, final PlanProductInfoBean bean) {
        ((SwipeMenuLayout) holder.itemView).setExpandListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mSwipeListener != null) {
                    mSwipeListener.onSwipe(holder.getAdapterPosition());
                }
            }
        });
//        holder.setText(R.id.tv_product_name, TextUtils.isEmpty(bean.productName) ? "暂无" : bean.productName);
//        holder.setText(R.id.tv_product_standard, "规格:"+bean.spec);
        if (TextUtils.isEmpty(bean.productName)) {
            bean.productName = "暂无";
        }
        TextView tv_shop_name = holder.getView(R.id.tv_product_name);
        tv_shop_name.setText(bean.productName + "/" + bean.spec);
        setGoodsName(tv_shop_name, bean);
        holder.setText(R.id.tv_product_company, bean.manufacturer);
        holder.setText(R.id.tv_product_price, TextUtils.isEmpty(bean.getPrice()) ? "历史价格:暂无" : "历史价格:¥" + bean.price);
        TextView tvNum = holder.getView(R.id.tv_product_num);
        tvNum.setText("补货数量：" + bean.purchaseNumber);
        holder.getView(R.id.tv_product_price).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mItemClickListener != null) {
                    mItemClickListener.onEditNum(holder.getAdapterPosition());
                }
            }
        });

        holder.getView(R.id.iv_edit).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mItemClickListener != null) {
                    mItemClickListener.onEditNum(holder.getAdapterPosition());
                }
            }
        });

        holder.getView(R.id.rl_root).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mItemClickListener != null) {
                    mItemClickListener.onItemClick(holder.getAdapterPosition());
                }
            }
        });

        holder.getView(R.id.tv_delete).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mSwipeListener != null) {
                    ((SwipeMenuLayout) holder.itemView).quickClose();
                    mSwipeListener.onDelete(holder.getAdapterPosition());
                }
            }
        });
        handleAuditPassedVisible(holder);
    }

    /**
     * 显示商品name+规格 and head show tag
     *
     * @param tv_shop_name
     * @param rowsBean
     */
    private void setGoodsName(TextView tv_shop_name, PlanProductInfoBean rowsBean) {

        String showContent = rowsBean.productName + "/" + rowsBean.spec;//商品名称和规格
        int nameLength = TextUtils.isEmpty(rowsBean.productName) ? 0 : rowsBean.productName.length();
        setShowActivityTag(tv_shop_name, showContent, nameLength);

    }

    private void setShowActivityTag(TextView textView, String showName, int nameLength) {

        SpannableStringBuilder shopName = getShopNameIcon(textView, showName, nameLength);
        textView.setText(shopName);

    }

    private SpannableStringBuilder getShopNameIcon(TextView textView, String shopName, int nameLength) {
        SpannableStringBuilder spannableString = new SpannableStringBuilder(shopName);
        try {
            spannableString.setSpan(new ClickableSpan() {

                @Override
                public void updateDrawState(TextPaint ds) {
                    super.updateDrawState(ds);
                    //设置文件颜色
                    ds.setColor(UiUtils.getColor(R.color.text_9494A6));
                    //设置下划线
                    ds.setUnderlineText(false);
                    ds.setTypeface(Typeface.DEFAULT);
                }

                @Override
                public void onClick(View view) {

                }
            }, nameLength, spannableString.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            //设置点击后的颜色为透明，否则会一直出现高亮
            textView.setHighlightColor(Color.TRANSPARENT);
            textView.setText(spannableString);
            //开始响应点击事件
            textView.setMovementMethod(LinkMovementMethod.getInstance());

//        SpannableStringBuilder spannableString = new SpannableStringBuilder(shopName);
            spannableString.setSpan(new AbsoluteSizeSpan(16, true), 0, nameLength + 1, Spannable.SPAN_INCLUSIVE_INCLUSIVE);
            spannableString.setSpan(new AbsoluteSizeSpan(12, true), nameLength + 1, shopName.length(), Spannable.SPAN_INCLUSIVE_INCLUSIVE);
        } catch (Exception e) {
            BugUtil.sendBug(e);

        }
        return spannableString;
    }

    public interface OnItemClickListener {
        void onItemClick(int position);

        void onEditNum(int position);
    }

    public interface OnSelectAllListener {
        void isChecked(boolean isChecked, PlanProductInfoBean bean);
    }

    public interface OnSwipeListener {
        void onDelete(int position);

        void onSwipe(int position);
    }

    /**
     * 处理价格认证资质可见
     */
    private void handleAuditPassedVisible(YBMBaseHolder baseViewHolder) {
        baseViewHolder.setGone(R.id.tv_product_price, AuditStatusSyncUtil.getInstance().isAuditFirstPassed());
        baseViewHolder.setGone(R.id.tv_audit_passed_visible, !AuditStatusSyncUtil.getInstance().isAuditFirstPassed());
    }
}
