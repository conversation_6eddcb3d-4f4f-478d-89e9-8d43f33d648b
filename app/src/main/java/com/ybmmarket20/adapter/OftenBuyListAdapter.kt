package com.ybmmarket20.adapter

import android.text.SpannableStringBuilder
import android.util.SparseArray
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.StaggeredGridLayoutManager
import com.chad.library.adapter.base.BaseViewHolder
import com.ybm.app.adapter.YBMBaseHolder
import com.ybm.app.bean.AbstractMutiItemEntity
import com.ybmmarket20.R
import com.ybmmarket20.bean.*
import com.ybmmarket20.common.JgTrackBean
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.utils.ImageUtil
import com.ybmmarket20.utils.StringUtil
import com.ybmmarket20.utils.analysis.BaseFlowData
import com.ybmmarket20.utils.analysis.XyyIoUtil
import com.ybmmarket20.view.homesteady.whenAllNotNull
import com.ybmmarketkotlin.adapter.YBMBaseMultiItemAdapter

/**
 * <AUTHOR>
 * @description
 */
class OftenBuyListAdapter(
        list: MutableList<AbstractMutiItemEntity>, var mJgTrackBean: JgTrackBean? = null,var mProductClickTrackListener:((RowsBean,Int,Int?)->Unit)? = null) :
        YBMBaseMultiItemAdapter<AbstractMutiItemEntity>(list) {

    val findSameGoodsAdapter by lazy {
        FindSameGoodsHelperAdapter(mContext).apply {
            jgTrackBean = mJgTrackBean
            productClickTrackListener = mProductClickTrackListener
        }
    }
    private val traceProductData = SparseArray<String>()

    var resourceViewTrackListener: ((productId: String, productName: String, productPrice: Double, productTag: String, position: Int) -> Unit)? = null

    // key: 商品Id     value:当时埋点的时间戳
    private val productViewTrackMap = hashMapOf<String, Long>()

    companion object{
        private const val TRACK_DURATION = 2 * 60 * 1000 //2分钟内不上报
    }
    init {
        addItemType(
                OFTEN_BUY_ITEM,
                R.layout.item_often_buy)
        addItemType(
                OFTEN_BUY_RECOMMEND_ITEM,
                R.layout.item_find_same_goods)
        addItemType(
                OFTEN_BUY_RECOMMEND_TITLE_ITEM,
                R.layout.item_often_buy_recommend_title)
        addItemType(
                OFTEN_BUY_ITEM_EMPTY,
                R.layout.item_often_buy_empty)
    }

    /**
     * 设置常购清单埋点数据
     */
    fun setOftenBuyFlowData(flowData: BaseFlowData?) {
        this.flowData = flowData
    }

    /**
     * 设置常购清单推荐埋点数据
     */
    fun setOftenBuyRecommendFlowData(flowData: BaseFlowData?) {
        findSameGoodsAdapter.flowData = flowData
    }

    override fun bindItemView(baseViewHolder: YBMBaseHolder?, t: AbstractMutiItemEntity?) {
        whenAllNotNull(baseViewHolder, t) { holder, bean ->
            when (bean.itemType) {
                OFTEN_BUY_ITEM -> {
                    if (bean is OftenBuyItem) {
                        bindOftenBuyItem(
                                holder,
                                bean)
                        productViewTrackMap[bean.skuId?:""]?.let{
                            if (it - System.currentTimeMillis() > TRACK_DURATION){
                                productViewTrackMap[bean.skuId?:""] = System.currentTimeMillis()
                                resourceViewTrackListener?.invoke(
                                        bean.skuId ?: "",
                                        bean.showName ?: "",
                                        bean.minPrice,
                                        "",
                                        holder.bindingAdapterPosition)
                            }
                        }?:run{
                            productViewTrackMap[bean.skuId?:""] = System.currentTimeMillis()
                            resourceViewTrackListener?.invoke(
                                    bean.skuId ?: "",
                                    bean.showName ?: "",
                                    bean.minPrice,
                                    "",
                                    holder.bindingAdapterPosition)
                        }

                    }
                }

                OFTEN_BUY_RECOMMEND_ITEM -> {
                    bean as OftenBuyRecommendRowsBean
                    var productTag = ""
                    bean.tags?.productTags?.let { tagList ->
                        tagList.forEachIndexed { index, tagBean ->
                            if (index != tagList.size - 1) {
                                productTag += tagBean.text + "，"
                            } else {
                                productTag += tagBean.text
                            }
                        }
                    }

                    productViewTrackMap[bean.productId?:""]?.let{
                        if (it - System.currentTimeMillis() > TRACK_DURATION){
                            productViewTrackMap[bean.productId] = System.currentTimeMillis()
                            resourceViewTrackListener?.invoke(
                                    bean.productId ?: "",
                                    bean.productName ?: "",
                                    bean.fob,
                                    productTag,
                                    holder.bindingAdapterPosition)
                        }
                    }?:run{
                        productViewTrackMap[bean.productId?:""] = System.currentTimeMillis()
                        resourceViewTrackListener?.invoke(
                                bean.productId ?: "",
                                bean.productName ?: "",
                                bean.fob,
                                productTag,
                                holder.bindingAdapterPosition)
                    }

                    bindOftenBuyRecommendItem(
                            holder,
                            bean)
                }
            }
        }
    }

    /**
     * 绑定常购清单Item
     */
    private fun bindOftenBuyItem(holder: YBMBaseHolder, bean: OftenBuyItem) {
        val ivGoods = holder.getView<ImageView>(R.id.ivGoods)
        ImageUtil.load(mContext, "${AppNetConfig.LORD_IMAGE}${bean.imageUrl}", ivGoods)
        val tvBuyCount = holder.getView<TextView>(R.id.tvBuyCount)
        tvBuyCount.text = "买过${bean.purchaseNum ?: 0}次"
        val tvOftenBuyTitle = holder.getView<TextView>(R.id.tvOftenBuyTitle)
        tvOftenBuyTitle.text = bean.showName
        val tvOftenBuyManufactor = holder.getView<TextView>(R.id.tvOftenBuyManufactor)
        tvOftenBuyManufactor.text = bean.manufacturer
        val tvPrice = holder.getView<TextView>(R.id.tvPrice)
        tvPrice.text = if (bean.maxPrice == bean.minPrice) {
            SpannableStringBuilder("￥").append(getPriceSpannableBuilder(bean.minPrice))
        } else {
            SpannableStringBuilder("￥")
                .append(getPriceSpannableBuilder(bean.minPrice))
                .append(" ~ ")
                .append(getPriceSpannableBuilder(bean.maxPrice))
        }

        if (flowData != null && traceProductData[holder.bindingAdapterPosition] == null) {
            XyyIoUtil.track("standardProductChart_exposure", hashMapOf("standardProduct_id" to bean.masterStandardProductId))
            traceProductData.put(holder.bindingAdapterPosition, bean.skuId)
        }
    }

    /**
     * 绑定常购清单推荐Item
     */
    private fun bindOftenBuyRecommendItem(holder: YBMBaseHolder, bean: OftenBuyRecommendRowsBean) {
        findSameGoodsAdapter.bindItemView(holder, bean)
    }

    override fun onViewAttachedToWindow(holder: BaseViewHolder) {
        super.onViewAttachedToWindow(holder)
        val lp: ViewGroup.LayoutParams = holder.itemView.layoutParams
        if (lp is StaggeredGridLayoutManager.LayoutParams) {
            val item = getItem(holder.layoutPosition)
            lp.isFullSpan = (item is AbstractMutiItemEntity && (item.itemType != OFTEN_BUY_RECOMMEND_ITEM)) || item !is AbstractMutiItemEntity
        }
    }

    private fun getPriceSpannableBuilder(price: Double): SpannableStringBuilder? {
        return StringUtil.getSpannableSizeWithDot("$price", 18, 14)
    }

}