package com.ybmmarket20.common;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;

import com.ybmmarket20.utils.externalLink.ExternalLinkContext;

/**
 * activity 路由分发功能
 */
public class YBMRouterActivity extends BaseActivity {

	@Override
	protected void onNewIntent(Intent intent) {
		super.onNewIntent(intent);
		if (intent != null) {
			Uri uri = intent.getData();
			if (setRouter(uri)) return;
			finish();
		} else {
			finish();
		}

	}

	@Override
	protected void onCreate(Bundle savedInstanceState) {
		super.onCreate(savedInstanceState);
		Uri uri = getIntent().getData();
		if (setRouter(uri)) return;
		finish();
	}

	@Override
	protected int getContentViewId() {
		return 0;
	}

	@Override
	protected void initData() {

	}

	private boolean setRouter(Uri uri) {
		if (uri != null) {
			new ExternalLinkContext(this).setUri(uri);
		}
		return false;
	}

}
