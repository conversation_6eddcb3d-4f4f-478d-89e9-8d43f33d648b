package com.ybmmarket20.common

import android.app.Dialog
import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.os.Bundle
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.style.AbsoluteSizeSpan
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import com.luck.picture.lib.tools.ScreenUtils
import com.ybmmarket20.R
import com.ybmmarket20.bean.ReceiveMoneyAccountInfo
import com.ybmmarket20.common.util.ToastUtils
import kotlinx.android.synthetic.main.dialog_receive_money_info.*

/**
 * 收款账户信息
 */
class ReceiveMoneyAccountInfoDialog(val mContext: Context, theme: Int) : Dialog(mContext, theme) {

    constructor(mContext: Context): this(mContext, R.style.AlertDialog)

    val rootView: View by lazy { View.inflate(mContext, R.layout.dialog_receive_money_info, null) }

    private var mContent = ""

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val display = mContext.resources.displayMetrics
        val width = (display.widthPixels * 0.9).toInt()
        setContentView(rootView, ViewGroup.LayoutParams(width, ViewGroup.LayoutParams.WRAP_CONTENT))
        findViewById<TextView>(R.id.tv_submit).setOnClickListener { dismiss() }
        findViewById<TextView>(R.id.tv_copy_info).setOnClickListener {
            val clipboardManager = mContext.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
            val clipData = ClipData.newPlainText(
                    "text",
                    mContent)
            clipboardManager.setPrimaryClip(clipData)
            ToastUtils.showShort("复制成功")
        }
    }

    fun setData(accountInfo: ReceiveMoneyAccountInfo?): Dialog {
        accountInfo?.let {
            rootView.findViewById<TextView>(R.id.tv_company_info).text = it.companyName
            rootView.findViewById<TextView>(R.id.tv_bank_info).text = it.othBankName
            rootView.findViewById<TextView>(R.id.tv_bank_account_info).text = it.accountNum
            val priceSpannable = SpannableStringBuilder("￥")
            priceSpannable.setSpan(AbsoluteSizeSpan(ScreenUtils.dip2px(mContext, 9f)), 0, 1, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
            priceSpannable.append(it.payAmount?: "")
            rootView.findViewById<TextView>(R.id.tv_money_info).text = priceSpannable

            mContent = """
                    公司名称：${it.companyName}
                    开户银行：${it.othBankName}
                    银行账户：${it.accountNum}
                    汇款金额：$priceSpannable
                    """.trimIndent()
        }
        return this
    }


}