package com.ybmmarket20.common

import android.annotation.SuppressLint
import android.app.Dialog
import android.text.Editable
import android.text.TextWatcher
import android.view.View
import android.view.ViewGroup
import com.ybm.app.bean.NetError
import com.ybmmarket20.R
import com.ybmmarket20.bean.AgentOrderRejectBean
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.common.util.ToastUtils
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.network.HttpManager
import com.ybmmarket20.utils.SpUtil
import com.ybmmarket20.utils.StringUtil
import kotlinx.android.synthetic.main.dialog_agent_order_reject.*

/**
 * 驳回代下单Dialog
 * <AUTHOR>
 * 2019-10-22
 */
class AgentOrderRejectDialog(
        val mContext: BaseActivity,
        val id: String?,
        val purchaseNo: String?
) : Dialog(mContext, R.style.AlertDialog) {

    var rejectReason: String = "没有采购需求"
    var rejectType: Int = 0
    var mListener: ((rejectReason: String) -> Unit)? = null

    init {
        val display = mContext.resources.displayMetrics
        val screenWidth = (display.widthPixels * 0.8).toInt()
        val view = View.inflate(mContext, R.layout.dialog_agent_order_reject, null)
        setContentView(view, ViewGroup.LayoutParams(screenWidth, ViewGroup.LayoutParams.WRAP_CONTENT))
        setCancelable(false)
        tv_agent_order_reject_cancel.setOnClickListener{dismiss()}
        tv_agent_order_reject_confirm.setOnClickListener{handleReject(id)}
        rg_agent_order_reject.setOnCheckedChangeListener{ _, checkedId ->
            rejectType = 0
            rejectReason = when(checkedId) {
                // 没有采购需求
                R.id.rb_no_purchase_demand -> {
                    cl_agent_order_reject_input.visibility = View.GONE
                    et_agent_order_reject_input.setText("")
                    "没有采购需求"
                }
                // 恶意代下单
                R.id.rb_spit_agent_order -> {
                    cl_agent_order_reject_input.visibility = View.GONE
                    et_agent_order_reject_input.setText("")
                    "恶意代下单"
                }
                // 商品品种和数量不对
                R.id.rb_goods_count_bad -> {
                    cl_agent_order_reject_input.visibility = View.GONE
                    et_agent_order_reject_input.setText("")
                    "商品品种和数量不对"
                }
                // 其他
                else -> {
                    cl_agent_order_reject_input.visibility = View.VISIBLE
                    rejectType = 1
                    "其它"
                }
            }
        }

        et_agent_order_reject_input.addTextChangedListener(object: TextWatcher{
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
            }

            @SuppressLint("SetTextI18n")
            override fun afterTextChanged(s: Editable?) {
                tv_agent_order_reject_count.text = "${s?.length ?: 0}/100"
            }

        })
    }

    constructor(mContext: BaseActivity, id: String): this(mContext, id, null)

    /**
     * 确定驳回
     */
    private fun handleReject(id: String?) {
        if(rejectType == 1) {
            val rejectContent: String = et_agent_order_reject_input.text.toString()
            if(rejectContent.isEmpty()) {
                ToastUtils.showLong("驳回原因不能为空")
                return
            }
            if(StringUtil.containsEmoji(rejectContent)) {
                ToastUtils.showLong("驳回原因录入数据不合法，请检查")
                return
            }
        }
        if(id==null || purchaseNo==null) return
        mContext.showProgress()
        val params = RequestParams().apply {
            put("id", id)
            put("merchantId", SpUtil.getMerchantid())
            put("refuseReason", rejectReason)
            put("refuseExplan", et_agent_order_reject_input.text.toString())
            purchaseNo?.let {put("purchaseNo", it)}
        }
        HttpManager.getInstance().post(AppNetConfig.AGENT_ORDER_REJECT_ORDER, params, object: BaseResponse<AgentOrderRejectBean>(){
            override fun onSuccess(content: String?, obj: BaseBean<AgentOrderRejectBean>?, t: AgentOrderRejectBean?) {
                super.onSuccess(content, obj, t)
                mContext.dismissProgress()
                if(obj!=null && obj.isSuccess) {
                    if(t?.errorCode == "failure") {
                        ToastUtils.showLong(t.errorMessage)
                        mListener?.invoke(t.errorMessage ?:"")
                    } else {
                        mListener?.invoke(if(rejectReason=="其它") et_agent_order_reject_input.text.toString() else rejectReason)
                    }
                    dismiss()
                }
            }

            override fun onFailure(error: NetError?) {
                super.onFailure(error)
                mContext.dismissProgress()
            }
        })
    }

    fun setOnRejectListener(listener: (rejectReason: String) -> Unit) {
        mListener = listener
    }
}