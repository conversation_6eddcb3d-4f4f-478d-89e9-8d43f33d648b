package com.ybmmarket20.common;

import android.app.Dialog;
import android.content.Context;
import androidx.appcompat.app.AlertDialog;
import android.text.TextUtils;
import android.text.method.ScrollingMovementMethod;
import android.view.View;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.ybm.app.utils.UiUtils;
import com.ybmmarket20.R;

/**
 * <AUTHOR> Brin
 * @date : 2019/6/5 - 17:56
 * @Description :
 */
public class UpdateDialog {

    private Dialog dialog;
    private final View view;
    private OnClickListener mClickListener;
    private final TextView tvTittle;
    private final TextView tvVersion;
    private final TextView tvSize;
    private final TextView tvContent;
    private final TextView btnPositive;
    private final TextView btnNagetivie;
    private boolean isForceUpdate;

    public UpdateDialog(Context context, boolean isForce, OnClickListener clickListener) {
        dialog = new Dialog(context, R.style.AlertDialog);
        view = View.inflate(context, R.layout.dialog_force_update, null);
        mClickListener = clickListener;
        tvTittle = (TextView) view.findViewById(R.id.tv_title);
        tvVersion = (TextView) view.findViewById(R.id.tv_version);
        tvSize = (TextView) view.findViewById(R.id.tv_size);
        tvContent = (TextView) view.findViewById(R.id.tv_content);
        tvContent.setMovementMethod(ScrollingMovementMethod.getInstance());
        btnPositive = (TextView) view.findViewById(R.id.btn_positive);
        btnNagetivie = (TextView) view.findViewById(R.id.btn_negative);
        dialog.setContentView(view);
        dialog.setCancelable(false);
        btnPositive.setOnClickListener(v -> {
            mClickListener.onPositiveClicked(UpdateDialog.this);
        });
        btnNagetivie.setOnClickListener(v -> {
            mClickListener.onNegativeClicked(UpdateDialog.this);
        });
    }

    public void setTitle(String versionTitle) {
        tvTittle.setText(versionTitle);
    }

    public void setContent(String versionInfo) {
        tvContent.setText(versionInfo);
    }

    public void setApkSize(String apkSize) {
        if (!TextUtils.isEmpty(apkSize)) {
            tvSize.setVisibility(View.VISIBLE);
            tvSize.setText("版本大小： " + apkSize + "M");
        } else {
            tvSize.setVisibility(View.INVISIBLE);
        }
    }

    public void setVersion(String versionName) {
        tvVersion.setText(versionName);
    }

    public void show() {
        if (dialog.getContext() != null && dialog != null && !dialog.isShowing()) {
            dialog.show();
        }
    }

    public void setForceUpdate(boolean isForceUpdate) {
        this.isForceUpdate = isForceUpdate;

        if (isForceUpdate) {
            btnNagetivie.setVisibility(View.GONE);
            btnPositive.setLayoutParams(new LinearLayout.LayoutParams(UiUtils.dp2px(150), UiUtils.dp2px(40)));
        } else {
            btnNagetivie.setVisibility(View.VISIBLE);
            btnPositive.setLayoutParams(new LinearLayout.LayoutParams(UiUtils.dp2px(100), UiUtils.dp2px(40)));
        }
    }

    public void dismiss() {
        if (dialog.getContext() != null &&dialog != null && dialog.isShowing()) {
            dialog.dismiss();
        }
    }

    interface OnClickListener {
        // 确认
        void onPositiveClicked(UpdateDialog alertDialog);

        // 取消
        void onNegativeClicked(UpdateDialog alertDialog);
    }

}
