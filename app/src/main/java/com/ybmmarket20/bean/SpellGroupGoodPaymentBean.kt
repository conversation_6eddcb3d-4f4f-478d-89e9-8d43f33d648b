package com.ybmmarket20.bean

import com.google.gson.annotations.SerializedName
import com.ybmmarket20.xyyreport.page.payment.IPaymentSuiXinPinGoods
import java.util.ArrayList

/**
 * 结算页随心拼
 */
//data class SpellGroupGoodsPaymentBean(
//    var goodsList: MutableList<SpellGroupGoodsItem>?, //商品列表
//    var goodsCategoryCount: Int, //商品种类数量
//    var goodsCount: Int, //商品数量
//    var totalPrice: String? //小计
//)

/**
 * 随心拼商品
 */
data class SpellGroupGoodsItem(
    var goodsUrl: String?, //商品图
    var goodsTagUrl: String?, //商品标签
    var goodsTitle: String?, //商品标题
    var goodsPrice: String?, //商品单价
    var goodsUnit: String?, //商品单位
    @SerializedName("fob")
    var goodsOriginalPrice: String?, //商品原价
    var goodsSelectedCount: Int, //商品选中数量
    var skuId: String?,
    var mediumPackageNum: Int,//中包装数量 默认为1
    var isSplit: Int, //是否可拆零 0:不可拆零；1:可拆零 默认1
    var goodsCount: Int = 0,
    var totalPrice: String? = "0", //总价
    var spid: String? = "",
    var sptype: String? = "",
    var sid: String? = "",
    var source: String? = "",
    var nearEffect: String? = "",
    var tagList: ArrayList<TagBean>? = null,
    var actPt: SkuStartBean? = null,
    var actPgby: SkuStartBean? = null,
): IPaymentSuiXinPinGoods {
    constructor(): this(null, null, null, null, null, null, 0, null, 1, 1, 0,"0", "", "", "", nearEffect = null)
    var qtData: PaymentSuiXinPinQtData? = null
    override fun getScmId(): String? = qtData?.scmId

    override fun getExpId(): String? = qtData?.expId

    override fun getRank(): String? = qtData?.rank

    override fun getSuiXinPinQtListData(): String? = qtData?.qtListData

    override fun getQtSkuData(): String? = qtData?.qtSkuData

    override fun getProductSkuSkuId(): String? = skuId

    override fun getLongSkuId(): Long {
        return try {
            if (skuId.isNullOrEmpty()) 0L
            else {
                skuId!!.toLong()
            }
        } catch (e: Exception) {
            e.printStackTrace()
            0L
        }
    }

    override fun getSkuName(): String? = goodsTitle
    override fun getSuiXinPinScmId(): String? {
        return qtData?.scmId
    }
}


data class SkuStartBean(
    var skuStartNum: Int
)