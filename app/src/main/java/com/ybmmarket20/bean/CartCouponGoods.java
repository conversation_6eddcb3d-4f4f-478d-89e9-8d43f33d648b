package com.ybmmarket20.bean;

/**
 * <AUTHOR>
 * 购物车可使用优惠券的商品
 */
public class CartCouponGoods {

    /**
     * skuId : 471
     * amount : 8
     * price : 12.5
     * imageUrl : 6938200750774.jpg
     * status : 1
     * spec : 10g*20袋
     * name : 白云山 板蓝根颗粒
     */

    private String skuId; //商品ID
    private int amount;   //加购数量
    private double price;   //商品价格
    private String imageUrl;    //图片URL
    private int status;     //选中状态：1:选中;0未选中
    private String spec;    //规格
    private String name;    //名称
    public String packageId; //商品ID

    public String getSkuId() {
        return skuId;
    }

    public void setSkuId(String skuId) {
        this.skuId = skuId;
    }

    public int getAmount() {
        return amount;
    }

    public void setAmount(int amount) {
        this.amount = amount;
    }

    public double getPrice() {
        return price;
    }

    public void setPrice(double price) {
        this.price = price;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getSpec() {
        return spec;
    }

    public void setSpec(String spec) {
        this.spec = spec;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
