package com.ybmmarket20.bean

data class PayForAnotherBean(
        val title: String = "",
        val desc: String = "",
        val imageUrl: String = "",
        val shareUrl: String = "",
        val wechatShowState: Int = 1, //是否支持微信分享：0 不支持;1支持
        val aliPayShowState: Int = 0, //是否支持支付宝分享：0 不支持;1支持
        val showState: Int, //1显示:0不显示
        val channelType: Int, //1微信分享2支付分享3复制链接
        val path :String = "",// 小程序路径
        val appId :String = "",//小程序id
        val isMiniProg :Int = 0,//是否使用小程序, 1使用，0不使用
        var shopName: String?, //店铺名称
        var createTimeDesc: String?,//下单时间
        var payAmountDesc: String?//支付金额
)