package com.ybmmarket20.bean

open class PriceType(
    var isOEM: Boolean,             //true是OEM协议商品
    var isControlAgreement: Int,    // 是否控销协议商品（0不是控销协议商品，1是控销协议商品）
    var signStatus: Int,            //协议签署状态(0-未签署,1-已签署)是普通商品或者(OEM协议商品且已签署协议)才会显示价格
    var showAgree: Int,             // 是否签署控销协议 (0没有签署，1签署)
    var isControl: Int,             //是否控销
    var isPurchase: Boolean         //是否可以购买 true:可购买 ；false:不可买
) : IPriceType {
    constructor() : this(false, 0, 0, 0, 0, false)

    override fun isOEMGoods(): Boolean = isOEM

    override fun isOEMSign(): Boolean = signStatus == 1

    override fun isControlAgreement(): Boolean = isControlAgreement == 1

    override fun isControlGoods(): Boolean = isControl == 1

    override fun isControlSign(): Boolean = showAgree == 1

    override fun isPurchaseGoods(): Boolean = isPurchase

    override fun isShowPrice(): Boolean = ((isOEMGoods() && !isOEMSign() && !isControlAgreement()) ||
            (!isOEMGoods() && isControlAgreement() && !isControlSign()) ||
            (isOEMGoods() && !isOEMSign() && isControlAgreement() && !isControlSign()))
        .not()

}

class WrapperPriceType(val priceType: IPriceType) : IPriceType by priceType

interface IPriceType {
    fun isOEMGoods(): Boolean
    fun isOEMSign(): Boolean
    fun isControlAgreement(): Boolean
    fun isControlGoods(): Boolean
    fun isPurchaseGoods(): Boolean
    fun isControlSign(): Boolean
    fun isShowPrice(): Boolean
}