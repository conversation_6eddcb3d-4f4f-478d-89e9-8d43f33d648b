package com.ybmmarket20.bean;

import java.util.List;

/**
 * 首页动态布局
 */
public class ModuleView<T> {

    public String bgRes = "" ;//背景色 #777777 或者 图片url,默认白色
    public String title  = "" ;// 标题
    public String titleRes = "" ;// 背景色 #777777 或者 图片url,默认白色
    public String titleColor  = "" ;//  标题色 #777777
    public int titleSize;//  标题大小 16sp 单位 sp                    | int型
    public int titleStyle;//  标题字体样式 0 普通 1加粗 2斜体                    | int型
    public int titleGravity;//  左中右                    | int型 1 2 3
    public List<Integer> padding;// [left,top,rigth,bottom] 内边距 单位dp       | int型数组
    public List<Integer> margin;// [left,top,rigth,bottom]  外边距 单位dp       | int型数组
    public List<Integer> titleMargin;// [left,top,rigth,bottom] 外边距 单位dp       | int型数组
    public List<Integer> titlePadding;// [left,top,rigth,bottom] 内边距 单位dp       | int型数组
    public String action  = "" ;//  块点击事件
    public String api ="" ;// 数据请求的地址 http://114.55.2.1/xyy-app/app/findPatchVersion？key1=2&key2=34
    public int height;// 块的高度                                    | int型
    public int titleHeight;// 标题的高度                                    | int型
    public int style;//  样式 上下排列 圆角
    public int moduleId ;// 模块id                                  | int型
    public List<T> items;//  模块条目数据

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        ModuleView<?> that = (ModuleView<?>) o;

        if (style != that.style) return false;
        if (moduleId != that.moduleId) return false;
        if(api == null){
            api = "";
        }
        if(that.api == null){
            that.api = "";
        }
        return api != null ? api.equals(that.api) : that.api == null;

    }

    @Override
    public int hashCode() {
        int result = api != null ? api.hashCode() : 0;
        result = 31 * result + style;
        result = 31 * result + moduleId;
        return result;
    }

    public boolean isEquals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        ModuleView<?> that = (ModuleView<?>) o;
        if(action == null){
            action = "";
        }
        if(that.action == null){
            that.action = "";
        }
        if(title == null){
            title = "";
        }
        if(that.title == null){
            that.title = "";
        }
        if(bgRes == null){
            bgRes = "";
        }
        if(that.bgRes == null){
            that.bgRes = "";
        }
        if(titleRes == null){
            titleRes = "";
        }
        if(that.titleRes == null){
            that.titleRes = "";
        }
        if(titleColor == null){
            titleColor = "";
        }
        if(that.titleColor == null){
            that.titleColor = "";
        }
        if (titleHeight != that.titleHeight) return false;
        if (titleSize != that.titleSize) return false;
        if (titleGravity != that.titleGravity) return false;
        if (titleStyle != that.titleStyle) return false;
        if (height != that.height) return false;
        if (style != that.style) return false;
        if (moduleId != that.moduleId) return false;
        if (bgRes != null ? !bgRes.equals(that.bgRes) : that.bgRes != null) return false;
        if (title != null ? !title.equals(that.title) : that.title != null) return false;
        if (titleRes != null ? !titleRes.equals(that.titleRes) : that.titleRes != null)
            return false;
        if (titleColor != null ? !titleColor.equals(that.titleColor) : that.titleColor != null)
            return false;
        if (padding != null ? !padding.equals(that.padding) : that.padding != null) return false;
        if (margin != null ? !margin.equals(that.margin) : that.margin != null) return false;
        if (titleMargin != null ? !titleMargin.equals(that.titleMargin) : that.titleMargin != null)
            return false;
        if (action != null ? !action.equals(that.action) : that.action != null) return false;
        return api != null ? api.equals(that.api) : that.api == null;

    }


    public boolean isProduct(){
        return moduleId>=2000;
    }
}
