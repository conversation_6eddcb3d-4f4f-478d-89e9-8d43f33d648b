package com.ybmmarket20.bean.cart;


import java.util.List;

/*
 *公司-商城-商品活动组-普通商品or套餐商品list
 */
public class CartSortedNewBean {

    private CartItemBean item;
    private int itemType; //item类型   3=套餐，其他不用判断
    private List<CartItemBean> subItemList;//套餐集合子item

    public CartItemBean getItem() {
        return item;
    }

    public void setItem(CartItemBean item) {
        this.item = item;
    }

    public int getItemType() {
        return itemType;
    }

    public void setItemType(int itemType) {
        this.itemType = itemType;
    }

    public List<CartItemBean> getSubItemList() {
        return subItemList;
    }

    public void setSubItemList(List<CartItemBean> subItemList) {
        this.subItemList = subItemList;
    }
}
