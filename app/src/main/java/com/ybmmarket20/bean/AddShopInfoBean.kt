package com.ybmmarket20.bean

data class AddShopInfoBean(
    val baseSuccessFlag: Int, //企业基本信息查询成功标志0,2-未成功，，1成功1成功(暂不使用)
    val baseRegNumber: String?,//注册号
    val baseCreditCode: String?,//统一社会信用代码
    val baseTaxNumber: String?,//纳税人识别号
    val baseName: String?,//企业名
    val baseRegStatus: Int, //工商存续状态 1-存续  2-非存续
    val positionSuccessFlag: Int,//经纬度查询成功标志0,2-未成功，1成功(暂不使用)
    val positionGraphId: String?,//公司id
    val positionName: String?,//企业名称
    val positionRegLocation: String?,//详细地址(注册地址)
    val positionProvince: String?,//省
    val positionProvinceCode: String?,
    val positionLongitude: String?,//经度
    val positionLatitude: String?,//纬度
    val positionDistrict: String?,//区县
    val positionDistrictCode: String?,
    val positionCity: String?,//市
    val positionCityCode: String?,
    //请求返回判断成功和失败0,2-未成功，1成功
    val successFlag: Int
) {
    fun getRegisterAddress(): String {
        return "$positionProvince$positionCity$positionDistrict"
    }
}