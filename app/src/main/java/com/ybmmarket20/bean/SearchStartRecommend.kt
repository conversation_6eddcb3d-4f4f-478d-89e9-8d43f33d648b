package com.ybmmarket20.bean

import com.ybmmarket20.adapter.SearchStartRecommendGoodsAdapter

/**
 * 搜索启动页
 */
data class SearchStartRecommend(
    var licenseStatus: Int,
    var sptype: String?,
    var spid: String?,
    var sid: String?,
    var rows: MutableList<SearchStartRecommendRows>?
)

/**
 * 搜索启动页品类
 */
data class SearchStartRecommendRows(
    //榜单类型:1-拼团榜单，2-品类榜单
    var type: Int,
    //榜单名称
    var name: String?,
    //跳转链接
    var link: String?,
    //跳转链接文本
    var linkText: String?,
    //榜单商品列表
    var skuList: MutableList<RowsBean>?,
    //缓存adapter
    @Transient var goodsListAdapter: SearchStartRecommendGoodsAdapter?
)