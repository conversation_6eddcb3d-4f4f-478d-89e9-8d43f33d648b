package com.ybmmarket20.bean

import android.os.Parcel
import android.os.Parcelable
import com.ybmmarket20.bean.loadmore.IPage

class AgentOrderListBean<T>(
        val currentPage: Int,
        val limit: Int,
        val offset: Int,
        val pageCount: Int,
        val rows: List<T>,
        val total: Int
) : IPage<T> {

    override fun getCurPage(): Int = currentPage

    override fun getPageRowSize(): Int = limit

    override fun getTotalPages(): Int = pageCount

    override fun getRowsList(): List<T> = rows
}

class AgentOrderListRowBean(
        val confirmExpireHour: Int,
        val createTime: Long,
        var id: String?,
        val imageUrl: String?,
        val money: String?,
        val orderNo: String?,
        val orderSource: Int,
        val productNum: String?,
        val saleMobile: String?,
        val saleName: String?,
        val salesId: Int,
        var status: Int,
        val varietyNum: Int,
        var currentDate: Long = -1,
        val payEndTime: Long = -1,
        val purchaseNo: String?
) : Parcelable {
    constructor(): this(0, 0, null, null, null, null, 0, null, null, null, 0, 0, 0, 0, 0, null)

    constructor(source: Parcel) : this(
            source.readInt(),
            source.readLong(),
            source.readString(),
            source.readString(),
            source.readString(),
            source.readString(),
            source.readInt(),
            source.readString(),
            source.readString(),
            source.readString(),
            source.readInt(),
            source.readInt(),
            source.readInt(),
            source.readLong(),
            source.readLong(),
            source.readString()
    )

    override fun describeContents() = 0

    override fun writeToParcel(dest: Parcel, flags: Int) = with(dest) {
        writeInt(confirmExpireHour)
        writeLong(createTime)
        writeString(id)
        writeString(imageUrl)
        writeString(money)
        writeString(orderNo)
        writeInt(orderSource)
        writeString(productNum)
        writeString(saleMobile)
        writeString(saleName)
        writeInt(salesId)
        writeInt(status)
        writeInt(varietyNum)
        writeLong(currentDate)
        writeLong(payEndTime)
        writeString(purchaseNo)
    }

    companion object {
        @JvmField
        val CREATOR: Parcelable.Creator<AgentOrderListRowBean> = object : Parcelable.Creator<AgentOrderListRowBean> {
            override fun createFromParcel(source: Parcel): AgentOrderListRowBean = AgentOrderListRowBean(source)
            override fun newArray(size: Int): Array<AgentOrderListRowBean?> = arrayOfNulls(size)
        }
    }
}