package com.ybmmarket20.bean

/**
 * 登陆信息
 */
data class LoginInfo(
    var token: String?,
    //店铺数
    var shopCount: Int,
    //账号ID
    var accountId: String?,
    //店铺ID
    var merchantId: String?,
    //是否审核中
    var isAudit: Boolean,
    //手机号
    var mobileNo: String?,
    //密码：前端自行存储、微信授权下发 md5+rsa
    var password: String?,
    //登陆成功后的跳转路由
    var loginSucceedUrl: String?,
    //是否是爬虫
    var isCrawler: Boolean,
    //  微信登录时绑定状态： 0 未绑定 1 登录成功
    var state:Int,
    // 微信授权登录下发：手机号 明文
    var account:String,
    // 绑定、验证码状态：0 未注册、未绑定  1 已注册、已绑定
    var status:Int,
){
    companion object{
        const val LOGIN_TYPE_WECHAT = "3"
        const val LOGIN_TYPE_DEFAULT = "0"
    }
}
