package com.ybmmarket20.bean;

import com.ybmmarket20.bean.loadmore.IPage;

import java.util.List;

public class AuthorizationAreaBean<T> implements IPage<T> {
	private int currentPage;
	private int limit;
	private int offset;
	private int pageCount;
	private int total;
	private List<T> rows;

	public int getCurrentPage() {
		return currentPage;
	}

	public void setCurrentPage(int currentPage) {
		this.currentPage = currentPage;
	}

	public int getLimit() {
		return limit;
	}

	public void setLimit(int limit) {
		this.limit = limit;
	}

	public int getOffset() {
		return offset;
	}

	public void setOffset(int offset) {
		this.offset = offset;
	}

	public int getPageCount() {
		return pageCount;
	}

	public void setPageCount(int pageCount) {
		this.pageCount = pageCount;
	}

	public int getTotal() {
		return total;
	}

	public void setTotal(int total) {
		this.total = total;
	}

	public List<T> getRows() {
		return rows;
	}

	public void setRows(List<T> rows) {
		this.rows = rows;
	}

	@Override
	public int getCurPage() {
		return currentPage;
	}

	@Override
	public int getPageRowSize() {
		return limit;
	}

	@Override
	public int getTotalPages() {
		return pageCount;
	}

	@Override
	public List<T> getRowsList() {
		return rows;
	}
}
