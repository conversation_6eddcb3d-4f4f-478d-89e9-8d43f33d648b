package com.ybmmarket20.bean;

import android.os.Parcel;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.style.AbsoluteSizeSpan;

import com.ybm.app.bean.AbstractMutiItemEntity;
import com.ybmmarket20.utils.MathUtils;
import com.ybmmarket20.utils.StringUtil;
import com.ybmmarket20.utils.UiUtils;

import java.io.Serializable;
import java.util.List;

/**
 * 退款药品列表
 */
public class RefundProductListBean extends AbstractMutiItemEntity implements Serializable {
    //普通套餐头
    public static final int ITEMTYPE_PACKAGE_TITLE = 1;
    //普通套餐商品
    public static final int ITEMTYPE_PACKAGE_CONTENT = 2;
    public static final int ITEMTYPE_PACKAGE_SUBTITLE = 3;
    public static final int ITEMTYPE_CONTENT = 4;
    //普通商品
    public static final int ITEMTYPE_REFUND_CONTENT = 5;
    public static final int ITEMTYPE_GIFT_CONTENT = 6;
    //近效期和临期套餐头
    public static final int ITEMTYPE_PACKAGE_TITLE_NEAR_EFFECTIVE = 7;
    //近效期和临期套餐商品
    public static final int ITEMTYPE_PACKAGE_CONTENT_NEAR_EFFECTIVE = 8;
    //临期商品
    public static final int ITEMTYPE_REFUND_CONTENT_NEAR_EFFECTIVE = 9;
    //临期分组头
    public static final int ITEMTYPE_GROUP_TITLE_NEAR_EFFECTIVE = 10;
    //赠品
    public static final int ITEMTYPE_REFUND_CONTENT_GIFT = 11;
    //选择赠品
    public static final int ITEMTYPE_REFUND_SELECT_GIFT = 12;

    //赠品已选中
    public static final int REFUND_GIFT_SELECTED = 1;
    // 赠品未选中
    public static final int REFUND_GIFT_UNSELECTED = 0;


    public String imageUrl;
    public String manufacturer;
    public String productId;
    public String productName;
    public String productAmount;
    public String skuId;
    //小计
    public String subTotal;
    public String orderNo;
    public String productQuantity;
    public String blackProductText;
    public String spec;
    public String fob;//原价
    public double rkPrice;//入库价格
    public double productPrice;//单价
    public double balanceAmount;//预返余额明细（返利）
    public double realPayAmount;//单品实付金额
    public double useBalanceAmount;//抵扣余额明细
    public double discountAmount;//单品优惠金额, 单品优惠总金额 = 单品优惠金额 + 抵扣余额明细
    public String costPrice;//成本价
    public String subtotal = "0.00";//小计
    public boolean isCheck = false;
    public boolean showDetail = false;
    public boolean checkGift = false;
    public int subSize = 0;
    public List<LabelIconBean> tagList;
    public List<LabelIconBean> tagWholeOrderList; // 整单包邮标签
    public String tagTitle; // 商品包邮tag
    public int isSplit;//是否可拆开、零

    public boolean isShow806;//是否是药采节
    public boolean gift;//是否是药采节
    public String priceDes;

    public int mediumPackageNum;//中包装数量
    public int numberAmount;//记录选择的数量
    public ActivityTagBean productActivityTag;//活动标签
    public int type;//(1, "满减"),(2, "满折"),(3, "满赠"),(4, "满减赠"),9-不参与活动，10-套餐，5:大礼包 **/
    public int id;//大礼包id
    public int selectStatus;//默认选中的大礼包
    public String validity; //有效期
    public String giftPromotionId; //赠品关联的主品订单活动ID
    public int giveSkuType; //赠送类型(满赠才有) 1.满赠活动赠送  2.满赠活动赠品池任选
    public boolean isGiftSelected; //是否选中 giveSkuType 2.满赠活动赠品池任选 有效
    public int curSelectedCount; //当前选中数量
    public String nearEffect; //效期

    /*------------------渠道-------------------*/
    private String channelCode;//渠道编码：默认为 1; 药帮忙 1; 2 宜块钱
    public int nearEffectiveCount;
    public int nearEffectiveFlag; //1 临期 2近效期
    public String subtotalPrice; //原价
    public String totalPrice;
    public int extraGift;//1 赠品 0 不是赠品
    public String packageId; //套餐
    public int extraGiftVariety;//赠品种类数
    public String extraGiftId;
    public int isRandom;
    public List<TagBean> afterSaleTags;
    public int selectGiftAmount; //可选赠品数量
    public String orderDetailId; //明细Id

    // 公司类型，1：大自营，2：POP
    public int companyType;
    public boolean isFbpShop;//是否是fbp店铺

    public boolean isThirdShop() {
        return companyType == 1;
    }

    public RefundProductListBean() {
        itemType = ITEMTYPE_CONTENT;
    }

    protected RefundProductListBean(Parcel in) {
        this.imageUrl = in.readString();
        this.manufacturer = in.readString();
        this.productId = in.readString();
        this.productName = in.readString();
        this.productAmount = in.readString();
        this.spec = in.readString();
        this.productPrice = in.readDouble();
        this.subtotal = in.readString();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        RefundProductListBean bean = (RefundProductListBean) o;

        return productId != null ? productId.equals(bean.productId) : bean.productId == null;

    }

    @Override
    public int hashCode() {
        return productId != null ? productId.hashCode() : 0;
    }


    @Override
    public int getItemType() {
        if (itemType <= 0) {
            itemType = ITEMTYPE_CONTENT;
        }
        return super.getItemType();
    }

    public int getProductAmount() {
        try {
            return Integer.parseInt(productAmount);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return 1;
    }

    public String getDiscountAmount() {
        if (discountAmount <= 0.0001) {
            return StringUtil.DecimalFormat2Double(discountAmount) + "";
        } else {
            return StringUtil.DecimalFormat2Double(MathUtils.div(getProductAmount(), discountAmount));
//            return UiUtils.getOrderPriceNumber(discountAmount / getProductAmount() + "");
        }
    }

    public String getBalanceAmount() {
        if (useBalanceAmount <= 0.0001) {
            return StringUtil.DecimalFormat2Double(useBalanceAmount) + "";
        } else {
            return StringUtil.DecimalFormat2Double(MathUtils.div(getProductAmount(), useBalanceAmount));
        }
    }

    public String getBalance2Amount() {
        if (balanceAmount <= 0.0001) {
            return StringUtil.DecimalFormat2Double(balanceAmount) + "";
        } else {
            return StringUtil.DecimalFormat2Double(MathUtils.div(getProductAmount(), balanceAmount));
        }
    }

    public String getRkPrice() {
        String srcStr = StringUtil.stripTrailingZeros(rkPrice+"");
        String[] split = srcStr.split("\\.");
        if (split.length == 1 || split[1].length()<=2) {
            return StringUtil.DecimalFormat2Double(rkPrice);
        } else {
            return StringUtil.DecimalFormat4Double(rkPrice);
        }
    }

    /**
     * 获取格式化的成本价
     * @return
     */
    public String getFormatCostPrice() {
        try {
            double v = Double.parseDouble(costPrice);
            String srcStr = StringUtil.stripTrailingZeros(v+"");
            String[] split = srcStr.split("\\.");
            if (split.length == 1 || split[1].length()<=2) {
                return StringUtil.DecimalFormat2Double(v);
            } else {
                return StringUtil.DecimalFormat4Double(v);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "0.00";
    }

    /*
     *单价-优惠-返利
     * */
    public String getRkPriceTwo() {//客户端计算的会有误差，统一改用服务器返回的成本价字段 costPrice
        return StringUtil.DecimalFormat2Double(productPrice - MathUtils.div(getProductAmount(), discountAmount) - MathUtils.div(getProductAmount(), balanceAmount, 2));
    }

    public boolean isType() {
        return type == 5;
    }

    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }


    /**
     * 是否是满选
     * @return
     */
    public boolean isGiveSkuTypeFullSelected() {
        return giveSkuType == 2;
    }

    /**
     * 是否是赠品
     * @return
     */
    public boolean isGift() {
        return extraGift == 1;
    }

    public SpannableStringBuilder getTotalPriceStringBuilder(){
        SpannableStringBuilder result = new SpannableStringBuilder();
        SpannableStringBuilder prefixStr = new SpannableStringBuilder();
        prefixStr.append("小计:");
        prefixStr.setSpan(new AbsoluteSizeSpan(14, true), 0, 3, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        prefixStr.append(" ¥");
        prefixStr.setSpan(new AbsoluteSizeSpan(10, true), 4, 5, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);

        SpannableStringBuilder showPriceStr = new SpannableStringBuilder();
        String subTotalStr = UiUtils.transform(subtotal);
        showPriceStr.append(subTotalStr);
        showPriceStr.setSpan(new AbsoluteSizeSpan(13, true), 0, subTotalStr.length() - 2, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        showPriceStr.setSpan(new AbsoluteSizeSpan(10, true), subTotalStr.length() - 2, subTotalStr.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        result.append(prefixStr);
        result.append(showPriceStr);
        return result;
    }

}
