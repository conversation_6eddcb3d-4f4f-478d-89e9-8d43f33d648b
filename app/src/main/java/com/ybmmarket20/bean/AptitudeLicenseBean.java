package com.ybmmarket20.bean;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * 资质
 */
public class AptitudeLicenseBean implements Parcelable {

    public int id;
    public int categoryId;
    public String merchantId;
    public String name;
    public String url;
    public int status;
    public long createTime;

    public void setUrl(String url) {
        this.url = url;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public void setId(int id) {
        this.id = id;
    }

    public void setCategoryId(int categoryId) {
        this.categoryId = categoryId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public void setName(String name) {
        this.name = name;
    }

    protected AptitudeLicenseBean(Parcel in) {
        id = in.readInt();
        categoryId = in.readInt();
        merchantId = in.readString();
        name = in.readString();
        url = in.readString();
        status = in.readInt();
        createTime = in.readLong();
    }

    public static final Creator<AptitudeLicenseBean> CREATOR = new Creator<AptitudeLicenseBean>() {
        @Override
        public AptitudeLicenseBean createFromParcel(Parcel in) {
            return new AptitudeLicenseBean(in);
        }

        @Override
        public AptitudeLicenseBean[] newArray(int size) {
            return new AptitudeLicenseBean[size];
        }
    };

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(id);
        dest.writeInt(categoryId);
        dest.writeString(merchantId);
        dest.writeString(name);
        dest.writeString(url);
        dest.writeInt(status);
        dest.writeLong(createTime);
    }
}
