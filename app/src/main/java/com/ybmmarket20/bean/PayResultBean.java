package com.ybmmarket20.bean;

import java.util.Objects;

/**
 * 支付结果接口
 */
public class PayResultBean {
    public PayResult payCode;
    public PayTran transferInfo;
    public String bigWheelText;//文案
    public String bigWheelUrl;// url
    public String payTypeName;//支付方式
    public String stateName;//状态
    public String title;//标题
    public String payDiscountTips;//支付宝支付本次订单已优惠xx
    public String tips;//我们将尽快为您配送，您可进入我的订单查看订单配送信息。

    public int isFinish; //1:继续轮询 2:停止轮询成功，3:停止轮询弹窗

    public String orderNo;
    public String companyName;
    public String orgId;

    public String shopTransferType; // 1-电汇商业;2-电汇平台

    /**
     * 是否是电汇商业
     * @return
     */
    public Boolean isTelegraphicTransferBusiness(){
        return Objects.equals(shopTransferType, "1");
    }
}
