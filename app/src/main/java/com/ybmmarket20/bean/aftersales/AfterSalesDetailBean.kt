package com.ybmmarket20.bean.aftersales

import com.google.gson.annotations.SerializedName

/**
 * 售后详情
 */
//1-发票售后
const val AFTER_SALES_TYPE_INVOICE = 1
//2-资质售后
const val AFTER_SALES_TYPE_LICENSE = 2

class AfterSalesDetailBean {

    //退回发票信息
    var refundInvoiceInfo: RefundInvoiceInfo? = null

    //商业收货信息
    val sellerAddressInfo: AfterSalesCompanyReceiveProductInfo? = null

    //商品资质
    val productCredentialList: MutableList<AfterSalesGoodsLicense>? = null

    //企业相关资质
    val corpCredential: String? = null

    //协商历史
    @SerializedName("auditRecords")
    val afterSalesConsultHistoryList: MutableList<AfterSalesConsultHistory>? = null

    //专票信息
    val specialInvoiceInfo: AfterSalesSpecialInvoiceInfo? = null

    //状态
    var afterSalesDetailStatusBean: AfterSalesDetailStatusBean? = null

    //错误信息
    val incorrectInvoiceInfo: String? = null

    //客服
    val merchantPhone: String? = null

    //6-待客户退回发票
    val auditProcessState: Int = 0

    //当前状态文案
    val auditProcessStateName: String? = null

    //创建时间
    val createTime: Long = 0L

    //售后类型：1-发票售后;2-资质售后
    val afterSalesType: Int = 0

    //售后类型
    val afterSalesTypeName: String? = null

    //售后单号
    val afterSalesNo: String? = null

    //再次发起售后的时候需要用到
    val orderNo: String? = null

    val orderId: String? = null

    val orgId: String? = null

    val origName: String? = null

    //补充说明
    val remarks: String? = null

    //凭证,全路径地址,多个已英文逗号隔开
    val evidences: List<String>? = null

    //底部 再次发起售后申请 0-不显示;1-显示
    val isShowApplyBtn: Int = 0
    val canPlatformIn: Boolean = false

    //撤回申请按钮 0-不显示;1-显示
    val isShowCancelBtn: Int = 0

    //0-自营;1-pop
    val isThirdCompany: Int = 0
}

/**
 * 商业收货信息
 */
class AfterSalesCompanyReceiveProductInfo {

    //收件人
    val recipient: String? = null

    //收货地址
    val deliveryAddress: String? = null

    //收货电话
    val receivingPhone: String? = null

    //快递说明
    val expressRemarks: String? = null

    fun isShowAfterSalesCompanyReceiveProductInfo(): Boolean {
        return !recipient.isNullOrEmpty()
                || !deliveryAddress.isNullOrEmpty()
                || !receivingPhone.isNullOrEmpty()
                || !expressRemarks.isNullOrEmpty()
    }
}

/**
 * 资质售后时-商品资质
 */
class AfterSalesGoodsLicense {
    val skuId: String? = null
    val productName: String? = null
    val spec: String? = null
    val imageUrl: String? = null
    //1-药监报告;2-首营资料
    val credentialType: String? = null
}

/**
 * 专票信息(只有申请专票时才会有)
 */
class AfterSalesSpecialInvoiceInfo {
    //公司名
    val companyName: String? = null

    //纳税人识别号
    val enterpriseRegistrationNo: String? = null

    //地址
    val registerAddress: String? = null

    //电话
    val phone: String? = null

    //银行名
    val bankName: String? = null

    //银行账号
    val acct: String? = null

    //接受电子专票：0-未勾选(不接受);1-勾选(接受)
    val isElectronicInvoice: Int = 0

    fun isShowAfterSalesSpecialInvoiceInfo(): Boolean {
        return !companyName.isNullOrEmpty()
                && !enterpriseRegistrationNo.isNullOrEmpty()
                && !registerAddress.isNullOrEmpty()
                && !phone.isNullOrEmpty()
                && !bankName.isNullOrEmpty()
                && !acct.isNullOrEmpty()
    }
}

/**
 * 协商历史
 */
class AfterSalesConsultHistory {
    //头像
    @SerializedName("logo")
    val avatar: String? = null
    //创建时间
    val createTime: String? = null

    //流程节点的文案：如：客户退回发票
    val auditProcessStateName: String? = null

    //售后类型
    val afterSalesTypeName: String? = null

    //客户/商家的名称
    val chatter: String? = null

    //凭证
    val evidences: List<String>? = null

    //补充说明
    val remarks: String? = null

    //快递单号，有则展示标题和该字段的值
    val expressNo: String? = null

    //物流公司，有则展示标题和该字段的值
    val expressName: String? = null

    //错误信息：如：公司名称，地址
    val incorrectInvoiceInfo: String? = null

    //需要补发的资质
    val reissueCredential: ReissueLicenseInfo? = null

    //商业收货信息
    @SerializedName("sellerAddressInfo")
    val companyReceiveProductInfo: AfterSalesCompanyReceiveProductInfo? = null

    //专票信息
    val specialInvoiceInfo: AfterSalesSpecialInvoiceInfo? = null

    /**
     * 是否显示物流
     */
    fun isShowLogistic(): Boolean {
        return !expressName.isNullOrEmpty() && !expressNo.isNullOrEmpty()
    }
}

/**
 *需要补发的资质
 */
class ReissueLicenseInfo {
    //企业资质：如购销合同,企业首营资料
    val corpCredential: String? = null

    //药检报告
    val drugSupervisionReport: String? = null

    //企业首营资料
    val productCredential: String? = null

    fun isShowReissueLicenseInfo(): Boolean {
        return !corpCredential.isNullOrEmpty()
                || !drugSupervisionReport.isNullOrEmpty()
                || !productCredential.isNullOrEmpty()
    }
}

/**
 * 退回发票信息
 */
class RefundInvoiceInfo {
    //物流公司
    var logisticsCompany: String? = null

    //快递单号
    var expressNo: String? = null

    //上传凭据
    var images: List<String>? = null

    //撤回申请按钮 0-不显示;1-显示
    var isShowCancelBtn: Int = 0

    //6-待客户退回发票
    var auditProcessState: Int = 0
}

