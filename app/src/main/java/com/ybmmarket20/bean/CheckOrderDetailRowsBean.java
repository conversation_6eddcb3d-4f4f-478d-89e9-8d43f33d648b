package com.ybmmarket20.bean;

import java.util.ArrayList;
import java.util.List;

/**
 * 订单详情商品item
 */
public class CheckOrderDetailRowsBean {
    public int id;
    public int orderId;
    public int productId;
    public int skuId;
    public int giftId;
    public double productPrice;
    public int productAmount;//商品数量
    public int type;
    public int status;
    public String productName;
    public String fob;//原价格
    public double purchasePrice;//入库价格
    public String manufacturer;
    public List<LabelIconBean> tagList;
    public double balanceAmount;//预返余额明细
    public double realPayAmount;//单品实付金额
    public double useBalanceAmount;//抵扣余额明细
    public double discountAmount;//单品优惠金额, 单品优惠总金额 = 单品优惠金额 + 抵扣余额明细
    public String blackProductText;
    public String spec;
    public String imageUrl;
    public boolean isCheck = true;
    public int isSplit;//是否可拆开、零
    public int mediumPackageNum;//中包装数量
    public ActivityTagBean productActivityTag;//活动标签
    public String costPrice;//成本价
    public int nearEffectiveFlag; //1 临期 2近效期
    public String refundCashPayAmount;
    public int extraGift;//1 赠品 0 不是赠品
    public String packageId; //套餐
    public int extraGiftVariety;//赠品种类数
    public String extraGiftId;//活动id
    public int isRandom;// 是否为随心拼商品，0:不是随心拼商品，1:是随心拼商品
    public String priceDesc;
    public String productCredential;//随货商品资质:1-药监报告;2-首营资质，该字段拼接返回使用逗号分隔
    public List<TagBean> afterSaleTags;
    public String giftPromotionId; //赠品关联的主品活动Id
    public int giveSkuType; //赠送类型(满赠才有) 1.满赠活动赠送  2.满赠活动赠品池任选
    public boolean isGiftSelected; //是否选中 giveSkuType 2.满赠活动赠品池任选 有效
    public int curSelectedCount; //当前选中数量
    public String nearEffect; //效期
    public String orderDetailId; //明细Id
    public String orderNo;

    public int getProductId() {
        if (productId <= 0) {
            return skuId;
        }
        return productId;
    }

    /**
     * 是否是赠品
     * @return
     */
    public boolean isGift() {
        return extraGift == 1;
    }

}
