package com.ybmmarket20.bean.homesteady

import com.ydmmarket.report.annotation.ReportEventName
import com.ydmmarket.report.annotation.ReportParamsKey
import java.io.Serializable

open class ComponentBean {
    var sptype: String? = null
    var jgspid: String? = null
    var sid: String? = null
    var pageType: String? = ""
    var pageId: String? = ""
    var pageName: String? = ""
    var rank: Int? = null //商品所属列表顺序
    var componentPosition: String? = "" //组件序号
    var componentName: String? = "" //组件名称
    var componentTitle: String? = "" //组件标题
}

class FeedComponentBean: ComponentBean() {
    var pageNum: String? = null
    var pageSize: String? = null
}

/**
 * 用于子模块的上报数据
 */
class SubModuleBean: ComponentBean() {
    var subModuleType: String? = "" //子模块类型
    var subModulePosition: String? = "" //子模块序号
    var subModuleName: String? = "" //子模块名称
    var voucherType: Int? = null //券类型
    var voucherTemplateId: Int? = null //券ID
    var voucherTitle: String? = "" //券标题
    var clickName: String? = "" //点击名称
    var clickLink: String? = "" //点击链接
}

class ComponentClickBean {
    var sptype: String? = null
    var jgspid: String? = null
    var sid: String? = null
    var pageType: String? = ""
    var pageId: String? = ""
    var pageName: String? = ""
    var rank: Int? = null //商品所属列表顺序
    var componentPosition: String? = "" //组件序号
    var componentName: String? = "" //组件名称
    var componentTitle: String? = "" //组件标题
    var clickName: String? = "" //点击名称
    var clickLink: String? = "" //点击链接
}

/**
 * 用于tab的存储数据
 */
data class HomeTabBean(
    var tabName: String?,
    var tabLink: String?,
    var sptype: String?,
    var jgspid: String?,
    var sid: String?,
    var pageType: String?,
    var pageId: String?,
    var pageName: String?,
    var componentPosition: String?,
    var componentName: String?,
    var componentTitle: String?
): Serializable

/**
 * APP端h5页面、原生新首页（含tab页），页面加载完毕上报一次，切换原生tab缓存不重新加载，不重复上报。
 * 不同账号*不同用户*不同入口*不同页面类型*不同页面
 */
open class AppPageCmsExposure {
    @ReportParamsKey("\$url")
    var url: String? = "" //页面完整路径
    @ReportParamsKey("\$title")
    var title: String? = "" //页面标题
    @ReportParamsKey("\$referrer")
    var referrer: String? = ""
    @ReportParamsKey("jgspid")
    var jgspid: String? = ""
    @ReportParamsKey("page_type")
    var page_type: String? = ""
    @ReportParamsKey("page_id")
    var page_id: String? = ""
    @ReportParamsKey("page_name")
    var page_name: String? = ""
    @ReportParamsKey("rank")
    var rank: Int? = null //商品所属列表顺序
    @ReportParamsKey("click_name")
    var click_name: String? = "" //点击名称
    @ReportParamsKey("click_link")
    var click_link: String? = "" //点击链接
}

@ReportEventName("page_list_build")
class PageListBuild: AppPageCmsExposure() {
    @ReportParamsKey("page_no")
    var page_no: Int? = 0 //当前页码
    @ReportParamsKey("page_size")
    var page_size: Int? = 0 //每页显示条数
    @ReportParamsKey("total_page")
    var total_page: Int? = 0 //总页数
    @ReportParamsKey("component_position")
    var component_position: String? = "" //组件序号
    @ReportParamsKey("component_name")
    var component_name: String? = "" //组件名称
    @ReportParamsKey("component_title")
    var component_title: String? = "" //组件标题
    @ReportParamsKey("sub_module_tab")
    var sub_module_tab: String? = "" //子模块tab
    @ReportParamsKey("sub_module_left_navigation")
    var sub_module_left_navigation: String? = "" //子模块左侧导航
    @ReportParamsKey("sub_module_top_navigation")
    var sub_module_top_navigation: String? = "" //子模块顶部导航
}

///**
// * 各个页面商品曝光，露出及报，一品一报
// */
//@ReportEventName("page_list_product_exposure")
//class PageListProductExposure {
//    @ReportParamsKey("\$url")
//    var url: String? = "" //页面完整路径
//    @ReportParamsKey("\$title")
//    var title: String? = "" //页面标题
//    @ReportParamsKey("\$referrer")
//    var referrer: String? = ""
//    @ReportParamsKey("jgspid")
//    var jgspid: String? = ""
//    @ReportParamsKey("page_type")
//    var page_type: String? = ""
//    @ReportParamsKey("page_id")
//    var page_id: String? = ""
//    @ReportParamsKey("page_name")
//    var page_name: String? = ""
//    @ReportParamsKey("rank")
//    var rank: Int? = null //商品所属列表顺序
//    @ReportParamsKey("result_cnt")
//    var result_cnt: Int? = 0 //列表结果总数量
//    @ReportParamsKey("page_no")
//    var page_no: Int? = 0 //当前页码
//    @ReportParamsKey("page_size")
//    var page_size: Int? = 0 //每页显示条数
//    @ReportParamsKey("total_page")
//    var total_page: Int? = 0 //总页数
//    @ReportParamsKey("component_position")
//    var component_position: String? = "" //组件序号
//    @ReportParamsKey("component_name")
//    var component_name: String? = "" //组件名称
//    @ReportParamsKey("component_title")
//    var component_title: String? = "" //组件标题
//    @ReportParamsKey("sub_module_tab")
//    var sub_module_tab: String? = "" //子模块tab
//    @ReportParamsKey("product_id")
//    var product_id: Long? = 0 //商品ID
//    @ReportParamsKey("product_name")
//    var product_name: String? = "" //商品名称
//    @ReportParamsKey("product_first")
//    var product_first: String? = "" //商品一级分类ID
//    @ReportParamsKey("product_price")
//    var product_price: Double? = 0.0 //商品现价
//    @ReportParamsKey("product_type")
//    var product_type: String? = "" //商品类型
//    @ReportParamsKey("product_shop_code")
//    var product_shop_code: String? = "" //商品店铺编码
//    @ReportParamsKey("product_shop_name")
//    var product_shop_name: String? = "" //商品店铺名称
//}

