package com.ybmmarket20.report.coupon

import com.ydmmarket.report.annotation.ReportEventName
import com.ydmmarket.report.annotation.ReportParamsKey

class CouponModel {
}

open class BaseRowListCoupon {
    @ReportParamsKey("jgspid") var jgspid: String? = null
    @ReportParamsKey("voucher_type") var voucherType: Int? = null
    @ReportParamsKey("voucher_template_id") var voucherTemplateId: Int? = null
    @ReportParamsKey("voucher_title") var voucherTitle: String? = null
    @ReportParamsKey("voucher_rank") var voucherRank: Int? = null
    @ReportParamsKey("sku_id") var skuId: Long? = null
    @ReportParamsKey("shop_code") var shopCode: String? = null
    @ReportParamsKey("shop_name") var shopName: String? = null
}


/**
 * 凑单页曝光
 */
@ReportEventName("app_page_makeuporder_exposure")
class MakeUpOrderCouponExposure {
    @ReportParamsKey("jgspid") var jgspid: String? = null
    @ReportParamsKey("voucher_type") var voucherType: Int = 0
    @ReportParamsKey("voucher_template_id") var voucherTemplateId: Int = 0
    @ReportParamsKey("voucher_title") var voucherTitle: String? = null
    @ReportParamsKey("page_version") var pageVersion: String? = null
    @ReportParamsKey("sku_id") var skuId: Int? = null
    @ReportParamsKey("shop_code") var shopCode: String? = null
    @ReportParamsKey("shop_name") var shopName: String? = null
}

/**
 * 待确认订单页优惠券曝光
 */
@ReportEventName("app_tobeconfirmedorder_voucher_exposure")
data class PaymentCouponExposure(
    @ReportParamsKey("voucher_type") val voucherType: Int,
    @ReportParamsKey("voucher_template_id") val voucherTemplateId: Int,
    @ReportParamsKey("voucher_title") val voucherTitle: String?,
    @ReportParamsKey("voucher_rank") val voucherRank: Int,
//    @ReportParamsKey("click_name") val clickName: String,
//    @ReportParamsKey("click_link") val clickLink: String
)

/**
 * 待确认订单页优惠券勾选
 */
@ReportEventName("app_tobeconfirmedorder_voucher_tick")
data class PaymentCouponCheck(
    @ReportParamsKey("voucher_type") val voucherType: Int,
    @ReportParamsKey("voucher_template_id") val voucherTemplateId: Int,
    @ReportParamsKey("voucher_title") val voucherTitle: String?,
    @ReportParamsKey("voucher_rank") val voucherRank: Int,
//    @ReportParamsKey("click_name") val clickName: String,
//    @ReportParamsKey("click_link") val clickLink: String,
    @ReportParamsKey("is_tick") val isTick: String?
)