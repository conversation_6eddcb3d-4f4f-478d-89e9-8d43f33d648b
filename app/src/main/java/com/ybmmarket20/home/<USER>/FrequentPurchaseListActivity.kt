package com.ybmmarket20.home.newpage

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Build
import android.view.View
import android.widget.TextView
import androidx.activity.viewModels
import androidx.annotation.RequiresApi
import androidx.core.view.isVisible
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.analysys.ANSAutoPageTracker
import com.chad.library.adapter.base.BaseQuickAdapter
import com.ybmmarket20.R
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.common.JGTrackManager
import com.ybmmarket20.common.JgTrackBean
import com.ybmmarket20.common.TrackManager
import com.ybmmarket20.common.TrackManager.TrackFrequentPurchaseList.EVENT_FREQUENT_PURCHASE_LIST_EXPOSURE
import com.ybmmarket20.common.YBMAppLike
import com.ybmmarket20.common.getFullClassName
import com.ybmmarket20.common.jgTrackResourceProductClick
import com.ybmmarket20.common.splicingModule2Entrance
import com.ybmmarket20.common.splicingPageTitle2Entrance
import com.ybmmarket20.constant.IntentCanst
import com.ybmmarket20.databinding.ActivitySuperValueFloorListBinding
import com.ybmmarket20.home.newpage.viewmodel.FrequentPurchaseListViewModel
import com.ybmmarket20.utils.AdapterUtils
import com.ybmmarket20.utils.analysis.NewTrackParams
import com.ybmmarket20.viewmodel.BaseViewModel
import com.ybmmarketkotlin.adapter.GoodListAdapterNew
import com.ybmmarketkotlin.utils.RouterJump

/**
 * @class   FrequentPurchaseListActivity
 * <AUTHOR>
 * @date  2024/4/17
 * @description  常购清单List页面
 *              刷新用的SmartRefreshLayout 加载用的adapter的setOnLoadMoreListener
 */
class FrequentPurchaseListActivity : BaseActivity(), ANSAutoPageTracker {

	private lateinit var mBinding: ActivitySuperValueFloorListBinding
	private val mViewModel: FrequentPurchaseListViewModel by viewModels()
	private lateinit var tvNum: TextView
	private var anchorCsuIds: String = ""
	private var mEntrance:String = ""
	private var mTitle:String = ""

	private val mAdapter: GoodListAdapterNew by lazy {
		GoodListAdapterNew(R.layout.item_goods_new, arrayListOf()).apply {
			newTrackParams = NewTrackParams(pageId = TrackManager.TrackFrequentPurchaseList.TRACK_FREQUENT_PURCHASE_PAGE_ID.toString(), module = TrackManager.TrackFrequentPurchaseList.TRACK_FREQUENT_PURCHASE_MODULE_6)
			jgTrackBean = JgTrackBean(
					pageId = JGTrackManager.TrackFrequentPurchase.PAGE_ID,
					title = JGTrackManager.TrackFrequentPurchase.TITLE,
					module = "商品列表",
					entrance = if (mEntrance.isEmpty()) splicingModule2Entrance(JGTrackManager.TrackFrequentPurchase.TITLE,mTitle)  else splicingModule2Entrance(splicingPageTitle2Entrance(mEntrance, JGTrackManager.TrackFrequentPurchase.TITLE),mTitle),
					jgReferrer = this.getFullClassName(),
					jgReferrerTitle = JGTrackManager.TrackFrequentPurchase.TITLE,
					jgReferrerModule = "商品列表",
					url = this.getFullClassName()
			)

			productClickTrackListener = { rowsBean, position,isBtnClick,mContent,number ->
				val map = hashMapOf<String, Any>(Pair(TrackManager.FIELD_PAGE_ID, TrackManager.TrackFrequentPurchaseList.TRACK_FREQUENT_PURCHASE_PAGE_ID), Pair(TrackManager.FIELD_SKU_ID, rowsBean.id))

				TrackManager.clickEventTrack(TrackManager.TrackFrequentPurchaseList.EVENT_ACTION_PRODUCT_CLICK, map)

				var productTag = ""
				rowsBean.tags?.productTags?.let { tagList ->
					tagList.forEachIndexed { index, tagBean ->
						if (index != tagList.size - 1) {
							productTag += tagBean.text + "，"
						} else {
							productTag += tagBean.text
						}
					}
				}
				rowsBean.tags?.dataTags?.let { tagList ->
					tagList.forEachIndexed { index, tagBean ->
						if (index != tagList.size - 1) {
							productTag += tagBean.text + "，"
						} else {
							productTag += tagBean.text
						}
					}
				}
			}

			resourceViewTrackListener = { rowsBean, position,_ ->
				var productTag = ""
				rowsBean.tags?.productTags?.let { tagList ->
					tagList.forEachIndexed { index, tagBean ->
						if (index != tagList.size - 1) {
							productTag += tagBean.text + "，"
						} else {
							productTag += tagBean.text
						}
					}
				}
				rowsBean.tags?.dataTags?.let { tagList ->
					tagList.forEachIndexed { index, tagBean ->
						if (index != tagList.size - 1) {
							productTag += tagBean.text + "，"
						} else {
							productTag += tagBean.text
						}
					}
				}
			}
		}
	}

	private var scrollDistance = 0 //RecycleView滑动距离

	companion object {

		private const val INTENT_TITLE = "intent_title"
		private const val INTENT_ANCHORCSUIDS = "intent_anchorCsuIds"
		private const val SCREEN_MULTIPLE = 3 //屏幕倍数

		fun launchActivity(
				mContext: Context,
				title: String,
				anchorCsuIds:String="",
				entrance: String = "",
		) {
			val intent = Intent(mContext, FrequentPurchaseListActivity::class.java).apply {
				putExtra(INTENT_TITLE, title)
				putExtra(INTENT_ANCHORCSUIDS, anchorCsuIds)
				putExtra(IntentCanst.JG_ENTRANCE, entrance)
			}
			mContext.startActivity(intent)
		}
	}

	override fun getContentViewId(): Int = R.layout.activity_super_value_floor_list

	override fun initViewBefore() {
		super.initViewBefore()
		tvNum = findViewById(R.id.tv_num)
		anchorCsuIds = intent.getStringExtra(INTENT_ANCHORCSUIDS) ?: ""
		mEntrance = intent.getStringExtra(IntentCanst.JG_ENTRANCE) ?: ""
		mTitle = intent.getStringExtra(INTENT_TITLE) ?: ""
		mBinding.tvTitle.text = mTitle
		mBinding.rvList.layoutManager = LinearLayoutManager(this)
		mBinding.rvList.adapter = mAdapter

		mBinding.smartRefreshLayout.setOnRefreshListener {
			toRequest(true)
		}

		mAdapter.setOnLoadMoreListener(object : BaseQuickAdapter.RequestLoadMoreListener {
			override fun onLoadMoreRequested() {
				toRequest(false)
			}
		},mBinding.rvList)
	}

	private fun initBroadCastReceiver() {
		br = object : BroadcastReceiver() {
			override fun onReceive(
					context: Context,
					intent: Intent
			) {
				if (intent != null) {
					if (IntentCanst.CART_NUM_CHANGED == intent.action) { //购物车数量修改了
						getCartNumber()
					}
				}
			}
		}
		val intentFilter = IntentFilter()
		intentFilter.addAction(IntentCanst.CART_NUM_CHANGED)
		LocalBroadcastManager.getInstance(applicationContext).registerReceiver(br, intentFilter)
	}

	override fun initData() {
		getCartNumber()
		mBinding.smartRefreshLayout.autoRefresh()
		initBroadCastReceiver()

		TrackManager.exposureEventTrack(EVENT_FREQUENT_PURCHASE_LIST_EXPOSURE)
	}

	private fun toRequest(isRefresh:Boolean){
		mViewModel.requestListData(isRefresh,anchorCsuIds)
	}

	override fun getBaseViewModel(): BaseViewModel = mViewModel

	@RequiresApi(Build.VERSION_CODES.M)
	override fun initObserverBefore() {
		super.initObserverBefore()
		mBinding = binding as ActivitySuperValueFloorListBinding
		mBinding.apply {

			rvList.post {
				val rvHeight = rvList.height
				rvList.addOnScrollListener(object : RecyclerView.OnScrollListener() {
					override fun onScrolled(
							recyclerView: RecyclerView,
							dx: Int,
							dy: Int
					) {
						super.onScrolled(recyclerView, dx, dy)
						scrollDistance += dy
						ivTopping.isVisible = scrollDistance > SCREEN_MULTIPLE * rvHeight
					}
				})
			}

			ivShopCar.setOnClickListener {
				RouterJump.jump2ShopCar()

				TrackManager.clickEventTrack(TrackManager.TrackFrequentPurchaseList.EVENT_ACTION_SHOPPING_CART_CLICK, hashMapOf())
			}


			ivTopping.setOnClickListener {
				rvList.smoothScrollToPosition(0)

				val map = hashMapOf<String,Any>(
						Pair(TrackManager.FIELD_PAGE_ID, TrackManager.TrackFrequentPurchaseList.TRACK_FREQUENT_PURCHASE_PAGE_ID),
				)

				TrackManager.clickEventTrack(TrackManager.TrackFrequentPurchaseList.EVENT_ACION_TOP_CLICK, map)
			}
		}

		mViewModel.tabListLiveData.observe(this){
			val isRefresh = it.first
			val list = it.second
			AdapterUtils.addLocalTimeForRows(list)
			if (isRefresh){
				mAdapter.setNewData((list as? MutableList<Any?>)?: arrayListOf())
			}else{
				mAdapter.addData((list as? MutableList<Any?>)?: arrayListOf<Any?>())
				mAdapter.loadMoreComplete()
			}
		}

		mViewModel.canLoadLiveData.observe(this){
			 mAdapter.setEnableLoadMore(it)
		}

		mViewModel.refreshFinishedLiveData.observe(this){
			mBinding.smartRefreshLayout.finishRefresh()
		}
	}

	private fun getCartNumber() {
		val num = YBMAppLike.cartNum
		if (num > 0) {
			if (num > 99) {
				tvNum.text = 99.toString() + "+"
			} else {
				tvNum.text = num.toString() + ""
			}
			tvNum.visibility = View.VISIBLE
		} else {
			tvNum.text = ""
			tvNum.visibility = View.GONE
		}
	}

	override fun registerPageProperties(): MutableMap<String, Any> {
		val properties: MutableMap<String, Any> = HashMap()
		properties[JGTrackManager.FIELD.FIELD_PAGE_ID] = JGTrackManager.TrackFrequentPurchase.PAGE_ID
		properties[JGTrackManager.FIELD.FIELD_TITLE] = JGTrackManager.TrackFrequentPurchase.TITLE
		return properties
	}

	override fun registerPageUrl(): String = this.getFullClassName()

}