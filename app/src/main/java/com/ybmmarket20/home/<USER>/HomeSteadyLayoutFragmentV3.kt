package com.ybmmarket20.home.newpage

//import kotlinx.android.synthetic.main.fragment_home_steady_layout_v3.view_tab_gradient
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.graphics.Bitmap
import android.graphics.Color
import android.graphics.Typeface
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.text.TextUtils
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.Animation
import android.view.animation.TranslateAnimation
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.isVisible
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.viewModels
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.widget.ViewPager2
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.drawable.GlideDrawable
import com.bumptech.glide.load.resource.gif.GifDrawable
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.target.Target
import com.github.mzule.activityrouter.router.Routers
import com.google.android.material.tabs.TabLayout
import com.google.gson.Gson
import com.jeremyliao.liveeventbus.LiveEventBus
import com.xyy.canary.utils.LogUtil
import com.ybm.app.common.BaseYBMApp
import com.ybm.app.common.NtpTrustedTime
import com.ybmmarket20.R
import com.ybmmarket20.activity.AptitudeActivity
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.homesteady.HomeTabBean
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.common.JGTrackManager
import com.ybmmarket20.common.JgTrackBean
import com.ybmmarket20.common.LiveEventBusManager
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.common.TrackManager
import com.ybmmarket20.common.dp
import com.ybmmarket20.common.getColorById
import com.ybmmarket20.common.getFullClassName
import com.ybmmarket20.common.glideLoadWithPlaceHolder
import com.ybmmarket20.common.init
import com.ybmmarket20.common.jgTrackHomeBtnClick
import com.ybmmarket20.common.jgTrackHomeResourceClick
import com.ybmmarket20.common.splicingUrlWithParams
import com.ybmmarket20.common.splitJointJgspid
import com.ybmmarket20.constant.IntentCanst
import com.ybmmarket20.constant.ROUTER_SEARCH_PRODUCT_OP
import com.ybmmarket20.databinding.TabImgTypeBinding
import com.ybmmarket20.databinding.TabTextTypeBinding
import com.ybmmarket20.fragments.HomeSteadyAlertFragmentV3
import com.ybmmarket20.home.newpage.bean.HomeSearchContentResponse
import com.ybmmarket20.home.newpage.bean.NewSearchBox
import com.ybmmarket20.home.newpage.bean.TabBean
import com.ybmmarket20.home.newpage.widget.InterceptClickTab
import com.ybmmarket20.reportBean.AppActionTopHotWordClick
import com.ybmmarket20.search.SearchProductOPActivity
import com.ybmmarket20.utils.AuditStatusHomeFloatManager
import com.ybmmarket20.utils.HomePageInfoSpUtil
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.SpUtil
import com.ybmmarket20.utils.analysis.XyyIoUtil
import com.ybmmarket20.view.DialImageView
import com.ybmmarket20.viewmodel.HomeAptitudeViewModel
import com.ybmmarket20.xyyreport.SpmLogUtil
import com.ybmmarket20.xyyreport.page.home.IHandleTabFragmentPv
import com.ybmmarket20.xyyreport.spm.XyyReportActivity
import com.ybmmarketkotlin.utils.RouterJump
import kotlinx.android.synthetic.main.fragment_home_steady_layout_v3.cl_dial_suspension
import kotlinx.android.synthetic.main.fragment_home_steady_layout_v3.iv_close
import kotlinx.android.synthetic.main.fragment_home_steady_layout_v3.iv_dial_suspension
import kotlinx.android.synthetic.main.fragment_home_steady_layout_v3.iv_head_bg
import kotlinx.android.synthetic.main.fragment_home_steady_layout_v3.iv_icon_all_drug
import kotlinx.android.synthetic.main.fragment_home_steady_layout_v3.ll_all_drug
import kotlinx.android.synthetic.main.fragment_home_steady_layout_v3.search_view
import kotlinx.android.synthetic.main.fragment_home_steady_layout_v3.tab_layout
import kotlinx.android.synthetic.main.fragment_home_steady_layout_v3.viewpager
import java.lang.reflect.Field


/**
 * <AUTHOR>
 * @date 2024-04-02
 * @description 首页fragment（静态布局） 目前首页改版最新的Fragment
 */
class HomeSteadyLayoutFragmentV3 : HomeSteadyAlertFragmentV3(), View.OnClickListener,
    IHandleTabFragmentPv {

    override fun getRootLayoutParam(): ViewGroup.LayoutParams {
        return ConstraintLayout.LayoutParams(ConstraintLayout.LayoutParams.MATCH_PARENT, 1)
    }

    private var br: BroadcastReceiver? = null
    private var auditStatusHomeFloatManager: AuditStatusHomeFloatManager? = null
    private val mViewModel: HomeAptitudeViewModel by viewModels()
    private val mFragments = arrayListOf<HomeTabCommonFragment>()

    private var mIsVisible = true

    //    private lateinit var mediator: TabLayoutMediatorExt
    // tab栏的所有tab名称
    private var mTabList = arrayListOf<TabBean>()
    private var mSearchContentList = arrayListOf<HomeSearchContentResponse>()

    private var preTabPosition = -1 //上一次tab选中下标
    private var curTabPosition = 0 //当前tab选中下标
    private var curTabTitle = ""
    private var curTabId = ""
    private var mIsTabSelected = false
    private val mTabSelectListener by lazy {
        object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab) {
                search_view.isHomePage = isHomePage()
                val customView = tab.customView ?: return
                preTabPosition = curTabPosition
                curTabPosition = tab.position
                val mTabBean = (tab.tag as? TabBean) ?: return
                try {
                    //底部tab曝光
                    homeCommonTabClickBottomExposure(transformPosition(tab_layout.selectedTabPosition))
                    //首页tab点击
                    if (mIsTabSelected) {
                        context?.let { onTabClick(it, mTabBean.trackData, transformPosition(tab_layout.selectedTabPosition)) }
                    }
                    //首页pv
                    mFragments[transformPosition(tab_layout.selectedTabPosition)].trackHomePv(requireActivity(), mTabBean.trackData)
                    if (context is XyyReportActivity) {
                        (context as XyyReportActivity).mScmCnt = mTabBean.trackData?.scmEntity
                    }
                    //首页默认tab点击
                    if (!mIsTabSelected) {
                        mIsTabSelected = true
//                        context?.let { onTabClick(it, mTabBean.trackData, transformPosition(tab_layout.selectedTabPosition)) }
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }
                when (customView.tag) {
                    TAG_TAB_IMG -> {
                        curTabTitle = "" //图片类型没有标题
                        curTabId = mTabBean.tabId?:""
                        if (mTabBean.isH5Url()) { //其实不会走这里 因为这种类型 在初始化tab的时候 已经拦截掉了
                            mTabBean.jumpLink?.let {
                                var url = it
                                val mParams:HashMap<String,Any> = hashMapOf()
                                mParams["referrer"] = <EMAIL>()
                                mParams["referrerTitle"] = "首页"
                                mParams["referrerModule"] = "首页-${curTabTitle}"
                                mParams["entrance"] = "首页（${curTabTitle}）"
                                mParams["jgspid"] = mTabBean.jgspid ?: ""
                                url = splicingUrlWithParams(url,mParams)
                                RoutersUtils.open(url)


                            }
                            return
                        }
                        changeTabSearchTabUIState(mTabBean)

                        val textTabList = mTabList.filter { it -> !it.isH5Url() }
                        if (textTabList.isNotEmpty()) {
                            textTabList.forEachIndexed { index, tabBean ->
                                if (tabBean == tab.tag) {
                                    viewpager.setCurrentItem(index, false)
                                }
                            }
                        }

                        val map = HashMap<String, Any>().apply {
                            put(TrackManager.FIELD_TITLE, "")
                            put(TrackManager.FIELD_OFFSET, tab.position + 1)
                            put(TrackManager.FIELD_URL, if (mTabBean.isH5Url()) mTabBean.jumpLink
                                    ?: "" else "")
                            put(TrackManager.FIELD_PAGE_ID, getPageId())
                        }
                        TrackManager.clickEventTrack(TrackManager.TrackHome.EVENT_ACTION_TAB_POSITION_CLICK, map)

                        requireActivity().jgTrackHomeBtnClick(<EMAIL>(),"顶部导航","顶部导航")
                    }

                    TAG_TAB_TEXT -> {
                        curTabTitle = mTabBean.tabName?:""
                        curTabId = mTabBean.tabId?:""

                        if (mTabBean.isH5Url()) { ////其实不会走这里 因为这种类型 在初始化tab的时候 已经拦截掉了
                            mTabBean.jumpLink?.let {
                                var url = it
                                val mParams:HashMap<String,Any> = hashMapOf()
                                mParams["referrer"] = <EMAIL>()
                                mParams["referrerTitle"] = "首页"
                                mParams["referrerModule"] = "首页-${curTabTitle}-${mTabBean.tabName?:""}"
                                mParams["entrance"] = "首页（${curTabTitle}）"
                                mParams["jgspid"] = mTabBean.jgspid ?: ""
                                url = splicingUrlWithParams(url,mParams)
                                RoutersUtils.open(url)
                            }
                            return
                        }

                        changeTabSearchTabUIState(mTabBean)

                        customView.findViewById<TextView>(R.id.tv_tab)?.apply {
                            typeface = Typeface.DEFAULT_BOLD
                            setTextSize(TypedValue.COMPLEX_UNIT_DIP, 17f)
                            mTabBean.wordColor?.let {
                                try {
                                    setTextColor(Color.parseColor(it))
                                } catch (e: Exception) {
                                    e.printStackTrace()
                                    setTextColor(getColorById(R.color.black))
                                }
                            } ?: kotlin.run {
                                setTextColor(getColorById(R.color.black))
                            }

                            mTabBean.wordTransparency?.let {
                                var mAlpha = 1f
                                try {
                                    mAlpha = it.toFloat() / 100
                                } catch (e: Exception) {
                                }
                                alpha = mAlpha
                            }
                        }

                        val textTabList = mTabList.filter { it -> !it.isH5Url() }
                        if (textTabList.isNotEmpty()) {
                            textTabList.forEachIndexed { index, tabBean ->
                                if (tabBean == tab.tag) {
                                    viewpager.setCurrentItem(index, false)
                                }
                            }
                        }

                        val map = HashMap<String, Any>().apply {
                            put(TrackManager.FIELD_TITLE, mTabBean.tabName ?: "")
                            put(TrackManager.FIELD_OFFSET, tab.position + 1)
                            put(TrackManager.FIELD_URL, "")
                            put(TrackManager.FIELD_PAGE_ID, getPageId())
                        }
                        TrackManager.clickEventTrack(TrackManager.TrackHome.EVENT_ACTION_TAB_POSITION_CLICK, map)

                        requireActivity().jgTrackHomeBtnClick(<EMAIL>(),"顶部导航","顶部导航-${mTabBean.tabName?:""}")
                    }

                    else -> {}
                }
            }

            override fun onTabUnselected(tab: TabLayout.Tab) {
                val mCustomView = tab.customView ?: return
                when (mCustomView.tag) {

                    TAG_TAB_IMG -> {
                    }

                    TAG_TAB_TEXT -> {
                        mCustomView.findViewById<TextView>(R.id.tv_tab)?.apply {
                            setTextSize(TypedValue.COMPLEX_UNIT_DIP, 15f)
                            setTextColor(getColorById(R.color.black))
                            typeface = Typeface.DEFAULT
                            alpha = 1f
                        }
                    }

                    else -> {}
                }
            }

            override fun onTabReselected(tab: TabLayout.Tab) {
            }
        }
    }

    companion object {
        const val TAG = "HomeSteadyLayoutFragmentV3"
        const val TAG_TAB_IMG = "tag_tab_img"
        const val TAG_TAB_TEXT = "tag_tab_text"

        const val JG_PAGE_TYPE = "newhome"
    }

    override fun getLayoutId(): Int = R.layout.fragment_home_steady_layout_v3

    override fun getDialSuspension(): DialImageView = iv_dial_suspension


    override fun initData(content: String?) {
        initBroadCastReceiver()
        initObserver()
//        initCache()
        refreshData()
        iv_dial_suspension.setHomeAlertAnalysisCallback(this)
        initMessageCountForBubble(search_view.getBubbleView())
        NtpTrustedTime.getInstance().check()
        trackHomeExposure()
    }

    //初始化缓存数据
    private fun initCache(){
        HomePageInfoSpUtil.getHomePageTabResponse()?.let {
            mViewModel.setCacheTabData(BaseBean.newSuccessBaseBean(it))
        }

    }

    private fun refreshData(){
        initSearchContent()
        requestTabList()
        requestHomeAlert()
        getTurnTable(iv_dial_suspension, cl_dial_suspension)
        mViewModel.getHomeAptitudeStatus()
    }

    private fun trackHomeExposure() {
        TrackManager.exposureEventTrack(TrackManager.TrackHome.EVENT_HOMEPAGE_EXPOSURE)
    }

    private fun requestTabList() {
        mViewModel.getTabList()
    }

    /**
     * 初始化tab
     * 不用TabLayoutMediatorExt，tabLayout单独监听
     */
    private fun initTabLayout() {
        viewpager.init(this, mFragments)
        viewpager.isUserInputEnabled = false
        viewPager2SetInnerRecyclerView(viewpager)
//        mediator =
//                TabLayoutMediatorExt(tab_layout, viewpager, true, false) { tab, position ->
//                    val tabBean = tabList[position]
//                    when (tabBean){
//
//                        is TabImgBean ->{
//                            val iv = ImageView(requireActivity()).apply {
//                                Glide.with(requireActivity()).load(tabBean.imgUrl).into(this)
//                            }
//                            tab.customView = iv
//                        }
//
//                        is TabNormalBean ->{
//                            val tv = TextView(requireActivity()).apply {
//                                setTextSize(TypedValue.COMPLEX_UNIT_DIP, 16f)
//                                gravity = Gravity.CENTER
//                                setTextColor(getColorById(R.color.black))
//                            }
//                            tab.customView = tv
//                            tv.text = tabBean.content
//                        }
//
//                        else ->{}
//                    }
//                }
        tab_layout.removeAllTabs()
        tab_layout.addOnTabSelectedListener(mTabSelectListener)
        tab_layout.setSelectedTabIndicator(R.drawable.home_drawable_tab_indicator)
        tab_layout.setSelectedTabIndicatorHeight(4.dp)

        mTabList.forEachIndexed() { index,tabBean ->
            tab_layout.addTab(newInterceptClickTab().apply {
                customView = if (tabBean.isTabPic()) {
                    val tabBinding = DataBindingUtil.inflate<TabImgTypeBinding>(LayoutInflater.from(requireActivity()), R.layout.tab_img_type, null, false)
                    Glide.with(requireActivity()).load(tabBean.tabImag).into(tabBinding.ivTab)
                        tabBean.isEyeImg?.let {
                            if (it.isNotEmpty()) {
                                val drawableTypeRequest = Glide.with(requireActivity()).load(it)
                                if (it.endsWith("gif")) {
                                    drawableTypeRequest.asGif().listener(object : RequestListener<String?, GifDrawable?> {
                                        override fun onException(
                                                e: Exception?,
                                                model: String?,
                                                target: Target<GifDrawable?>?,
                                                isFirstResource: Boolean
                                        ): Boolean {
                                            tabBinding.ivTip.isVisible = false
                                            tabBinding.guideLine.setGuidelineEnd(0)
                                            return false
                                        }

                                        override fun onResourceReady(
                                                resource: GifDrawable?,
                                                model: String?,
                                                target: Target<GifDrawable?>?,
                                                isFromMemoryCache: Boolean,
                                                isFirstResource: Boolean
                                        ): Boolean {
                                            tabBinding.ivTip.isVisible = true
                                            tabBinding.guideLine.setGuidelineEnd(12.dp)
                                            return false
                                        }
                                    }).override(16.dp, 9.dp) //设置这个是为了回调监听 不然不知道多大会不回调
                                            .placeholder(R.drawable.transparent).error(R.drawable.transparent).into(tabBinding.ivTip)
                                } else {
                                    drawableTypeRequest.asBitmap().listener(object : RequestListener<String?, Bitmap?> {
                                        override fun onException(
                                                e: Exception?,
                                                model: String?,
                                                target: Target<Bitmap?>?,
                                                isFirstResource: Boolean
                                        ): Boolean {
                                            tabBinding.ivTip.isVisible = false
                                            tabBinding.guideLine.setGuidelineEnd(0)
                                            return false
                                        }

                                        override fun onResourceReady(
                                                resource: Bitmap?,
                                                model: String?,
                                                target: Target<Bitmap?>?,
                                                isFromMemoryCache: Boolean,
                                                isFirstResource: Boolean
                                        ): Boolean {
                                            tabBinding.ivTip.isVisible = true
                                            tabBinding.guideLine.setGuidelineEnd(12.dp)
                                            return false
                                        }
                                    }).dontAnimate()
                                            .override(16.dp, 9.dp) //设置这个是为了回调监听 不然不知道多大会不回调
                                            .placeholder(R.drawable.transparent).error(R.drawable.transparent).into(tabBinding.ivTip)
                                }
                            }else{
                                tabBinding.ivTip.isVisible = false
                                tabBinding.guideLine.setGuidelineEnd(0)
                            }
                        }?: kotlin.run {
                            tabBinding.ivTip.isVisible = false
                            tabBinding.guideLine.setGuidelineEnd(0)
                        }

                    tabBinding.root.tag = TAG_TAB_IMG
                    tabBinding.root
                } else {
                    val tabBinding = DataBindingUtil.inflate<TabTextTypeBinding>(LayoutInflater.from(requireActivity()), R.layout.tab_text_type, null, false)
                    tabBinding.tvTab.text = tabBean.tabName
                    tabBinding.tvTab.typeface = Typeface.DEFAULT
                    tabBean.isEyeImg?.let {
                        if (it.isNotEmpty()) {
                            val drawableTypeRequest = Glide.with(requireActivity()).load(it)
                            if (it.endsWith("gif")) {
                                drawableTypeRequest.asGif().listener(object : RequestListener<String?, GifDrawable?> {
                                            override fun onException(
                                                    e: Exception?,
                                                    model: String?,
                                                    target: Target<GifDrawable?>?,
                                                    isFirstResource: Boolean
                                            ): Boolean {
                                                tabBinding.ivTip.isVisible = false
                                                tabBinding.guideLine.setGuidelineEnd(0)
                                                return false
                                            }

                                            override fun onResourceReady(
                                                    resource: GifDrawable?,
                                                    model: String?,
                                                    target: Target<GifDrawable?>?,
                                                    isFromMemoryCache: Boolean,
                                                    isFirstResource: Boolean
                                            ): Boolean {
                                                tabBinding.ivTip.isVisible = true
                                                tabBinding.guideLine.setGuidelineEnd(12.dp)
                                                return false
                                            }
                                        }).override(16.dp, 9.dp) //设置这个是为了回调监听 不然不知道多大会不回调
                                        .placeholder(R.drawable.transparent).error(R.drawable.transparent).into(tabBinding.ivTip)
                            } else {
                                drawableTypeRequest.asBitmap().listener(object : RequestListener<String?, Bitmap?> {
                                            override fun onException(
                                                    e: Exception?,
                                                    model: String?,
                                                    target: Target<Bitmap?>?,
                                                    isFirstResource: Boolean
                                            ): Boolean {
                                                tabBinding.ivTip.isVisible = false
                                                tabBinding.guideLine.setGuidelineEnd(0)
                                                return false
                                            }

                                            override fun onResourceReady(
                                                    resource: Bitmap?,
                                                    model: String?,
                                                    target: Target<Bitmap?>?,
                                                    isFromMemoryCache: Boolean,
                                                    isFirstResource: Boolean
                                            ): Boolean {
                                                tabBinding.ivTip.isVisible = true
                                                tabBinding.guideLine.setGuidelineEnd(12.dp)
                                                return false
                                            }
                                        })
                                        .dontAnimate()
                                        .override(16.dp, 9.dp) //设置这个是为了回调监听 不然不知道多大会不回调
                                        .placeholder(R.drawable.transparent).error(R.drawable.transparent).into(tabBinding.ivTip)
                            }
                        }else{
                            tabBinding.ivTip.isVisible = false
                            tabBinding.guideLine.setGuidelineEnd(0)
                        }
                    }?:run {
                        tabBinding.ivTip.isVisible = false
                        tabBinding.guideLine.setGuidelineEnd(0)
                    }
                    tabBinding.root.tag = TAG_TAB_TEXT
                    tabBinding.root
                }

                view.setOnClickListener {
                    tabBean.jumpLink?.let {
                        context?.let { it1 -> onTabClick(it1, tabBean.trackData, index) }
                        RoutersUtils.open(splicingUrlWithParams(it, mapOf<String, Any>("jgspid" to (tabBean.jgspid ?: ""))))
                        requireActivity().jgTrackHomeBtnClick(<EMAIL>(),"顶部导航","顶部导航-${tabBean.tabName?:""}")
                    }
                }

                tag = tabBean
            })
        }
    }

    //Viewpager2反射设置内部RecyclerView
    private fun viewPager2SetInnerRecyclerView(mViewPager2: ViewPager2) {
        try {
            val recyclerViewField: Field = ViewPager2::class.java.getDeclaredField("mRecyclerView")
            recyclerViewField.isAccessible = true
            //内部recyclerView
            val recyclerView: RecyclerView =
                    recyclerViewField.get(mViewPager2) as RecyclerView
            recyclerView.overScrollMode = View.OVER_SCROLL_NEVER
        } catch (ignore: Exception) {
            LogUtil.d(TAG, "ViewPager2反射异常: $ignore")
        }
    }

    private fun initSearchContent() {
        mViewModel.getSearchHotList()
    }

    override fun initTitle() {}
    override fun getParams(): RequestParams? = null
    override fun getUrl(): String? = null

    private fun isHomePage(): Boolean = tab_layout.selectedTabPosition == 0

    private fun getPageId(): String {
        try {
            val data = mTabList[curTabPosition]
            return data.pageId ?: ""
        } catch (e: Exception) {
            e.printStackTrace()
            return ""
        }
    }

    private fun getCurTabBean(): TabBean? { //获取当前的选择的tab
        try {
            return mTabList[curTabPosition]
        } catch (e: Exception) {
            e.printStackTrace()
            return null
        }
    }

    private fun getPreTabBean(): TabBean? { //获取上一次选择的tab
        try {
            return mTabList[preTabPosition]
        } catch (e: Exception) {
            e.printStackTrace()
            return null
        }
    }


    private fun initObserver() {
        ll_all_drug.setOnClickListener(this)
        iv_icon_all_drug.setOnClickListener(this)
        iv_close.setOnClickListener(this)

        search_view.apply {
            mItemClickListener = { content, position, isSearchIcon ->

                val map = HashMap<String, Any>().apply {
                    put(TrackManager.FIELD_PAGE_ID, TrackManager.TrackHome.TRACK_HOME_PAGE_ID)
                    put(TrackManager.FIELD_CONTENT, content?.hotWord?:"")
                    put(TrackManager.FIELD_PAGE_ID, getPageId())
                }
                TrackManager.clickEventTrack(TrackManager.TrackHome.EVENT_ACTION_SEARCH_CLICK, map)
                if (isSearchIcon){
                    mContext.jgTrackHomeBtnClick(<EMAIL>(),"顶部搜索栏","搜索")
                }else{
                    mContext.jgTrackHomeBtnClick(<EMAIL>(),"顶部搜索栏","搜索框")
                }
                startActivity(Intent(requireActivity(), SearchProductOPActivity::class.java).apply {
                    var hotStyleJson = ""
                    if (content?.hotStyle != null && !TextUtils.isEmpty(content.hotStyle?.atmosphereIconText)) {
                        hotStyleJson = Gson().toJson(content.hotStyle)
                    }
                    var couponId=""
                    if (!TextUtils.isEmpty(content?.type) && content?.type == "5"&&!TextUtils.isEmpty(content.couponId)) {
                        couponId= content.couponId.toString()
                    }
                    putExtras(Bundle().apply {
                        putString("mContent",content?.hotWord ?: "")
                        putString("isShowCart", "1")
                        putString("couponId", couponId)
                        putString("hotLabel", hotStyleJson)
                        putString("searchJumpLink",content?.hrefUrl ?: "")
                        putString(Routers.KEY_RAW_URL, "$ROUTER_SEARCH_PRODUCT_OP?pageSource=${IntentCanst.PAGE_SOURCE_HOME_SEARCH}&mContent=${content?.hotWord ?: ""}&searchJumpLink=${content?.hrefUrl ?: ""}")
                        putString(SearchProductOPActivity.INTENT_ENTRANCE,"首页(搜索框)")
                        putSerializable(IntentCanst.JG_TRACK_BEAN,JgTrackBean(
                                jgReferrer = <EMAIL>(),
                                jgReferrerTitle = "首页",
                                jgReferrerModule = "商品列表",
                                entrance = "首页(搜索框)"
                        ))
                        putSerializable(IntentCanst.JG_APP_ACTION_TOP_HOT_WORD_CLICK,AppActionTopHotWordClick(
                                url = JGTrackManager.TrackSearchResult.TRACK_URL,
                                title = JGTrackManager.TrackSearchIntermediateState.TITLE,
                                referrer = JGTrackManager.TrackHomePage.TRACK_HOME_OUT_URL,
                                jgspid = getSearchJgspid(),
                                rank = position + 1,
                                click_name = content?.hotWord ?: "",
                                click_link = content?.hrefUrl ?: "",
                        ))
                        putString(IntentCanst.JG_JGSPID,getSearchJgspid())
                    })
                })
            }

            mJgResourceClickListener = {
                mContext.jgTrackHomeResourceClick(<EMAIL>(),"顶部搜索框",curTabTitle)
            }

            //没有搜索热词内容才会走这个点击
            setOnClickListener {
                if (search_view.hasSearchContent()) {
                    return@setOnClickListener
                }
                RouterJump.jump2SearchOPPage("首页(搜索框)")
                mContext.jgTrackHomeBtnClick(<EMAIL>(), "顶部搜索栏", "搜索框")
                mContext.jgTrackHomeResourceClick(<EMAIL>(), "顶部搜索框", curTabTitle)
            }
        }



        mViewModel.homeAptitudeStatusLiveData.observe(this) {
            if (it.isSuccess) {
                Handler(Looper.getMainLooper()).post {
                    val isShow = !it.data?.msg.isNullOrEmpty()
                    handleAuditPassedFloatView(isShow, it.data.msg, it.data.type, it.data.status)
                    if (isShow && it.data.type == 2) {
                        SpUtil.setAptitudeTip(it.data.msg)
                    } else SpUtil.setAptitudeTip("")
                }
            }
        }

        mViewModel.searchHotListLiveData.observe(this) {
            if (it.isSuccess) {
                mSearchContentList = it.data?.let { list -> list as ArrayList<HomeSearchContentResponse> }
                        ?: arrayListOf()
                search_view.setSearchContentList(mSearchContentList)
            } else {
                search_view.setSearchContentList(arrayListOf())
            }
        }

        mViewModel.tabListLiveData.observe(this) {tabBean->
            if (tabBean.isSuccess) {
                allDrugTrackData = tabBean.data.allProduct?.trackData
                HomePageInfoSpUtil.setHomePageTabResponse(tabBean.data)
                tabBean.data.classBtnBackground?.let { backGround ->
                    Glide.with(requireActivity()).load(backGround).listener(object : RequestListener<String?, GlideDrawable?> {
                        override fun onException(
                                e: java.lang.Exception?,
                                model: String?,
                                target: Target<GlideDrawable?>?,
                                isFirstResource: Boolean
                        ): Boolean {
                            iv_icon_all_drug.isVisible = false
                            ll_all_drug.isVisible = true
                            return false
                        }

                        override fun onResourceReady(
                                resource: GlideDrawable?,
                                model: String?,
                                target: Target<GlideDrawable?>?,
                                isFromMemoryCache: Boolean,
                                isFirstResource: Boolean
                        ): Boolean {
                            iv_icon_all_drug.isVisible = true
                            ll_all_drug.visibility = View.INVISIBLE
                            target?.let {
                                iv_icon_all_drug.background = resource
                            }
                            return false
                        }
                    }).placeholder(R.drawable.jiazaitu_min).error(R.drawable.jiazaitu_min).into(iv_icon_all_drug)
                } ?: kotlin.run {
                    iv_icon_all_drug.isVisible = false
                }

                tabBean.data.tabList?.let { tabList ->
                    mTabList.clear()
                    mTabList.addAll(tabList)
                    mFragments.clear()

                    //过滤掉链接类型
                    mTabList.filter { it -> !it.isH5Url() }.forEachIndexed { index, tabBean ->
                        mFragments.add(HomeTabCommonFragment().apply {
                            arguments = Bundle().apply {
                                putString(HomeTabCommonFragment.ARGUMENTS_HOME_TAB_ID, tabBean.tabId
                                        ?: "0")
                                putString(HomeTabCommonFragment.ARGUMENTS_HOME_TAB_TYPE, tabBean.tabType
                                        ?: "0")
                                putString(HomeTabCommonFragment.ARGUMENTS_HOME_TAB_NAME, tabBean.tabName?:"")
                                putBoolean(HomeTabCommonFragment.ARGUMENTS_HOME_IS_HOME, index == 0)
                                putSerializable(HomeTabCommonFragment.ARGUMENTS_HOME_TAB_BEAN, HomeTabBean(
                                    tabBean.tabName, tabBean.jumpLink, tabBean.sptype, tabBean.jgspid, tabBean.sid,
                                    tabBean.pageType, tabBean.pageId, tabBean.pageName,
                                    tabBean.componentPosition, tabBean.componentName, tabBean.componentTitle
                                ))
                            }
                            setOnSearchDataListener(object: HomeTabCommonFragment.IIndexSearchListener {
                                override fun onIndexSearch(indexSearch: NewSearchBox?) {
                                    context?.let { it1 ->
                                        onComponentExposure(it1, indexSearch?.trackData, 0){
                                            SpmLogUtil.print("首页-组件-搜索曝光")
                                        }
                                    }
                                    this@HomeSteadyLayoutFragmentV3.search_view?.trackData = indexSearch?.trackData
                                }
                            })
                        })
                    }

                    if (tabList.isNotEmpty()) {
                        initTabLayout()
                    } else {
                        // TODO: 李江 空数据站位图
                    }
                } ?: kotlin.run {
                    // TODO: 李江 空数据站位图
                }
            }
        }

        mViewModel.showBigWheelImgLiveData.observe(this) {
            if (ivDialSuspensionNeedShow && !closeIvDialSuspension) {
                showBigWheelInOutAnimator(cl_dial_suspension, it)
                cl_dial_suspension.isVisible = true
            } else {
                cl_dial_suspension.isVisible = false
            }
        }

        LiveEventBus.get(LiveEventBusManager.HomeBus.BUS_SHOW_BIG_WHEEL, Boolean::class.java).observe(this) {
            mViewModel.showBigWheelImgLiveData.value = it
        }

    }

    private fun getSearchJgspid():String{
        try {
            val data = mTabList[curTabPosition]

            return splitJointJgspid(
                    a_page_type = data.pageType?.trim()?.replace("_","")?:"",
                    b_page_id = data.pageId?.trim()?.replace("_","")?:"",
                    c_page_name = data.pageName?.trim()?.replace("_","")?:"",
                    g_component_position = data.componentPosition?.trim()?.replace("_","")?:"",
                    h_component_name = data.componentName?.trim()?.replace("_","")?:"",
                    l_sub_module_type = "searchBox"
            )
        }catch (e:Exception){
            e.printStackTrace()
            return ""
        }
    }

    override fun onVisibleChanged(isVisible: Boolean) {
        super.onVisibleChanged(isVisible)

        if (isVisible){
            val properties: HashMap<String, Any> = HashMap()
            properties[JGTrackManager.FIELD.FIELD_PAGE_ID] = JGTrackManager.TrackHomePage.PAGE_ID
            properties[JGTrackManager.FIELD.FIELD_TITLE] = JGTrackManager.TrackHomePage.TITLE
            properties[JGTrackManager.FIELD.FIELD_URL] = this.getFullClassName()
            JGTrackManager.pageViewTrack(BaseYBMApp.getAppContext(), JGTrackManager.TrackHomePage.TITLE, properties)
        }
        mIsVisible = isVisible
    }

    private fun showBigWheelInOutAnimator(
            mView: View,
            isIn: Boolean
    ) {
        val animate = if (isIn) {
            TranslateAnimation((mView.width + 6.dp).toFloat(), 0f, 0f, 0f)
        } else {
            TranslateAnimation(0f, (mView.width + 6.dp).toFloat(), 0f, 0f)
        }
        animate.duration = 500L
        animate.setAnimationListener(object : Animation.AnimationListener {
            override fun onAnimationStart(animation: Animation) {}
            override fun onAnimationEnd(animation: Animation) {
                mView.isVisible = isIn
            }

            override fun onAnimationRepeat(animation: Animation) {}
        })
        mView.startAnimation(animate)

    }



    //CMS配置的搜索和tab跟随tab变动的一些UI样式 没有就默认样式
    private fun changeTabSearchTabUIState(tabBean: TabBean) {
        tabBean.bgImg?.let {//背景图
            requireActivity().glideLoadWithPlaceHolder(it, iv_head_bg, R.drawable.transparent, R.drawable.transparent)
//            view_tab_gradient.isVisible = it.isEmpty()
        } ?: kotlin.run {
            tabBean.bgRes?.let {
                try {
                    iv_head_bg.setBackgroundColor(Color.parseColor(it))
//                    view_tab_gradient.isVisible = it.isEmpty()
                } catch (e: Exception) {
                    e.printStackTrace()
                    requireActivity().glideLoadWithPlaceHolder("", iv_head_bg, R.drawable.transparent, R.drawable.transparent)
                }
            } ?: kotlin.run {
                requireActivity().glideLoadWithPlaceHolder("", iv_head_bg, R.drawable.transparent, R.drawable.transparent)
//                view_tab_gradient.isVisible = true
            }
        }

        tabBean.messageButtonUrl?.let {  //消息图
            var alpha = 1f
            search_view.setSearchMessageBg(it)
            tabBean.messageButtonTransparency?.let { transparency -> //设置透明度
                try {
                    alpha = transparency.toFloat() / 100
                } catch (e: Exception) {
                }
            }
            search_view.setSearchMessageAlpha(alpha)
        } ?: kotlin.run {
            search_view.setDefaultSearchMessageBg()
        }

        tabBean.searchBoxColor?.let {//搜索框的颜色
            try {
                var alpha = 1f
                tabBean.searchBoxTransparency?.let { transparency -> //设置透明度
                    alpha = transparency.toFloat() / 100
                }
                search_view.setSearchBg(Color.parseColor(it))
                search_view.setSearchBgAlpha(alpha)
            } catch (e: Exception) {
                search_view.setDefaultSearchBg()
            }
        } ?: kotlin.run {
            search_view.setDefaultSearchBg()
        }

        tabBean.messageSubColor?.let {//消息角标颜色
            try {
                var alpha = 1f
                search_view.setSearchMessageBubbleBg(Color.parseColor(it))
                tabBean.messageSubTransparency?.let { transparency -> //设置透明度
                    alpha = transparency.toFloat() / 100
                }
                search_view.setSearchMessageBubbleBgAlpha(alpha)
            } catch (e: Exception) {
                search_view.setDefaultSearchMessageBubbleBg()
            }
        } ?: kotlin.run {
            search_view.setDefaultSearchMessageBubbleBg()
        }

        tabBean.searchImageUrl?.let {//搜索图标
            search_view.setSearchIcon(it)
        } ?: kotlin.run {
            search_view.setDefaultSearchIcon()
        }

        tabBean.scanColor?.let { color ->
            tabBean.scanTransparency?.let { transparency ->
                search_view.setSearchContentList(mSearchContentList, color, transparency)
            } ?: kotlin.run {
                search_view.setSearchContentList(mSearchContentList, color)
            }
        } ?: kotlin.run {
            search_view.setSearchContentList(mSearchContentList, "")
        }

    }

    /**
     * 网络请求失败
     */
//    private fun errorNetHandle() {
//        try {
//            ll_home_error_net.visibility = View.VISIBLE
//            tv_home_error_net_btn.setOnClickListener {
//                showProgress()
//                getHeaderData()
//            }
//            ll_home_error_net.setOnClickListener{}
//        } catch(e: Exception) {
//            e.printStackTrace()
//        }
//
//    }
    /**
     * 初始化广播
     */
    private fun initBroadCastReceiver() {
        br = object : BroadcastReceiver() {
            override fun onReceive(context: Context, intent: Intent) {
                if (IntentCanst.REFRESH_PAGE == intent.action) {
                    refreshData()
                    toTop()
                } else if (IntentCanst.ACTION_SWITCH_USER == intent.action) {
                    mIsTabSelected = false
                    refreshData()
                    toTop()
                }
//                else if (IntentCanst.ACTION_AD_COLLECT_POP == intent.action) run {
//                    iv_ad_suspension.visibility = View.VISIBLE
//                } else if (IntentCanst.ACTION_AD_COLLECT_HINT_POP == intent.action) run {
//                    iv_ad_suspension.visibility = View.GONE
//                }
                else if (IntentCanst.ACTION_CHANGE_HOME_LAYOUT_TYPE == intent.action) {
                    initMessageCountForBubble(search_view?.getBubbleView())
                } else if (IntentCanst.ACTION_LOGOUT == intent.action) {
                    // 退出登录时候
                } else if (IntentCanst.REFRESH_HOT_SEARCH == intent.action) {
                    //下拉刷新热搜接口
                    initSearchContent()
                }
            }
        }
        val intentFilter = IntentFilter(IntentCanst.MSG_NUM_EDIT)
        intentFilter.addAction(IntentCanst.REFRESH_PAGE)
        intentFilter.addAction(IntentCanst.REFRESH_HOT_SEARCH)
        intentFilter.addAction(IntentCanst.ACTION_LOGOUT)
        intentFilter.addAction(IntentCanst.ACTION_SWITCH_USER)
        intentFilter.addAction(IntentCanst.ACTION_AD_COLLECT_POP)
        intentFilter.addAction(IntentCanst.ACTION_AD_COLLECT_HINT_POP)
        intentFilter.addAction(IntentCanst.ACTION_CHANGE_HOME_LAYOUT_TYPE)
        LocalBroadcastManager.getInstance(notNullActivity).registerReceiver(br as BroadcastReceiver, intentFilter)
    }

    override fun toTop() {
        super.toTop()
        val newPosition = transformPosition(tab_layout.selectedTabPosition)
        if (newPosition>=0 && newPosition<mFragments.size){
            mFragments[newPosition].toTop()
        }
    }

    /**
     * 转换下标
     * 因为tabList中存在url的链接tab占了下标 所以要转换一下  要过滤掉再对应
     * @param tabSelectPosition Int
     * @return Int
     */
    private fun transformPosition(tabSelectPosition:Int):Int{
        var newPosition = -1
        if (tabSelectPosition>=0 && tabSelectPosition<mTabList.size){
            val tab = mTabList[tabSelectPosition]
             mTabList.filter { it -> !it.isH5Url() }.forEachIndexed { index, tabBean ->
                if (tab == tabBean){
                    newPosition = index
                    return@forEachIndexed
                }
            }
        }
        return newPosition
    }


    override fun onDestroyView() {
        super.onDestroyView()
        br?.let {
            LocalBroadcastManager.getInstance(notNullActivity).unregisterReceiver(it)
        }
    }

    override fun onLicenseStatusEnable(): Boolean = true

    override fun handleLicenseStatusChange(status: Int) {
        mViewModel.getHomeAptitudeStatus()
    }

    override fun reComponentExposure() {

    }


    /**
     * 处理一审悬浮弹窗是否显示
     * @param isShow 是否显示
     */
    fun handleAuditPassedFloatView(isShow: Boolean, msg: String?, type: Int, status: Int) {
        if (auditStatusHomeFloatManager == null) auditStatusHomeFloatManager = AuditStatusHomeFloatManager()
        val vg = loadingPage.findViewById<ViewGroup>(R.id.fl_home_steady)
        if (isShow) {
            context?.let { it ->
                val btnText = if(type == 1) "资质认证" else "去更新"
                auditStatusHomeFloatManager?.addParentLayout(it, vg, msg, btnText)
                    ?.setOnAuditStatusFloatVieListener(object: AuditStatusHomeFloatManager.AuditStatusFloatViewListener{
                        override fun callback() {
                            (notNullActivity as BaseActivity).gotoAtivity(AptitudeActivity::class.java, null)
                            getLicenseType(status)?.let { typeStatus ->
                                XyyIoUtil.track("Homepage_Bottom_Qualification_Popup_Click", hashMapOf(
                                    "type" to typeStatus
                                ))
                            }
                        }
                    })

                getLicenseType(status)?.let { typeStatus ->
                    XyyIoUtil.track("Homepage_Bottom_Qualification_Popup_Exposure", hashMapOf(
                        "type" to typeStatus
                    ))
                }
            }
        } else {
            auditStatusHomeFloatManager?.hiddenFloatView()
        }
    }

    private fun getLicenseType(status: Int): String? {
        //1.临期&过期 2.仅过期 3.仅临期
        return when (status) {
            1 -> "expire_and_advent"
            2 -> "expire"
            3 -> "advent"
            else -> null
        }
    }


    private fun getJgPageId() {

    }

    override fun onClick(v: View?) {
        v ?: return
        when (v.id) {
            R.id.ll_all_drug, R.id.iv_icon_all_drug -> { //全部药品
                AllDrugActivity.launchActivity(requireActivity())
                val map = HashMap<String, Any>().apply {
                    put(TrackManager.FIELD_PAGE_ID, getPageId())
                }
                TrackManager.clickEventTrack(TrackManager.TrackHome.EVENT_ACTION_TAB_CLASSIFICATION_CLICK, map)
                context?.let { onSubcomponentALLDrugClick(it, allDrugTrackData) }
            }

            R.id.iv_close -> {  //关闭大转盘
                closeIvDialSuspension = true
                cl_dial_suspension.visibility = View.GONE

                val map = HashMap<String, Any>().apply {
                    put(TrackManager.FIELD_PAGE_ID, getPageId())
                }
                TrackManager.clickEventTrack(TrackManager.TrackHome.EVENT_ACTION_FLOAT_OFF_CLICK, map)
            }
        }

    }


    //大费周章 反射就是为了 不让连接跳转类型的tab被选择 但是触发点击事件
    private fun newInterceptClickTab(): InterceptClickTab {
        val tab = InterceptClickTab()
        tab.parent = tab_layout
        tab.view = createInterceptClickTabView(tab)
        if (tab.id != View.NO_ID) {
            tab.view.id = tab.id
        }
        return tab
    }

    private fun createInterceptClickTabView(tab: InterceptClickTab): TabLayout.TabView {
        val createTabViewMethod = TabLayout::class.java.getDeclaredMethod("createTabView", TabLayout.Tab::class.java)
        createTabViewMethod.isAccessible = true

        // 通过反射调用 createTabView 方法
        return createTabViewMethod.invoke(tab_layout, tab) as TabLayout.TabView
    }

    override fun onHandleFragmentPv() = try{
        mFragments[transformPosition(tab_layout.selectedTabPosition)].onHandleFragmentPv()
    } catch (e: Exception) {
        e.printStackTrace()
    }
}