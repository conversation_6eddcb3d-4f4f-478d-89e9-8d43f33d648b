package com.ybmmarket20.home

import android.view.View
import android.widget.RadioButton
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.viewModels
import com.ybmmarket20.R
import com.ybmmarket20.common.BaseFragment2
import com.ybmmarket20.common.JGTrackManager
import com.ybmmarket20.common.JGTrackManager.Companion.pageViewTrack
import com.ybmmarket20.common.jgTrackOftenBuyBtnClick
import com.ybmmarket20.viewmodel.OftenBuyNotifyViewModel
import com.ybmmarket20.viewmodel.OftenBuyViewModel
import com.ybmmarketkotlin.utils.RouterJump
import kotlinx.android.synthetic.main.fragment_often_buy.cbOftenBuy
import kotlinx.android.synthetic.main.fragment_often_buy.ivBack
import kotlinx.android.synthetic.main.fragment_often_buy.ivCart
import kotlinx.android.synthetic.main.fragment_often_buy.rgFilter
import kotlinx.android.synthetic.main.fragment_often_buy.tvCartNum
import kotlinx.android.synthetic.main.fragment_often_buy.tv_title

/**
 * <AUTHOR>
 * @date 2022/11/21
 * @description 常购清单
 */
class OftenBuyFragment : BaseFragment2() {

    private val oftenBuyViewModel: OftenBuyViewModel by viewModels()
    var mThirtyDays = "0"
    var mOrder = "1"
    var mOrderText = "最常购买"
    val fm: FragmentManager? by lazy { activity?.supportFragmentManager }
    var curFragment: Fragment? = null
    val notifyViewModel: OftenBuyNotifyViewModel by activityViewModels()

    override fun initData(content: String?) {
        tv_title.text = "常购清单"
        initView()
        initObserver()
    }

    override fun getLayoutId(): Int = R.layout.fragment_often_buy

    private fun initView() {
        //设置购物车商品数
        setCartNum()
        rgFilter.setOnCheckedChangeListener { _, checkedId ->
            showProgress()
            mThirtyDays = if (cbOftenBuy.isChecked) "1" else "0"
            mOrder = if (checkedId == R.id.rbFilterOftenBuy) "1" else "2"
            mOrderText = if (checkedId == R.id.rbFilterOftenBuy) "最常购买" else "最近购买"
            switchFilter()
            this.context?.jgTrackOftenBuyBtnClick(if (mOrder == "1") "最常购买" else "最近购买", "功能")
        }
        cbOftenBuy.setOnCheckedChangeListener { _, isChecked ->
            showProgress()
            mThirtyDays = if (isChecked) "1" else "0"
            mOrder = if ((rgFilter.getChildAt(0) as RadioButton).isChecked) "1" else "2"
            mOrderText = if ((rgFilter.getChildAt(0) as RadioButton).isChecked) "最常购买" else "最近购买"
            switchFilter()
            this.context?.jgTrackOftenBuyBtnClick("仅查看近30天内买过", "功能")
        }
        ivBack.setOnClickListener { activity?.finish()}
        ivCart.setOnClickListener {
            RouterJump.jump2ShopCar()
            this.context?.jgTrackOftenBuyBtnClick("购物车", "功能")
        }
        switchFilter()
    }


    private fun switchFilter() {
        val transaction = fm?.beginTransaction()
        curFragment = OftenBuyFilterFragment(mThirtyDays, mOrder,mOrderText)
        curFragment?.let { transaction?.add(R.id.flFragment, it) }
        transaction?.commit()
    }

    private fun setCartNum() {
        val cartNum = oftenBuyViewModel.getCartNumber()
        tvCartNum.visibility = if (cartNum.isNullOrEmpty()) View.GONE else View.VISIBLE
        tvCartNum.text = cartNum
    }

    private fun initObserver() {
        notifyViewModel.oftenBuyLiveData.observe(this) {
            fm?.fragments?.forEach {
                if (it != curFragment && it is OftenBuyFilterFragment) {
                    val transaction = fm?.beginTransaction()
                    transaction?.remove(it)
                    transaction?.commit()
                }
            }
        }
    }
}