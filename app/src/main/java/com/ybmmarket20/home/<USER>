package com.ybmmarket20.home

import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import android.view.View
import android.view.animation.Animation
import android.view.animation.Animation.AnimationListener
import android.view.animation.TranslateAnimation
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarketkotlin.adapter.YBMBaseMultiItemAdapter
import com.ybm.app.common.BaseYBMApp
import com.ybmmarket20.R
import com.ybmmarket20.bean.DiscountItemBean
import com.ybmmarket20.bean.ExpandableItem
import com.ybmmarket20.bean.cart.CartDiscountDataBean
import com.ybmmarket20.common.YBMAppLike

/**
 * <AUTHOR> Brin
 * @date : 2020/11/18 - 15:44
 * @Description :
 * @version
 */
class CartDiscountUtils(discountView: View, boomView: View, tvCartDiscountShowDetail: TextView, discountData: CartDiscountDataBean?) {

    val flCartDiscount: View?
    val boomView: View?
    val tvCartDiscountShowDetail: TextView?
    val llDiscountDialog: LinearLayout?
    val ivClose: ImageView?
    val rvDiscount: RecyclerView?
    lateinit var mAdapter: DiscountCartAdapter

    init {
        this.flCartDiscount = discountView
        this.boomView = boomView
        this.tvCartDiscountShowDetail = tvCartDiscountShowDetail
        llDiscountDialog = discountView.findViewById(R.id.ll_discount_dialog)

        ivClose = discountView.findViewById(R.id.iv_close)
        ivClose?.setOnClickListener { dismissDiscountView() }
        rvDiscount = discountView.findViewById(R.id.rv_discount)
        updateData(discountData)
    }


    fun updateData(discountData: CartDiscountDataBean?) {
        var discountListData = arrayListOf<DiscountItemBean>()
        discountData?.promoDiscountGroupList?.forEach {
            discountListData.add(DiscountItemBean(title = it?.name, discountPrice = it?.discountAmount).apply { isGroup = true })
            it?.promoDiscountDetailList?.forEach {
                discountListData.add(DiscountItemBean(typeTitle = it?.name, discountPrice = it?.discountAmount))
            }
        }

        mAdapter = DiscountCartAdapter(discountListData)
        val headerView = View.inflate(BaseYBMApp.getApp().currActivity, R.layout.show_dicount_pop_header, null)
        headerView.findViewById<TextView>(R.id.tv_content).visibility = View.GONE
        headerView.findViewById<TextView>(R.id.tv_group_title).text = "商品总额"
        headerView.findViewById<TextView>(R.id.tv_product_price).setText("¥${discountData?.subTotal}")

        val footerView = View.inflate(BaseYBMApp.getApp().currActivity, R.layout.show_dicount_cart_pop_footer, null)
        footerView.findViewById<TextView>(R.id.tv_total_discount_price_title).setText("共优惠")
        footerView.findViewById<TextView>(R.id.tv_total_discount_price).setText("-¥${discountData?.totalDiscount}")
        mAdapter.addHeaderView(headerView)
        mAdapter.addFooterView(footerView)
        rvDiscount?.setLayoutManager(LinearLayoutManager(BaseYBMApp.getApp().currActivity))
        rvDiscount?.setAdapter(mAdapter)
    }

    fun dispatchDisCountAnim() {
        if (flCartDiscount?.visibility == View.GONE) {
            showDiscountView()
        } else {
            dismissDiscountView()
        }
    }

    fun showDiscountView() {
        tvCartDiscountShowDetail?.setCompoundDrawablesWithIntrinsicBounds(null, null, YBMAppLike.getApp().currActivity.getDrawable(R.drawable.icon_arrow_down), null)
        flCartDiscount?.setBackgroundResource(R.color.transparent)
        flCartDiscount?.setVisibility(View.VISIBLE)
        val popAnim: Animation = TranslateAnimation(0f, 0f, boomView?.getTop()?.toFloat() ?: 0f, 0f)
        popAnim.duration = 250
        popAnim.fillAfter = true
        popAnim.isFillEnabled = true
        llDiscountDialog?.setAnimation(popAnim)
        popAnim.setAnimationListener(object : AnimationListener {
            override fun onAnimationStart(animation: Animation) {
                flCartDiscount?.setBackgroundResource(R.color.transparent)
            }

            override fun onAnimationEnd(animation: Animation) {
                flCartDiscount?.setBackgroundResource(R.color.translucent_black_50)
            }

            override fun onAnimationRepeat(animation: Animation) {}
        })
    }

    fun dismissDiscountView() {
        flCartDiscount?.setBackgroundResource(R.color.transparent)
        tvCartDiscountShowDetail?.setCompoundDrawablesWithIntrinsicBounds(null, null, YBMAppLike.getApp().currActivity.getDrawable(R.drawable.icon_arrow_up), null)
        val exitAnim: Animation = TranslateAnimation(0f, 0f, 0f, boomView?.getTop()?.toFloat()
                ?: 0f)
        exitAnim.duration = 250
        exitAnim.fillAfter = true
        exitAnim.isFillEnabled = true
        llDiscountDialog?.animation = exitAnim
        exitAnim.setAnimationListener(object : AnimationListener {
            override fun onAnimationStart(animation: Animation) {}
            override fun onAnimationEnd(animation: Animation) {
                flCartDiscount?.visibility = View.GONE
            }

            override fun onAnimationRepeat(animation: Animation) {}
        })
    }

    inner class DiscountCartAdapter :
        YBMBaseMultiItemAdapter<DiscountItemBean> {

        constructor(data: List<DiscountItemBean>) : super(data) {
            addItemType(ExpandableItem.TYPE_GROUP, R.layout.item_discount_group)
            addItemType(ExpandableItem.TYPE_CONTENT, R.layout.item_discount_child)
        }

        override fun bindItemView(baseViewHolder: YBMBaseHolder, t: DiscountItemBean) {
            when (t.itemType) {
                ExpandableItem.TYPE_GROUP -> binGroupItemView(baseViewHolder, t)
                ExpandableItem.TYPE_CONTENT -> binChildItemView(baseViewHolder, t)
            }
        }

        private fun binChildItemView(ybmBaseHolder: YBMBaseHolder, bean: DiscountItemBean) {
            ybmBaseHolder.setGone(R.id.tv_coupon_used_status, false)
            ybmBaseHolder.setText(R.id.tv_child_title, bean.typeTitle)
            ybmBaseHolder.setText(R.id.tv_child_price, "-¥${bean.discountPrice}")

        }

        private fun binGroupItemView(ybmBaseHolder: YBMBaseHolder, bean: DiscountItemBean) {
            ybmBaseHolder.setText(R.id.tv_group_title, bean.title)
            ybmBaseHolder.setText(R.id.tv_group_price, "-¥${bean.discountPrice}")
        }
    }
}