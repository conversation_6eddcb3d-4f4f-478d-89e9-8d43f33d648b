package com.ybmmarket20.home

import KEY_SHOP_EXPAND_STATUS
import android.content.Context
import android.graphics.Color
import android.os.CountDownTimer
import android.os.SystemClock
import android.text.InputType
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.TextUtils
import android.text.style.AbsoluteSizeSpan
import android.text.style.ForegroundColorSpan
import android.util.SparseArray
import android.view.View
import android.view.inputmethod.InputMethodManager
import android.widget.Button
import android.widget.CheckBox
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import com.chad.library.adapter.base.BaseViewHolder
import com.chad.library.adapter.base.entity.MultiItemEntity
import com.google.gson.Gson
import com.tencent.mmkv.MMKV
import com.xyy.canary.utils.LogUtil
import com.ybmmarket20.R
import com.ybmmarket20.bean.TagBean
import com.ybmmarket20.bean.cart.CartItemBean
import com.ybmmarket20.bean.cart.Level0ItemInvalidBean
import com.ybmmarket20.bean.cart.Level0ItemShopFooterBean
import com.ybmmarket20.bean.cart.Level0ItemShopHeaderBean
import com.ybmmarket20.bean.cart.Level1InvalidGoodBean
import com.ybmmarket20.bean.cart.Level1InvalidGroupGoodBean
import com.ybmmarket20.bean.cart.Level1InvalidGroupHeaderBean
import com.ybmmarket20.bean.cart.Level1InvalidItemGoodsBeanAbs
import com.ybmmarket20.bean.cart.Level1ItemActivityGiftSelectBean
import com.ybmmarket20.bean.cart.Level1ItemActivityGoodBean
import com.ybmmarket20.bean.cart.Level1ItemActivityGoodGiftBean
import com.ybmmarket20.bean.cart.Level1ItemActivityHeaderBean
import com.ybmmarket20.bean.cart.Level1ItemCommonGoodsBean
import com.ybmmarket20.bean.cart.Level1ItemGoodsBeanAbs
import com.ybmmarket20.bean.cart.Level1ItemGroupFooterBean
import com.ybmmarket20.bean.cart.Level1ItemGroupGoodBean
import com.ybmmarket20.bean.cart.Level1ItemGroupHeaderBean
import com.ybmmarket20.bean.cart.Level1ItemSubShopHeaderBean
import com.ybmmarket20.bean.cart.Level1ItemWrapper
import com.ybmmarket20.bean.isStep
import com.ybmmarket20.common.AlertDialogEx
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.common.JGTrackManager
import com.ybmmarket20.common.JGTrackManager.Companion.eventTrack
import com.ybmmarket20.common.JgTrackBean
import com.ybmmarket20.common.dp
import com.ybmmarket20.common.splicingUrlWithParams
import com.ybmmarket20.common.util.ConvertUtils
import com.ybmmarket20.constant.IntentCanst
import com.ybmmarket20.utils.ClickDelayUtil
import com.ybmmarket20.utils.DialogUtil
import com.ybmmarket20.utils.DialogUtil.DialogClickListener
import com.ybmmarket20.utils.ImageUtil
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.SpUtil
import com.ybmmarket20.utils.UiUtils
import com.ybmmarket20.utils.analysis.AnalysisConst
import com.ybmmarket20.utils.analysis.XyyIoUtil
import com.ybmmarket20.utils.analysis.addAnalysisRequestParams
import com.ybmmarket20.view.FreightTipDialog
import com.ybmmarket20.view.SwipeMenuLayout
import com.ybmmarket20.view.TagView
import com.ybmmarket20.xyyreport.page.cart.CartReport
import com.ybmmarketkotlin.utils.TextWithPrefixTag
import com.ybmmarketkotlin.utils.TimeUtils
import com.ybmmarketkotlin.utils.addMiddleline
import com.ybmmarketkotlin.utils.textWithSuffixTag

class CartItemAdapter3 : CartItemAnalysisAdapter3, CartIntemAdapterInterface {

    companion object {
        // 1. 构造一级店铺头部数据
        // 2.构造一级店铺尾部数据
        // 3.构造一级失效头部数据

        // 1.1 构造二级 自营店铺商品数据

        // 1.2 构造二级 活动头部
        // 1.3 构造二级 活动商品头部
        // 1.4 构造二级 活动商品中间
        // 1.5 构造二级 活动商品尾部

        // 1.6 构造二级 套餐商品头部
        // 1.7 构造二级 套餐商品
        // 1.8 构造二级 套餐尾部

        const val level0_shop_header = 0x0000_0001
        const val level0_shop_footer = 0x0000_0002
        const val level0_shop_Invalid = 0x0000_0003

        const val level1_sub_shop_header = 0x0000_0100
        const val level1_common_good = 0x0000_0101

        const val level1_activity_header = 0x0000_0110
        const val level1_activity_good_header = 0x0000_0111
        const val level1_activity_good = 0x0000_0112
        const val level1_activity_good_end = 0x0000_0113

        const val level1_group_header = 0x0000_0120
        const val level1_group_good = 0x0000_0121
        const val level1_group_goods_gift = 0x0000_0123 //赠品
        const val level1_group_footer = 0x0000_0122

        const val level1_Invalid_good = 0x0000_0131
        const val level1_Invalid_group_header = 0x0000_0132
        const val level1_Invalid_group_good = 0x0000_0133

        //选择赠品、已选赠品、放弃赠品  按钮的样式
        const val level1_activity_good_gift_select = 0x0000_0199

        private const val TRACK_DURATION = 2 * 60 * 1000 //2分钟内不上报
    }

    var cachedShopExpandStatus: HashMap<String?, Boolean> = hashMapOf()
    var cartFragmentV3: CartFragmentV3? = null
    val mCartAnalysis: CartAnalysis by lazy { CartAnalysis() }
    private val traceProductData = mutableListOf<String>()
    private var traceProductGiftData = mutableListOf<String>()

    private val countDownTimerMap: SparseArray<CountDownTimer> = SparseArray()

    var resourceClickTrackListener: ((productId: String, productName: String, productPrice: Double, position: Int) -> Unit)? =
        null

    //曝光事件回调
    var resourceViewTrackListener: ((productId: String, productName: String, productPrice: Double, position: Int) -> Unit)? =
        null

    // key: 商品Id     value:当时埋点的时间戳
    private val productViewTrackMap = hashMapOf<String, Long>()

    var jgTrackBean: JgTrackBean? = null

    var mGiftSelectClickListener: ((Level1ItemActivityGiftSelectBean) -> Unit)? = null

    //限时补价倒计时
    val limitTimePremiumTimerMap: SparseArray<CountDownTimer> = SparseArray()

    constructor(data: MutableList<MultiItemEntity>?, cartFragmentV3: CartFragmentV3) : super(data) {

        this.cartFragmentV3 = cartFragmentV3

        addItemType(level0_shop_header, R.layout.item_cart_shop_header)
        addItemType(level0_shop_footer, R.layout.item_cart_shop_end)
        addItemType(level0_shop_Invalid, R.layout.item_cart_shop_invalid)

        addItemType(level1_sub_shop_header, R.layout.item_cart_sub_shop_header)
        addItemType(level1_common_good, R.layout.item_cart_common_good)

        addItemType(level1_activity_header, R.layout.item_cart_activity_header)
        addItemType(level1_activity_good_header, R.layout.item_cart_common_good)
        addItemType(level1_activity_good_gift_select, R.layout.item_cart_activity_good_gift_select)
        addItemType(level1_activity_good, R.layout.item_cart_common_good)
        addItemType(level1_activity_good_end, R.layout.item_cart_common_good)

        addItemType(level1_group_header, R.layout.item_cart_group_header)
        addItemType(level1_group_good, R.layout.item_cart_common_good)
        addItemType(level1_group_goods_gift, R.layout.item_cart_goods_gift)
        addItemType(level1_group_footer, R.layout.item_cart_group_footer)

        addItemType(level1_Invalid_good, R.layout.item_cart_invalid_good)
        addItemType(level1_Invalid_group_header, R.layout.item_cart_group_header)
        addItemType(level1_Invalid_group_good, R.layout.item_cart_invalid_good)
    }

    override fun setNewData(data: MutableList<MultiItemEntity>?) {
        super.setNewData(data)
        traceProductData.clear()
        traceProductGiftData.clear()
    }

    fun getTraceKey(shopcode: String?, merchantId: String?, activityId: String? = null): String {
        return "${shopcode}_${merchantId}_${activityId}"
    }

    override fun convert(holder: BaseViewHolder, item: MultiItemEntity?) {
        when (item?.itemType) {
            level0_shop_header -> convertShopHeader(holder, item)
            level0_shop_footer -> convertShopEnd(holder, item)
            level0_shop_Invalid -> convertInvalidHeader(holder, item)

            level1_sub_shop_header -> convertSubShopHeader(holder, item)
            level1_activity_header -> convertActivityHeader(holder, item)

            level1_common_good,
            level1_activity_good_header,
            level1_activity_good,
            level1_activity_good_end,
            level1_group_good -> {
                super.convert(holder, item)
                convertCommonGoodsContent(holder, item)
            }
            
            level1_group_header -> convertgroupHeader(holder, item)
            level1_group_footer -> convertgroupFooter(holder, item)

            level1_Invalid_group_header -> convertInvalidGroupHeader(holder, item)
            level1_Invalid_group_good,
            level1_Invalid_good -> convertInvalidGoodsContent(holder, item)

            level1_group_goods_gift -> convertGoodsGift(holder, item)

            level1_activity_good_gift_select -> convertGoodsGiftSelect(holder, item)
        }
    }

    //赠品选择的样式
    private fun convertGoodsGiftSelect(holder: BaseViewHolder, item: MultiItemEntity) {
        val bean = item as Level1ItemActivityGiftSelectBean
        val tvTitle = holder.getView<TextView>(R.id.tv_title)
        val clRoot = holder.getView<ConstraintLayout>(R.id.cl_root)
        tvTitle.text = bean.content

        if (bean.isSelected()) {
            clRoot.layoutParams = clRoot.layoutParams.apply {
                this as ConstraintLayout.LayoutParams
                setMargins(0, 0, 0, 0)
            }
        } else {
            clRoot.layoutParams = clRoot.layoutParams.apply {
                this as ConstraintLayout.LayoutParams
                setMargins(0, 0, 0, 12.dp)
            }
        }

        holder.itemView.setOnClickListener {
            mGiftSelectClickListener?.invoke(bean)
        }
    }
    
    // 赠品
    private fun convertGoodsGift(holder: BaseViewHolder, item: MultiItemEntity) {
        val bean = item as Level1ItemActivityGoodGiftBean
        val iv = holder.getView<ImageView>(R.id.iv_cart_goods_gift)
        val title = holder.getView<TextView>(R.id.tv_goods_gift_title)
        val effect = holder.getView<TextView>(R.id.tv_goods_gift_effect)
        val tagView = holder.getView<TagView>(R.id.tv_goods_gift_type_tag)
        val count = holder.getView<TextView>(R.id.tv_goods_gift_count)
        bean.imageUrl.let { ImageUtil.load(holder.itemView.context, it, iv) }
        title.text = bean.name
        effect.text = "效期：${bean.effect}"
        if (bean.tagList?.isNullOrEmpty() == false) {
            tagView.bindData(bean.tagList)
        } else {
            tagView.visibility = View.GONE
        }
        count.text = "X${bean.amount}"
        holder.itemView.setOnClickListener {
            var mUrl = "ybmpage://productdetail/${bean.skuid}"
            mUrl = splicingUrlWithParams(mUrl, hashMapOf(
                    Pair<String,Any>(IntentCanst.JG_ENTRANCE,jgTrackBean?.entrance?:"购物车"),
                    Pair<String,Any>(IntentCanst.JG_REFERRER,JGTrackManager.TrackShoppingCart.TRACK_URL),
                    Pair<String,Any>(IntentCanst.JG_REFERRER_TITLE,JGTrackManager.TrackShoppingCart.TITLE),
                    Pair<String,Any>(IntentCanst.JG_REFERRER_MODULE,"${JGTrackManager.TrackShoppingCart.TITLE}-${JGTrackManager.Common.MODULE_PRODUCT_LIST}"),
            ))
            RoutersUtils.open(mUrl)
            XyyIoUtil.track("page_shoppingTrolley_giftCard_Click", hashMapOf(
                "productId" to bean.skuid
            ))
        }
        if (!traceProductGiftData.contains("${bean.activityId}${bean.skuid}")) {
            XyyIoUtil.track("page_shoppingTrolley_giftCard_Exposure", hashMapOf(
                "productId" to bean.skuid
            ))
            bean.skuid?.let { traceProductGiftData.add("${bean.activityId?: ""}$it") }
        }

        val builder = SpannableStringBuilder("${bean.price}")
        builder.setSpan(ForegroundColorSpan(UiUtils.getColor(R.color.color_ff2121)), 0, builder.length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        holder.getView<TextView>(R.id.tvZengPinPrice).text = builder
        val ivZengPinTip = holder.getView<ImageView>(R.id.ivZengPinTip)
        if (bean.priceDesc.isNullOrEmpty()) {
            ivZengPinTip.visibility = View.GONE
        } else {
            ivZengPinTip.visibility = View.VISIBLE
            ivZengPinTip.setOnClickListener {
                val alert = AlertDialogEx(mContext)
                alert.setTitle("价格说明")
                    .setMessage(bean.priceDesc)
                    .setCancelButton("我知道了") { dialog: AlertDialogEx, button: Int -> dialog.dismiss() }
                alert.show()
            }
        }

    }

    /**
     * 失效商品
     * @param holder BaseViewHolder
     * @param item MultiItemEntity
     */
    private fun convertInvalidGoodsContent(holder: BaseViewHolder, item: MultiItemEntity) {

        val invalidGoodsBean = item as Level1InvalidItemGoodsBeanAbs
        val iv_good = holder.getView<ImageView>(R.id.iv_good)
        val iv_marker = holder.getView<ImageView>(R.id.iv_marker)
        val tv_goods_name = holder.getView<TextView>(R.id.tv_goods_name)
        val tv_goods_invalid_status = holder.getView<TextView>(R.id.tv_goods_invalid_status)
        val iv_goods_invalid_status_content =
            holder.getView<TextView>(R.id.iv_goods_invalid_status_content)
        val iv_good_num_in_group = holder.getView<TextView>(R.id.iv_good_num_in_group)
        val tv_find_similarity = holder.getView<TextView>(R.id.tv_find_similarity)
        val btnCollect = holder.getView<Button>(R.id.btnCollect)
        val btnDelete = holder.getView<Button>(R.id.btnDelete)
        val cart_swipe_layout = holder.getView<SwipeMenuLayout>(R.id.cart_swipe_layout)
        val cl_item = holder.getView<ConstraintLayout>(R.id.cl_item)
        val btnFindSameGoods = holder.getView<Button>(R.id.btn_find_same_goods)

        invalidGoodsBean?.apply {
            imageUrl?.let {
                ImageUtil.load(holder.itemView.context, it, iv_good)
            }
            iv_marker?.visibility = if (markerUrl.isNullOrEmpty()) View.GONE else View.VISIBLE
            markerUrl?.let { ImageUtil.load(holder.itemView.context, it, iv_marker) }
            tv_goods_name?.text = name
            tv_goods_invalid_status?.visibility = if (invalidStatus.isNullOrEmpty()) View.GONE else View.VISIBLE
            invalidStatus?.let {
                tv_goods_invalid_status?.text = invalidStatus
            }
            iv_goods_invalid_status_content?.text = invalidContent
            iv_good_num_in_group?.text = groupGoodsNum ?: ""
            tv_find_similarity?.setOnClickListener {
                val mEntrance = "购物车(失效品找相似)"
                var mUrl = "ybmpage://findsamegoods/${skuid}"
                mUrl = splicingUrlWithParams(
                    mUrl, hashMapOf(
                        Pair<String, Any>(IntentCanst.JG_ENTRANCE, mEntrance),
                        Pair<String, Any>(
                            IntentCanst.JG_REFERRER,
                            JGTrackManager.TrackShoppingCart.TRACK_URL
                        ),
                        Pair<String, Any>(
                            IntentCanst.JG_REFERRER_TITLE,
                            JGTrackManager.TrackShoppingCart.TITLE
                        ),
                        Pair<String, Any>(
                            IntentCanst.JG_REFERRER_MODULE,
                            "${JGTrackManager.TrackShoppingCart.TITLE}-${JGTrackManager.Common.MODULE_PRODUCT_LIST}"
                        ),
                    )
                )
                RoutersUtils.open(mUrl)
                XyyIoUtil.track(
                    "action_ShoppingCart_Similar",
                    hashMapOf("sku_id" to skuid, "located" to "2")
                )
            }
            cl_item.setOnClickListener {
                val mEntrance = "购物车(失效品找相似)"
                var mUrl =  "ybmpage://productdetail/${skuid}"
                mUrl = splicingUrlWithParams(mUrl, hashMapOf(
                        Pair<String,Any>(IntentCanst.JG_ENTRANCE,mEntrance),
                        Pair<String,Any>(IntentCanst.JG_REFERRER,JGTrackManager.TrackShoppingCart.TRACK_URL),
                        Pair<String,Any>(IntentCanst.JG_REFERRER_TITLE,JGTrackManager.TrackShoppingCart.TITLE),
                        Pair<String,Any>(IntentCanst.JG_REFERRER_MODULE,"${JGTrackManager.TrackShoppingCart.TITLE}-${JGTrackManager.Common.MODULE_PRODUCT_LIST}"),
                ))
                RoutersUtils.open(mUrl)
            }

            btnCollect?.setOnClickListener {
                //失效商品收藏点击
                cartFragmentV3?.showProgress()
                //且如果想让侧滑菜单同时关闭，需要同时调用quickClose();
                cart_swipe_layout.quickClose()
                cartFragmentV3?.mViewModel?.batchCollectWithGoodsInfo(skuid ?: "", data)
            }

            btnDelete?.setOnClickListener {
                cartFragmentV3?.showProgress()
                cart_swipe_layout.quickClose()
                // merchantId: String, packageIds: String, ids: String
                cartFragmentV3?.showDelDialog(mutableListOf(this))
//
//                cartFragmentV3?.mViewModel?.removeProductFromCart(SpUtil.getMerchantid(), packageIds = "", ids = skuid ?: "")
            }

            //找相似
            btnFindSameGoods.setOnClickListener {
                RoutersUtils.open("ybmpage://findsamegoods/${skuid}")
                cart_swipe_layout.quickClose()
                XyyIoUtil.track("action_ShoppingCart_Similar", hashMapOf("sku_id" to skuid, "located" to "2"))
            }
        }
        holder.itemView.setOnClickListener {
            var mUrl = "ybmpage://productdetail/${item.skuid}"
            mUrl = splicingUrlWithParams(mUrl, hashMapOf(
                    Pair<String,Any>(IntentCanst.JG_ENTRANCE,jgTrackBean?.entrance?:"购物车"),
                    Pair<String,Any>(IntentCanst.JG_REFERRER,JGTrackManager.TrackShoppingCart.TRACK_URL),
                    Pair<String,Any>(IntentCanst.JG_REFERRER_TITLE,JGTrackManager.TrackShoppingCart.TITLE),
                    Pair<String,Any>(IntentCanst.JG_REFERRER_MODULE,"${JGTrackManager.TrackShoppingCart.TITLE}-${JGTrackManager.Common.MODULE_PRODUCT_LIST}"),
            ))
            RoutersUtils.open(mUrl)
        }
    }


    private fun convertInvalidGroupHeader(holder: BaseViewHolder, item: MultiItemEntity) {
        val invalidGroupHeaderBean = item as Level1InvalidGroupHeaderBean

        val cb_group = holder.getView<TextView>(R.id.cb_group)
        cb_group?.visibility = View.GONE
        val tv_real_group_price = holder.getView<TextView>(R.id.tv_real_group_price)
        val tv_real_origin_price = holder.getView<TextView>(R.id.tv_real_origin_price)
        invalidGroupHeaderBean.apply {
            tv_real_group_price?.text = price
            tv_real_origin_price?.visibility = if (TextUtils.isEmpty(origPrice)) View.GONE else View.VISIBLE
            tv_real_origin_price?.takeIf { it.visibility == View.VISIBLE }?.apply {
                addMiddleline()
                text = origPrice
            }
        }
    }

    private fun convertgroupFooter(holder: BaseViewHolder, item: MultiItemEntity) {
        val itembean = item as Level1ItemGroupFooterBean

        val tv_group_total = holder.getView<TextView>(R.id.tv_group_total)
        val tv_number = holder.getView<TextView>(R.id.tv_number)
        val iv_numSub = holder.getView<ImageView>(R.id.iv_numSub)
        val iv_numAdd = holder.getView<ImageView>(R.id.iv_numAdd)
        itembean.apply {
            tv_group_total?.text = subtotal
            tv_number?.text = "${amount}"
            tv_number?.takeIf { it.visibility == View.VISIBLE }?.setOnClickListener {
                onEditNumClick(holder, itembean)
            }
        }

        iv_numSub?.setOnClickListener {
            subGoodsFromCart(holder.absoluteAdapterPosition, itembean, (itembean.amount ?: "0").toInt())
        }
        iv_numAdd.setOnClickListener {
            addGoodsToCart(holder.absoluteAdapterPosition, itembean, (itembean.amount ?: "0").toInt())
        }

    }

    private fun convertgroupHeader(holder: BaseViewHolder, item: MultiItemEntity) {
        val tv_real_group_price = holder.getView<TextView>(R.id.tv_real_group_price)
        val tv_real_origin_price = holder.getView<TextView>(R.id.tv_real_origin_price)
        val cb_group = holder.getView<CheckBox>(R.id.cb_group)

        val level1ItemGroupHeaderBean = item as Level1ItemGroupHeaderBean
        level1ItemGroupHeaderBean?.apply {
            tv_real_group_price.text = price
            tv_real_origin_price.text = origPrice
            tv_real_origin_price?.addMiddleline()
            cb_group.isChecked = selected
            cb_group.setOnClickListener { changeItemSelectStatus(!selected, packageId ?: "", true) }
        }
    }

    private fun convertCommonGoodsContent(holder: BaseViewHolder, item: MultiItemEntity?) {

        val baseGoodsBean = item as Level1ItemGoodsBeanAbs

        //<editor-fold desc=" 商品item的间距布局、左侧竖线、加购编辑显示状态 ">
        val divider_top = holder.getView<View>(R.id.divider_top)
        val line_top = holder.getView<TextView>(R.id.line_top)
        val line_bottom = holder.getView<TextView>(R.id.line_bottom)
        val layout_subtotal_and_edit = holder.getView<ConstraintLayout>(R.id.layout_subtotal_and_edit)


        when (baseGoodsBean?.itemType == level1_common_good && !baseGoodsBean.isPersonalShop) {
            true -> divider_top.visibility = View.VISIBLE
            false -> divider_top.visibility = View.GONE
        }

        layout_subtotal_and_edit.visibility = if (baseGoodsBean?.itemType == level1_group_good) View.GONE else View.VISIBLE

//        when (baseGoodsBean?.itemType) {
//            level1_common_good, level1_group_good -> {
//                line_top.visibility = View.GONE
//                line_bottom.visibility = View.GONE
//            }
//            level1_activity_good_header, level1_activity_good -> {
//                line_top.visibility = View.VISIBLE
//                line_bottom.visibility = View.VISIBLE
//            }
//            level1_activity_good_end -> {
//                line_top.visibility = View.VISIBLE
//                line_bottom.visibility = View.GONE
//            }
//        }
        if (baseGoodsBean is Level1ItemActivityGoodBean && baseGoodsBean.combinationType == 2) {
            // 带有赠品的主品
            line_bottom.visibility = View.GONE
        }
        //</editor-fold>

        val iv_good = holder.getView<ImageView>(R.id.iv_good)
        val iv_marker = holder.getView<ImageView>(R.id.iv_marker)
        val tv_goods_name = holder.getView<TextView>(R.id.tv_goods_name)
        val tv_effect = holder.getView<TextView>(R.id.tv_effect)
        val tv_price = holder.getView<TextView>(R.id.tv_price)
        val tv_discount_price = holder.getView<TextView>(R.id.tv_discount_price)
        val tv_group_total = holder.getView<TextView>(R.id.tv_group_total)
        val tv_number = holder.getView<TextView>(R.id.tv_number)
        val cb_good = holder.getView<CheckBox>(R.id.cb_good)
        val iv_numSub = holder.getView<ImageView>(R.id.iv_numSub)
        val iv_numAdd = holder.getView<ImageView>(R.id.iv_numAdd)
        val tv_activity_single = holder.getView<TextView>(R.id.tv_activity_single)
        val tv_limit = holder.getView<TextView>(R.id.tv_limit)
        val tv_limited = holder.getView<TextView>(R.id.tv_limited)
        val tv_cart_countdown = holder.getView<TextView>(R.id.tv_cart_countdown)
        val ll_time_out = holder.getView<LinearLayout>(R.id.ll_time_out)
        val tv_depreciate_tips = holder.getView<TagView>(R.id.tv_depreciate_tips)
        val rl_icon_type = holder.getView<TagView>(R.id.rl_icon_type)
        val tv_goods_status = holder.getView<TextView>(R.id.tv_goods_status)
        val iv_good_num_in_group = holder.getView<TextView>(R.id.iv_good_num_in_group)
        val btnCollect = holder.getView<Button>(R.id.btnCollect)
        val btnDelete = holder.getView<Button>(R.id.btnDelete)
        val btnFindSameGoods = holder.getView<Button>(R.id.btn_find_same_goods)
        val cart_swipe_layout = holder.getView<SwipeMenuLayout>(R.id.cart_swipe_layout)
        val cl_item = holder.getView<ConstraintLayout>(R.id.cl_item)
        val cl_new_tag = holder.getView<ConstraintLayout>(R.id.cl_new_tag)
        val tv_new_tag_1 = holder.getView<TextView>(R.id.tv_new_tag_1)
        val tv_new_tag_2 = holder.getView<TextView>(R.id.tv_new_tag_2)
        val clProductCoupons = holder.getView<ConstraintLayout>(R.id.cl_product_coupons)
        val tvProductCouponsContent = holder.getView<TextView>(R.id.tv_product_coupons_content)
        val llGoGatherOrders = holder.getView<LinearLayout>(R.id.ll_go_gather_orders)

        baseGoodsBean.apply {
            imageUrl.let { ImageUtil.load(holder.itemView.context, it, iv_good) }
            iv_marker?.visibility = if (markerUrl.isNullOrEmpty()) View.GONE else View.VISIBLE
            markerUrl?.let { ImageUtil.load(holder.itemView.context, it, iv_marker) }
            tv_goods_name?.text = name
            tv_goods_status?.visibility = if (invalidStatus.isNullOrEmpty()) View.GONE else View.VISIBLE
            invalidStatus?.let {
                tv_goods_status?.text = invalidStatus
            }
            tv_effect?.text = "效期: ${effect}"
            tv_price?.text = price
            if (TextUtils.isEmpty(actPurchaseTip)) {
                tv_limited?.visibility = View.GONE
            } else {
                tv_limited?.visibility = View.VISIBLE
                tv_limited?.text = actPurchaseTip
            }
            if (TextUtils.isEmpty(showPriceAfterDiscount)) {
                tv_discount_price.visibility = View.GONE
            } else {
                tv_discount_price.visibility = View.VISIBLE
                tv_discount_price?.text = showPriceAfterDiscount
            }

            if (baseGoodsBean?.dataTagList?.isNullOrEmpty() == false) {
                tv_depreciate_tips.bindData(baseGoodsBean?.dataTagList)
            } else {
                tv_depreciate_tips.visibility = View.GONE
            }
            if (baseGoodsBean?.tagList?.isNullOrEmpty() == false) {
                rl_icon_type.bindData(baseGoodsBean?.tagList)
            } else {
                rl_icon_type.visibility = View.GONE
            }
            if (baseGoodsBean?.tagWholeOrderList?.isNullOrEmpty() == false) {
                baseGoodsBean?.tagWholeOrderList?.forEach {
                    if (it.name.equals("整单包邮")) {
                        cl_new_tag.visibility = View.VISIBLE
                        tv_new_tag_1.text = it.name
                        tv_new_tag_2.text = it.description
                    }
                }
            } else {
                cl_new_tag.visibility = View.GONE
            }
            if (baseGoodsBean?.tagTitle?.isNullOrEmpty() == false) {
                tv_goods_name.textWithSuffixTag(name, baseGoodsBean.tagTitle!!)
            }
            tv_group_total?.text = subtotal
            tv_number?.text = "${amount}"
            if (baseGoodsBean?.itemType == level1_group_good) {
                cb_good.visibility = View.GONE
            } else {
                cb_good.visibility = View.VISIBLE
                cb_good.isChecked = this.selected
            }
            if (baseGoodsBean is Level1ItemGroupGoodBean) {
                iv_good_num_in_group?.visibility = View.VISIBLE
                iv_good_num_in_group?.text = "X ${packageProductQty}"
            } else {
                iv_good_num_in_group?.visibility = View.GONE
            }

            if (baseGoodsBean is Level1ItemCommonGoodsBean) {
                if (baseGoodsBean?.activityBean?.title.isNullOrEmpty()) {
                    tv_activity_single?.visibility = View.GONE
                } else {
                    tv_activity_single?.visibility = View.VISIBLE
                    val tagBean: TagBean = TagBean().apply {
                        uiStyle = 1
                        text = baseGoodsBean?.activityBean?.activityTypeText ?: ""
                        textColor = "#FFFFFF"
                        bgColor = "#FC6C41"
                        borderColor = "#FC6C41"
                    }
                    val listOfTagBean = mutableListOf<TagBean>()
                    listOfTagBean.add(tagBean)
                    tv_activity_single.TextWithPrefixTag(listOfTagBean, baseGoodsBean?.activityBean?.title ?: "", 1)
                    tv_activity_single.setLineSpacing(0f,1.1f)
                }
            }


            if (TextUtils.isEmpty(purchaseLimitStr)) {
                tv_limit.visibility = View.GONE
            } else {
                tv_limit.visibility = View.VISIBLE
                val tagBean: TagBean = TagBean().apply {
                    uiStyle = 1
                    text = "限购商品"
                    textColor = "#FF7200"
                    bgColor = "#ffffffff"
                    borderColor = "#FF7200"
                }
                val listOfTagBean = mutableListOf<TagBean>()
                listOfTagBean.add(tagBean)
                tv_limit.TextWithPrefixTag(listOfTagBean, purchaseLimitStr, 1)
            }

            val remainTime = seckillRemainingTime - SystemClock.elapsedRealtime() + responseLocalTime
            if (remainTime > 0) {
                ll_time_out?.visibility = View.VISIBLE
//                tv_cart_countdown?.addSecondCountDown(seckillRemainingTime) { expireTime, end ->
//                    tv_cart_countdown.text = if (end) "已结束" else "距结束还剩 ${TimeUtils.timeFormat(expireTime)}"
//                }
                // 如果遇到刷新数据，要先取消原来的倒计时
                countDownTimerMap.get(holder.hashCode())?.let {
                    it.cancel()
                    countDownTimerMap.remove(holder.hashCode())
                }

                val countDownTimer = object : CountDownTimer(remainTime, 1000L) {
                    override fun onTick(millisUntilFinished: Long) {
                        tv_cart_countdown?.text = "距结束还剩 ${TimeUtils.timeFormat(millisUntilFinished)}"
                    }

                    override fun onFinish() {
                        seckillRemainingTime = 0L
                        tv_cart_countdown.text = "已结束"
                        //notifyItemChanged(holder.bindingAdapterPosition)
                    }
                }
                countDownTimerMap.put(holder.hashCode(), countDownTimer)
                countDownTimer.start()
            } else {
                ll_time_out?.visibility = View.GONE
            }

            baseGoodsBean.specialtyCouponTag?.let { tag ->
                clProductCoupons.isVisible = !tag.tips.isNullOrEmpty()

                if (!tag.appSupplementOrderUrl.isNullOrEmpty()) {
                    llGoGatherOrders.visibility = View.VISIBLE
                    llGoGatherOrders.setOnClickListener {
                        //去凑单
                        RoutersUtils.open(tag.appSupplementOrderUrl)
                    }
                } else {
                    llGoGatherOrders.visibility = View.GONE
                }

                val listTag = arrayListOf<TagBean>()
                if (!tag.title.isNullOrEmpty()) {
                    listTag.add(TagBean().apply {
                        text = tag.title
                        textColor = "#F82324"
                        bgColor = "#F7F7F7"
                        borderColor = "#F82324"
                    })
                }
                tvProductCouponsContent.TextWithPrefixTag(listTag,tag.tips?:"", tagTextSize = 11, mRadiusSize = 3, rightMargin = 6)

            } ?: kotlin.run {
                clProductCoupons.visibility = View.GONE
            }


            cb_good.setOnClickListener {
                changeItemSelectStatus(!selected, skuid ?: "", false)
            }
            tv_number?.takeIf { it.visibility == View.VISIBLE }?.setOnClickListener {
                CartReport.trackCartGoodsCountBtnClick(mContext, baseGoodsBean.getProdId(), tv_number.text?.toString())
                onEditNumClick(holder, item)
            }
            iv_numSub?.setOnClickListener {
                CartReport.trackCartGoodsSubtractBtnClick(mContext, baseGoodsBean.getProdId())
                subGoodsFromCart(holder.absoluteAdapterPosition, baseGoodsBean, (baseGoodsBean.amount ?: "0").toInt())
            }
            iv_numAdd.setOnClickListener {
                CartReport.trackCartGoodsAddBtnClick(mContext, baseGoodsBean.getProdId())
                addGoodsToCart(holder.absoluteAdapterPosition, baseGoodsBean, (baseGoodsBean.amount ?: "0").toInt())
            }

            cl_item.setOnClickListener {
                if (baseGoodsBean.itemType != level1_group_good) {
                    CartReport.trackCartGoodsClick(mContext, baseGoodsBean.getProdId())
                }
                var mUrl = "ybmpage://productdetail/${skuid}"
                mUrl = splicingUrlWithParams(
                        mUrl,
                        hashMapOf(
                                Pair<String, Any>(
                                        IntentCanst.JG_ENTRANCE,
                                        jgTrackBean?.entrance ?: "购物车"),
                                Pair<String, Any>(
                                        IntentCanst.JG_REFERRER,
                                        JGTrackManager.TrackShoppingCart.TITLE),
                                Pair<String, Any>(
                                        IntentCanst.JG_REFERRER_TITLE,
                                        JGTrackManager.TrackShoppingCart.TITLE),
                                Pair<String, Any>(
                                        IntentCanst.JG_REFERRER_MODULE,
                                        "${JGTrackManager.TrackShoppingCart.TITLE}-${JGTrackManager.Common.MODULE_PRODUCT_LIST}"),
                                 ))
                RoutersUtils.open(mUrl)
                resourceClickTrackListener?.invoke(
                        skuid ?: "",
                        name?.toString() ?: "",
                        unitPrice,
                        filterGoodsPosition(holder.bindingAdapterPosition))
            }

            btnCollect?.setOnClickListener {
                if (baseGoodsBean.itemType != level1_group_good) {
                    CartReport.trackCartGoodsCollectBtnClick(mContext, baseGoodsBean.getProdId())
                }
                cartFragmentV3?.showProgress()
                //且如果想让侧滑菜单同时关闭，需要同时调用quickClose();
                cart_swipe_layout.quickClose()
                cartFragmentV3?.mViewModel?.batchcollect(ids = skuid ?: "")
            }

            btnDelete?.setOnClickListener {
//                cartFragmentV3?.showProgress()
                cart_swipe_layout.quickClose()
                // merchantId: String, packageIds: String, ids: String
                cartFragmentV3?.showDelDialog(mutableListOf(this))
//                cartFragmentV3?.mViewModel?.removeProductFromCart(SpUtil.getMerchantid(), packageIds = "", ids = skuid ?: "")
            }

            //找相似
            btnFindSameGoods.setOnClickListener {
                if (baseGoodsBean.itemType != level1_group_good) {
                    CartReport.trackCartGoodsSameBtnClick(mContext, baseGoodsBean.getProdId())
                }
                RoutersUtils.open("ybmpage://findsamegoods/${skuid}")
                cart_swipe_layout.quickClose()
                XyyIoUtil.track("action_ShoppingCart_Similar", hashMapOf("sku_id" to skuid, "located" to "1"))
            }

            skuid?.let {
                productViewTrackMap[it]?.let { time ->
                    if (time - System.currentTimeMillis() > TRACK_DURATION) {
                        resourceViewTrackListener?.invoke(
                                skuid ?: "",
                                name?.toString() ?: "",
                                unitPrice,
                                filterGoodsPosition(holder.bindingAdapterPosition))
                        productViewTrackMap[it] = System.currentTimeMillis()
                    }
                } ?: kotlin.run {
                    resourceViewTrackListener?.invoke(
                            skuid ?: "",
                            name?.toString() ?: "",
                            unitPrice,
                            filterGoodsPosition(holder.bindingAdapterPosition))
                    productViewTrackMap[it] = System.currentTimeMillis()
                }
            }

            handleLimitTimePremium(holder, this)

        }
    }

    private fun handleLimitTimePremium(holder: BaseViewHolder,level1ItemGoodsBeanAbs: Level1ItemGoodsBeanAbs) {
        val group = holder.itemView.findViewById<androidx.constraintlayout.widget.Group>(R.id.group_limited_time_premium)
        val tv_discount_price = holder.getView<TextView>(R.id.tv_discount_price)

        val limitedTimeSupplement = level1ItemGoodsBeanAbs.limitedTimeSupplement ?: kotlin.run {
            group.isVisible = false
            return
        }
        group.isVisible = true
        tv_discount_price.visibility = View.GONE //折后价隐藏
        processCountDown(holder, limitedTimeSupplement)
    }


    private fun processCountDown(baseViewHolder: BaseViewHolder, limitedTimeSupplement: CartItemBean.LimitedTimeSupplement) {
        val tvLimitedTimePremiumTime = baseViewHolder.itemView.findViewById<TextView>(R.id.tv_limited_time_premium_time)
        val localDiff: Long = System.currentTimeMillis() - (limitedTimeSupplement.responseLocalTime ?: 0L)
        val leftTime = (limitedTimeSupplement.remainingTime ?: 0) - localDiff
        // 如果遇到刷新数据，要先取消原来的倒计时
        limitTimePremiumTimerMap.get(baseViewHolder.hashCode())?.let {
            it.cancel()
            limitTimePremiumTimerMap.remove(baseViewHolder.hashCode())
        }

        if (leftTime >= 0) {
            // 补价倒计时
            val countDownTimer = object : CountDownTimer(leftTime, 100L) {
                override fun onTick(millisUntilFinished: Long) {
                    // 计算剩余的小时数
                    val hours: Long = millisUntilFinished / (1000 * 60 * 60)
                    // 计算剩余的分钟数（去掉小时部分后的毫秒数）
                    val minutes: Long = millisUntilFinished % (1000 * 60 * 60) / (1000 * 60)
                    // 计算剩余的秒数（去掉分钟部分后的毫秒数）
                    val seconds: Long = millisUntilFinished % (1000 * 60) / 1000
                    // 计算剩余的百毫秒数
                    val hundredMilliseconds: Long = millisUntilFinished % 1000 / 100

                    val spannableStringBuilder = SpannableStringBuilder("${if (hours < 10) "0$hours" else hours.toString()}:${if (minutes < 10) "0$minutes" else minutes.toString()}:${if (seconds < 10) "0$seconds" else seconds.toString()}")
                    spannableStringBuilder.setSpan(
                            AbsoluteSizeSpan(11, true),
                            spannableStringBuilder.length - 3,
                            spannableStringBuilder.length,
                            Spannable.SPAN_INCLUSIVE_INCLUSIVE
                    )
                    tvLimitedTimePremiumTime.text = spannableStringBuilder
                }

                override fun onFinish() {
                    val spannableStringBuilder = SpannableStringBuilder("00:00:00")
                    spannableStringBuilder.append("后结束")
                    spannableStringBuilder.setSpan(
                            AbsoluteSizeSpan(11, true),
                            spannableStringBuilder.length - 3,
                            spannableStringBuilder.length,
                            Spannable.SPAN_INCLUSIVE_INCLUSIVE
                    )
                    tvLimitedTimePremiumTime.text = spannableStringBuilder
                }
            }
            limitTimePremiumTimerMap.put(baseViewHolder.hashCode(), countDownTimer)
            countDownTimer.start()
        }
    }


    /**
     * 筛选商品类型的下标
     */
    private fun filterGoodsPosition(originPosition: Int): Int {
        var position = originPosition
        for (i in 0 until originPosition) {
            if (data[i].itemType != level1_group_good && data[i].itemType != level1_activity_good_header && data[i].itemType != level1_activity_good && data[i].itemType != level1_activity_good_end && data[i].itemType != level1_common_good) {
                position--
            }
        }
        return position
    }

    private fun onEditNumClick(
            holder: BaseViewHolder, item: Level1ItemWrapper) {
        var amount: Int = 0
        var packageNum: Int = 1
        var canSplit: Boolean = false
        when (item) {
            is Level1ItemGoodsBeanAbs -> {
                amount = item.amount?.toInt() ?: 0
                packageNum = item.mediumPackageNum
                canSplit = item.canSplit
            }

            is Level1ItemGroupFooterBean -> {
                amount = item.amount?.toInt() ?: 0
                packageNum = item.mediumPackageNum
                canSplit = item.canSplit
            }
        }

        DialogUtil.addOrSubDialog(mContext as BaseActivity, InputType.TYPE_CLASS_NUMBER, amount.toString(), packageNum, canSplit, true,
            object : DialogClickListener {
                private var mImm: InputMethodManager? = null
                override fun confirm(num: String) {
                    sendShopNum(holder.absoluteAdapterPosition, item, num)
                }

                override fun cancel() {}
                override fun showSoftInput(view: View) {
                    try {
                        if (mImm == null) {
                            mImm =
                                view.context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
                        }
                        if (mImm != null) {
                            mImm!!.showSoftInput(view, InputMethodManager.SHOW_IMPLICIT)
                        }
                    } catch (e: Throwable) {
                        e.printStackTrace()
                    }
                }
            })

    }

    fun subGoodsFromCart(position: Int, item: Level1ItemWrapper, num: Int) {
        var subNum = 0
        when (item) {
            is Level1ItemGoodsBeanAbs -> {
                subNum = if (item.canSplit) 1 else item.mediumPackageNum
                sendShopNum(position, item, "${num - subNum}")
            }
            is Level1ItemGroupFooterBean -> {
                subNum = if (item.canSplit) 1 else item.mediumPackageNum
                sendShopNum(position, item, "${num - subNum}")
            }
        }
    }


    fun addGoodsToCart(posion: Int, item: Level1ItemWrapper, num: Int) {
        when (item) {
            is Level1ItemGoodsBeanAbs -> {
                sendShopNum(posion, item, "${num + item.mediumPackageNum}")
            }
            is Level1ItemGroupFooterBean -> {
                sendShopNum(posion, item, "${num + item.mediumPackageNum}")
            }
        }
    }

    val clickDelayUtil by lazy { ClickDelayUtil() }

    /**
     * 编辑商品或套餐的加购数量
     */
    private fun sendShopNum(postion: Int, item: Level1ItemWrapper, num: String) {
        var goodsNum = try {
            num.toInt()
        } catch (e: Exception) {
            0
        }
        if (goodsNum < 0) goodsNum = 0
        if (goodsNum == 0) {
            cartFragmentV3?.showDelDialog(mutableListOf(item))
        } else {
            val changeCartParamsMap = hashMapOf<String, String>()
            changeCartParamsMap["merchantId"] = SpUtil.getMerchantid()
            when (item) {
                is Level1ItemGoodsBeanAbs -> {
                    changeCartParamsMap["skuId"] = item.skuid ?: ""
                    changeCartParamsMap["productName"] = item.name?.toString() ?: ""
                    changeCartParamsMap["productPrice"] = item.unitPrice.toString()
                    changeCartParamsMap["productNumber"] = num
                    changeCartParamsMap["position"] = postion.toString()
                }

                is Level1ItemGroupFooterBean -> {
                    changeCartParamsMap["packageId"] = item.packageId ?: ""
                }
            }
            changeCartParamsMap["amount"] = goodsNum.toString()
            addAnalysisRequestParams(changeCartParamsMap, null, AnalysisConst.FlowDataChain.FLOWDATACHAIN_TAG_ADDCART_CART)
            clickDelayUtil.checkClick(true) { count: Int?, aBoolean: Boolean ->
                if (aBoolean) {
                    changeCartParamsMap["entrance"] = "购物车"
                    clickDelayUtil.pushTask {
//                        cartFragmentV3?.showProgress()
                        cartFragmentV3?.mViewModel?.changeCart(changeCartParamsMap)
                    }
                } else {
                    if (goodsNum > 0) {
                        item.amount = goodsNum.toString()
                        if (item is Level1ItemGroupFooterBean && !item.rangePriceBean.isStep()) {
                            val subTotal = item.realPrice * (item.amount?.toInt()?: 0)
                            item.subtotal = SpannableStringBuilder("小计 ¥${UiUtils.transform(subTotal.toString() ?: "")}").toString()
                        } else if(item is Level1ItemGoodsBeanAbs && !item.rangePriceBean.isStep()) {
                            val subTotal = item.unitPrice * (item.amount?.toInt()?: 0)
                            item.subtotal = SpannableStringBuilder("小计 ¥${UiUtils.transform(subTotal.toString() ?: "")}")
                        }
                        notifyItemChanged(postion)
                    }
                }
            }
        }

    }

    fun changeShopSelectStatus(check: Boolean, orgid: String, isThirdCompany: Boolean = false, isSubShop: Boolean = false) {
        cartFragmentV3?.mViewModel?.changeShopSelectStatus(check, orgid, isThirdCompany, isSubShop)
    }

    fun changeItemSelectStatus(check: Boolean, itemId: String, isGroup: Boolean) {
        cartFragmentV3?.mViewModel?.changeGoodsSelectStatus(check, itemId, isGroup)
    }


    private fun convertActivityHeader(holder: BaseViewHolder, item: MultiItemEntity) {
        val tv_activity_title = holder.getView<TextView>(R.id.tv_activity_title)
        val tv_arrow = holder.getView<TextView>(R.id.tv_arrow)

        val level1ItemActivityHeaderBean = item as Level1ItemActivityHeaderBean
        level1ItemActivityHeaderBean.apply {
            if (!traceProductData.contains(getTraceKey(shopCode, SpUtil.getMerchantid(), activityId))) {
                mCartAnalysis.shopeItemReductionExposure(shopcode = shopCode, prom_type = activityType, prom_id = activityId, action = titleUrl)
                traceProductData.add(getTraceKey(shopCode, SpUtil.getMerchantid(), activityId))
            }
            val tagBean: TagBean = TagBean().apply {
                uiStyle = 1
                text = activityTypeText ?: ""
                textColor = "#FFFFFF"
                bgColor = "#FD2424"
                borderColor = "#FD2424"
            }
            val listOfTagBean = mutableListOf<TagBean>()
            listOfTagBean.add(tagBean)
            tv_activity_title.TextWithPrefixTag(listOfTagBean, title ?: "", 1)
            tv_arrow?.visibility = if (TextUtils.isEmpty(titleUrl)) View.GONE else View.VISIBLE
            tv_arrow?.setOnClickListener {
                titleUrl?.let {
                    mCartAnalysis.shopeItemReductionClick(shopcode = shopCode, prom_type = activityType, prom_id = activityId, action = titleUrl)
                    RoutersUtils.open(it)
                }
            }
            tv_arrow?.takeIf { it.visibility == View.VISIBLE }?.text = titleUrlText
        }
    }

    private fun convertSubShopHeader(holder: BaseViewHolder, item: MultiItemEntity) {
        val cb_shop = holder.getView<CheckBox>(R.id.cb_shop)
        val tv_shop_name = holder.getView<TextView>(R.id.tv_shop_name)
        val iv_coupon = holder.getView<ImageView>(R.id.iv_coupon)

        val level1ItemSubShopHeaderBean = item as Level1ItemSubShopHeaderBean
        level1ItemSubShopHeaderBean.apply {
            cb_shop?.isChecked = selected
            cb_shop?.setOnClickListener { changeShopSelectStatus(!selected, shopCode ?: "", isThirdCompany, isSubShop = true) }
            tv_shop_name?.text = companyName
            tv_shop_name?.setOnClickListener {
                var mUrl = shopJumpUrl?:""
                var mEntrance: String? = jgTrackBean?.entrance?: JGTrackManager.TrackShoppingCart.TITLE
                val mParams = HashMap<String, String>()
                mParams[IntentCanst.JG_REFERRER] = jgTrackBean?.jgReferrer?:""
                mParams[IntentCanst.JG_REFERRER_TITLE] = jgTrackBean?.jgReferrerTitle?:""
                mParams[IntentCanst.JG_ENTRANCE] = mEntrance?:""
                mUrl = splicingUrlWithParams(
                        mUrl,
                        mParams)
                RoutersUtils.open(mUrl)
            }
            iv_coupon?.visibility = if (isHaveVoucher) View.VISIBLE else View.GONE
            iv_coupon?.setOnClickListener {
                mCartAnalysis.cartShopCouponClick(it.context,shopCode)
                cartFragmentV3?.showShopBottomCartCouponDialog(shopCode ?: "", companyName?: "", skuids ?: "", it)
            }
        }
    }

    /**
     * 处理失效商品头部
     */
    private fun convertInvalidHeader(holder: BaseViewHolder, item: MultiItemEntity?) {
        val tv_invalid_title = holder.getView<TextView>(R.id.tv_invalid_title)
        val tv_move_to_collection = holder.getView<TextView>(R.id.tv_move_to_collection)
        val tv_clear_all = holder.getView<TextView>(R.id.tv_clear_all)
        val ids = mutableListOf<String>()
        val packageIds = mutableListOf<String>()

        val level0ItemInvalidBean = item as Level0ItemInvalidBean
        tv_invalid_title?.text = "失效商品(${level0ItemInvalidBean.productTotalNum})"
        tv_move_to_collection?.setOnClickListener {
            data.forEach {
                if (it is Level1InvalidGoodBean) {
                    ids.add(it.skuid ?: "")
                }
                if (it is Level1InvalidGroupGoodBean) {
                    ids.add(it.skuid ?: "")
                }
            }
            cartFragmentV3?.showProgress()
            cartFragmentV3?.mViewModel?.batchCollectWithGoodsInfo(ids.joinToString(","), data)
        }
        tv_clear_all?.setOnClickListener {
            data.forEach {
                if (it is Level1InvalidGoodBean) {
                    ids.add(it.skuid ?: "")
                }
                if (it is Level1InvalidGroupHeaderBean) {
                    packageIds.add(it.packageId ?: "")
                }
            }
            cartFragmentV3?.mViewModel?.removeProductFromCart(SpUtil.getMerchantid(), packageIds.joinToString(","), ids.joinToString(","))
        }
    }

    /**
     * 处理店铺尾部信息
     */
    private fun convertShopEnd(holder: BaseViewHolder, item: MultiItemEntity?) {
        val tv_shop_num = holder.getView<TextView>(R.id.tv_shop_num)
        val tv_payAmount = holder.getView<TextView>(R.id.tv_payAmount)
        val tv_payAmount_loading = holder.getView<TextView>(R.id.tv_payAmount_loading)
        val ll_payAmount_loading = holder.getView<LinearLayout>(R.id.ll_payAmount_loading)

        val level0ItemShopFooterBean = item as Level0ItemShopFooterBean
        level0ItemShopFooterBean.apply {
            tv_shop_num?.text = "${productVarietyNum}种 共${productTotalNum}件"
            if (level0ItemShopFooterBean.isLoading) {
                ll_payAmount_loading.visibility = View.VISIBLE
                tv_payAmount.visibility = View.GONE
                tv_payAmount_loading.text = level0ItemShopFooterBean.loadingText
            } else {
                tv_payAmount.visibility = View.VISIBLE
                ll_payAmount_loading.visibility = View.GONE
                tv_payAmount?.text = "合计：¥${UiUtils.transform(payAmount)}"
            }
        }
    }

    /**
     * 处理店铺头部信息
     */
    private fun convertShopHeader(holder: BaseViewHolder, item: MultiItemEntity?) {
        val tv_shop_name = holder.getView<TextView>(R.id.tv_shop_name)
        val cbExpand = holder.getView<CheckBox>(R.id.cb_expand)
        val iv_coupon = holder.getView<ImageView>(R.id.iv_coupon)
        val cb_shop = holder.getView<CheckBox>(R.id.cb_shop)

        val tv_freight = holder.getView<TextView>(R.id.tv_freight)
        val tv_freight_arrow = holder.getView<TextView>(R.id.tv_freight_arrow)

        val tv_refund = holder.getView<TextView>(R.id.tv_refund)
        val tv_refund_arrow = holder.getView<TextView>(R.id.tv_refund_arrow)
        val tv_shop_discounts = holder.getView<TextView>(R.id.tv_shop_discounts)
        val tv_shop_discounts_arrow = holder.getView<TextView>(R.id.tv_shop_discounts_arrow)
        val iv_shop_discounts = holder.getView<ImageView>(R.id.iv_shop_discounts)

        val level0ItemShopHeaderBean = item as Level0ItemShopHeaderBean

        level0ItemShopHeaderBean?.apply {
            var tagBean: TagBean? = null
            if (!isThirdCompany) {
                tagBean = TagBean().apply {
                    uiStyle = 1
                    text = "自营"
                    textColor = "#00B377"
                    bgColor = "#1A00B377"
                    borderColor = "#A0E1CC"
                }
            } else {
                tagBean = TagBean().apply {
                    uiStyle = 1
                    text = "商业"
                    textColor = "#FC6B0B"
                    bgColor = "#FFFFFBF8"
                    borderColor = "#FDAC77"
                }
            }
            val listOfTagBean = mutableListOf<TagBean>()
            tagBean?.let { listOfTagBean.add(it) }
            tv_shop_name.TextWithPrefixTag(listOfTagBean, shopName, 1)
            tv_shop_name?.setOnClickListener {
                CartReport.trackCartShopBtnClick(mContext, originalShopCode)
                mCartAnalysis.cartShopClick(shopCode, shopJumpUrl)
                var mUrl = shopJumpUrl?:""
                var mEntrance: String? = jgTrackBean?.entrance?: JGTrackManager.TrackShoppingCart.TITLE
                val mParams = HashMap<String, String>()
                mParams[IntentCanst.JG_REFERRER] = jgTrackBean?.jgReferrer?:""
                mParams[IntentCanst.JG_REFERRER_TITLE] = jgTrackBean?.jgReferrerTitle?:""
                mParams[IntentCanst.JG_ENTRANCE] = mEntrance?:""
                mUrl = splicingUrlWithParams(
                        mUrl,
                        mParams)
                RoutersUtils.open(mUrl)
            }

            cbExpand.isChecked = isExpanded
            cb_shop.isChecked = selected
            cb_shop.setOnClickListener { changeShopSelectStatus(!selected, orgId ?: "", isThirdCompany) }
            iv_coupon?.setOnClickListener {
                CartReport.trackCartShopCouponBtnClick(mContext, originalShopCode)
                mCartAnalysis.cartShopCouponClick(it.context,shopCode)
                cartFragmentV3?.showShopBottomCartCouponDialog(cartFragmentV3?.getCouponCartShopEntryType(), shopCode ?: "", shopName?: "", skuids ?: "", iv_coupon,isTab = true)
            }

            if (showFreightTips) {
                tv_freight?.visibility = View.VISIBLE
                val preFreight = TagBean().apply {
                    uiStyle = 1
                    text = " 运费凑单 "
                    textColor = "#FF0001"
                    bgColor = "#00000000"
                    borderColor = "#FF0001"
                }
                val preFreightTagBeans = mutableListOf<TagBean>()
                preFreightTagBeans.add(preFreight)
                freightTips?.let { tv_freight?.TextWithPrefixTag(preFreightTagBeans, freightTips, 1) }

                if (showFreightIcon) {
                    val drawable = ContextCompat.getDrawable(mContext, R.drawable.icon_hint_image_cart)
                    drawable?.setBounds(0, 0, ConvertUtils.dp2px(12f), ConvertUtils.dp2px(12f))
                    tv_freight.setCompoundDrawables(null, null, drawable, null)
                    tv_freight?.setOnClickListener {
                        mCartAnalysis.shopeItemPostClick(shopCode)
                        FreightTipDialog(mContext).showTip(shopCode)
                    }
                } else {
                    tv_freight.setCompoundDrawables(null, null, null, null)
                }

                tv_freight_arrow?.visibility = if (freightUrlText.isNullOrEmpty()) View.GONE else View.VISIBLE
                tv_freight_arrow?.text = freightUrlText
                tv_freight_arrow?.setOnClickListener {
                    CartReport.trackCartShopCouponMakeUpOrder1(mContext, originalShopCode, tv_freight_arrow.text?.toString())
                    mCartAnalysis.shopeItemPostToAddClick(shopCode)
                    RoutersUtils.open(freightJumpUrl)
                }
            } else {
                tv_freight?.visibility = View.GONE
                tv_freight_arrow?.visibility = View.GONE
            }

            if (showReturnVoucherInfo) {
                tv_refund?.visibility = View.VISIBLE
                if (!traceProductData.contains(getTraceKey(shopCode, SpUtil.getMerchantid()))) {
                    mCartAnalysis.shopeItemReturndExposure(shopcode = shopCode, prom_type = activityType, prom_id = activityId, action = returnVoucherJumpUrl)
                    traceProductData.add(getTraceKey(shopCode, SpUtil.getMerchantid()))
                }
                val preRefund = TagBean().apply {
                    uiStyle = 1
                    text = " 返 "
                    textColor = "#F82324"
                    bgColor = "#FFF7F7"
                    borderColor = "#FFC7C8"
                }
                returnVoucherTips?.let { tv_refund?.TextWithPrefixTag(listOf(preRefund), returnVoucherTips, 1) }

                tv_refund_arrow?.visibility = if (returnVoucherUrlText.isNullOrEmpty()) View.GONE else View.VISIBLE
                tv_refund_arrow?.text = returnVoucherUrlText
                tv_refund_arrow?.setOnClickListener {
                    mCartAnalysis.shopeItemReturndClick(shopcode = shopCode, action = returnVoucherJumpUrl)
                    RoutersUtils.open(returnVoucherJumpUrl)
                }
            } else {
                tv_refund?.visibility = View.GONE
                tv_refund_arrow?.visibility = View.GONE
            }

            tv_shop_discounts?.visibility = View.GONE
            tv_shop_discounts_arrow?.visibility = View.GONE
            iv_shop_discounts?.visibility = View.GONE
            shopDiscounts?.let {tipsBean->
                tipsBean.tips?.let {
//                    val preShopDiscounts = TagBean().apply {
//                        uiStyle = 1
//                        text = if (!tipsBean.tagList.isNullOrEmpty()){
//                            tipsBean.tagList[0]?.let {tag->
//                                if (!tag.name.isNullOrEmpty()){
//                                    " ${tag.name} "
//                                }else " 券 "
//                            }?:" 券 "
//                        }else " 券 "
//                        textColor = "#F82324"
//                        bgColor = "#FFF7F7"
//                        borderColor = "#FFC7C8"
//                    }

                    iv_shop_discounts?.visibility = View.VISIBLE
                    tv_shop_discounts?.visibility = View.VISIBLE
//                    tv_shop_discounts?.TextWithPrefixTag(listOf(preShopDiscounts), it, 1)
                    tv_shop_discounts?.text = it
                    try {
                        tv_shop_discounts.setTextColor(Color.parseColor(tipsBean.tipsFontColor?:"#676773"))
                    }catch (e:Exception){
                        tv_shop_discounts.setTextColor(ContextCompat.getColor(mContext,R.color.color_676773))
                    }
                }

                tipsBean.titleUrlText?.let {
                    tv_shop_discounts_arrow?.visibility = View.VISIBLE
                    tv_shop_discounts_arrow?.text = it
                    tv_shop_discounts_arrow?.setOnClickListener {
                        CartReport.trackCartShopCouponMakeUpOrder2(mContext, originalShopCode, tv_shop_discounts_arrow.text?.toString())
                        if (tipsBean.haseReceived) {
                            RoutersUtils.open(tipsBean.appUrl)
                            val properties = java.util.HashMap<String, Any>()
                            properties[JGTrackManager.FIELD.FIELD_TITLE] = JGTrackManager.TrackShoppingCart.TITLE
                            properties[JGTrackManager.FIELD.FIELD_BTN_NAME] = "购物车店铺券凑单"

                        } else {
                            cartFragmentV3?.mViewModel?.getVoucher2(tipsBean.couponTemplateId,tipsBean.appUrl, tipsBean)
                        }
                    }
                }
            }


        }

        cbExpand?.setOnClickListener {
            if (level0ItemShopHeaderBean.isExpanded) {
                val subItemCount = collapse(holder.absoluteAdapterPosition, true)
                LogUtil.e("xyd", "collapse count = ${subItemCount} status = ${level0ItemShopHeaderBean.isExpanded}")
                cachedShopExpandStatus[level0ItemShopHeaderBean.shopCode] = false
                mCartAnalysis.shopeItemCollapseClick(level0ItemShopHeaderBean.shopCode)
            } else {
                val subItemCount = expand(holder.absoluteAdapterPosition, true)
                LogUtil.e("xyd", "expand count = ${subItemCount} status = ${level0ItemShopHeaderBean.isExpanded}")
                cachedShopExpandStatus[level0ItemShopHeaderBean.shopCode] = true
                mCartAnalysis.shopeItemExpandClick(level0ItemShopHeaderBean.shopCode)
            }
            val toJson = Gson().toJson(cachedShopExpandStatus)
            MMKV.defaultMMKV().encode("${KEY_SHOP_EXPAND_STATUS}_${SpUtil.getMerchantid()}", toJson)
        }
        iv_coupon.visibility = if (level0ItemShopHeaderBean.isHaveVoucher) View.VISIBLE else View.GONE

    }

}