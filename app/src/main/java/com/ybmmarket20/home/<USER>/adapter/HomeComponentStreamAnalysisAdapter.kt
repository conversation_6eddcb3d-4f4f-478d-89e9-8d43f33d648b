package com.ybmmarket20.home.newpage.adapter

import android.content.Context
import androidx.recyclerview.widget.RecyclerView
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.home.newpage.adapter.HomeFeedStreamAdapter.CommodityFeedStreamVH
import com.ybmmarket20.home.newpage.bean.HomeFeedStreamBean
import com.ybmmarket20.xyyreport.SpmLogUtil
import com.ybmmarket20.xyyreport.page.home.HomeReportEvent
import com.ybmmarket20.xyyreport.session.SessionManager
import com.ybmmarket20.xyyreport.spm.TrackData

/**
 * 商品广告流-埋点
 */
abstract class HomeComponentStreamAnalysisAdapter<T: RecyclerView.ViewHolder>:
    HomeComponentAnalysisAdapter<T>() {

    var mComponentTrackData: TrackData? = null
    lateinit var mContext: Context
    private var isComponentExposure = false

    override fun onBindViewHolder(holder: T, position: Int) {
        if (!isComponentExposure) {
            SpmLogUtil.print("首页-feed组件-曝光")
            HomeReportEvent.trackHomeComponentExposure(mContext, mComponentTrackData?.spmEntity)
            isComponentExposure = true
        }
        if (holder is CommodityFeedStreamVH) {
            if (mExposureRecord.contains(position)) return
            onGoodsItemExposure(getDataList()[position].product?.trackData, position)
            mExposureRecord.add(position)
        }
    }

    /**
     * 广告点击
     */
    open fun onAdItemClick(trackData: TrackData?) {
        SpmLogUtil.print("首页-feed组件-广告点击")
        onSubcomponentClick(mContext, trackData)
    }

    /**
     * banner点击
     */
    fun onBannerItemClick(trackData: TrackData?) {
        SpmLogUtil.print("首页-feed组件-banner点击")
        onSubcomponentClick(mContext, trackData)
    }

    /**
     * 商品点击
     */
    fun onGoodsItemClick(trackData: TrackData?, rowsBean: RowsBean?) {
        SpmLogUtil.print("首页-feed组件-商品点击")
        val newTrackData = trackData?.newTrackData()
        newTrackData?.scmEntity?.scmE += SessionManager.get().newGoodsScmRandom()
        HomeReportEvent.trackHomeSubComponentGoodsClick(mContext, newTrackData?.spmEntity, newTrackData?.scmEntity, rowsBean?.id, rowsBean?.showName)
    }

    /**
     * 商品曝光
     */
    private fun onGoodsItemExposure(trackData: TrackData?, position: Int) {
        onComponentExposure(mContext, trackData, position) {
            SpmLogUtil.print("首页-feed组件-商品曝光")
        }
    }

    override fun onComponentExposure(
        context: Context,
        trackData: TrackData?,
        position: Int,
        block: (() -> Unit)?
    ) {
        if (mExposureRecord.contains(position)) return
        block?.invoke()
        mExposureRecord.add(position)
    }

    override fun resetExposureRecord() {
        super.resetExposureRecord()
        isComponentExposure = false
    }

    abstract fun getDataList(): MutableList<HomeFeedStreamBean>

}