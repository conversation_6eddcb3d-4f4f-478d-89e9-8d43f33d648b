package com.ybmmarket20.search

import android.annotation.SuppressLint
import android.graphics.Color
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.style.ForegroundColorSpan
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.CheckBox
import android.widget.LinearLayout
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.android.flexbox.FlexboxLayoutManager
import com.luck.picture.lib.tools.ScreenUtils
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.SearchDynamicLabelItem
import com.ybmmarket20.view.BaseFilterPopWindow
import com.ybmmarket20.view.homesteady.whenAllNotNull

/**
 *  动态标签pop弹框
 */
class DynamicLabelExtPopWindow(var isDynamicParam:Int,dynamicLabelSubItems: List<SearchDynamicLabelItem>?) :
    BaseFilterPopWindow() {

    private var rvDynamicLabel: RecyclerView? = null
    private lateinit var adapter: CnMediaAdapter
    var confirmListener:((String)->Unit)? = null
    private val newData = dynamicLabelSubItems?.map { it.copy() }
    init {
        initThisView()
    }

    override fun getLayoutParams() = LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT)

    @SuppressLint("NotifyDataSetChanged")
    private fun initThisView() {
        rvDynamicLabel = getView(R.id.rvDynamicLabelItem)
        val btnConfirm = getView<Button>(R.id.btn_affirm)

        if(isDynamicParam == 1){
            rvDynamicLabel?.layoutManager = FlexboxLayoutManager(rvDynamicLabel?.context)
            adapter = CnMediaAdapter(R.layout.item_pop_dynamic_label_flex, newData)
        }else{
            rvDynamicLabel?.layoutManager = GridLayoutManager(rvDynamicLabel?.context, 3)
            adapter = CnMediaAdapter(R.layout.item_pop_dynamic_label, newData)
        }
        rvDynamicLabel?.adapter = adapter
        getView<Button>(R.id.btn_reset).setOnClickListener {
            newData?.forEach {
                it.setSelectedStatus(false)
            }
            rvDynamicLabel?.adapter?.notifyDataSetChanged()
        }

        btnConfirm.setOnClickListener {
            multiSelectedSpecStr = StringBuilder(newData?.filter { it.getSelectedStatus() }?.joinToString("/") { it.itemKey } ?:"")
            confirmListener?.invoke(multiSelectedSpecStr.toString())
            dismiss()
        }
    }

    override fun getLayoutId() = R.layout.pop_layout_dynamic_label

    override fun initView() {
    }

    inner class CnMediaAdapter(layoutResId: Int, val list: List<SearchDynamicLabelItem>?) :
        YBMBaseAdapter<SearchDynamicLabelItem>(layoutResId, list) {

        var mCallback: ((selectedSpecCount: String) -> Unit)? = null

        @SuppressLint("NotifyDataSetChanged")
        override fun bindItemView(baseViewHolder: YBMBaseHolder, t: SearchDynamicLabelItem?) {
            whenAllNotNull(baseViewHolder, t) { holder, bean ->
                val cbItem = baseViewHolder.getView<CheckBox>(R.id.cb_item)
                val itemCount = if (bean.itemCount != 0) " (${bean.itemCount})" else ""
                val itemCountSpannable = SpannableStringBuilder(itemCount).also {
                    it.setSpan(
                        ForegroundColorSpan(Color.parseColor(if (bean.getSelectedStatus()) "#00B377" else "#9090A1")),
                        0,
                        it.length,
                        Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                    )
                }
                val key = if(1 == isDynamicParam){
                    cbItem.minWidth = (ScreenUtils.getScreenWidth(mContext) - ScreenUtils.dip2px(mContext,55f)) / 3
                    bean.itemName
                }else{
                    if (bean.itemName.length > 5) "${bean.itemName.subSequence(0, 5)}..." else bean.itemName
                }
                cbItem.text = SpannableStringBuilder(key).append(itemCountSpannable)
                cbItem.isChecked = bean.getSelectedStatus()
                holder.setOnClickListener(R.id.ll_item) {
                    bean.apply {
                        bean.setSelectedStatus(!bean.getSelectedStatus())
                        notifyItemChanged(holder.layoutPosition)
                    }
                }
            }
        }
    }
}