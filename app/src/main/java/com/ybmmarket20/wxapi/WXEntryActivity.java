package com.ybmmarket20.wxapi;


import android.content.pm.ActivityInfo;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.PersistableBundle;
import android.text.TextUtils;

import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentActivity;

import com.apkfuns.logutils.LogUtils;
import com.tencent.mm.opensdk.constants.ConstantsAPI;
import com.tencent.mm.opensdk.modelbase.BaseReq;
import com.tencent.mm.opensdk.modelbase.BaseResp;
import com.tencent.mm.opensdk.modelbiz.WXLaunchMiniProgram;
import com.tencent.mm.opensdk.modelmsg.SendAuth;
import com.tencent.mm.opensdk.modelmsg.ShowMessageFromWX;
import com.tencent.mm.opensdk.modelmsg.WXMediaMessage;
import com.tencent.mm.opensdk.openapi.IWXAPIEventHandler;
import com.ybmmarket20.R;
import com.ybmmarket20.common.YBMAppLike;
import com.ybmmarket20.common.util.ToastUtils;
import com.ybmmarket20.utils.RoutersUtils;
import com.ybmmarket20.utils.YBMPayUtil;
import com.ybmmarket20.utils.YBMWxUtil;
import com.ybmmarket20.utils.analysis.XyyIoUtil;

import org.json.JSONException;
import org.json.JSONObject;

import java.net.URLDecoder;
import java.util.HashMap;

public class WXEntryActivity extends FragmentActivity {

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState, @Nullable PersistableBundle persistentState) {
        if (android.os.Build.VERSION.SDK_INT != Build.VERSION_CODES.O) {
            setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT);
        }
        super.onCreate(savedInstanceState, persistentState);
    }

    static class WXHandler implements IWXAPIEventHandler {
        @Override
        public void onReq(BaseReq baseReq) {

            //获取开放标签传递的extinfo数据逻辑
            if (baseReq.getType() == ConstantsAPI.COMMAND_SHOWMESSAGE_FROM_WX && baseReq instanceof ShowMessageFromWX.Req) {
                ShowMessageFromWX.Req showReq = (ShowMessageFromWX.Req) baseReq;
                WXMediaMessage mediaMsg = showReq.message;
                String extInfo = mediaMsg.messageExt;
                LogUtils.d("wxentry", "extinfo:" + extInfo);
                RoutersUtils.open(extInfo);
                try {
                    String merchantId = getParamByKey(extInfo, "merchantId");
                    String type = getParamByKey(extInfo, "type");
                    HashMap<String, String> hashMap = new HashMap<>();
                    hashMap.put("merchantId", merchantId);
                    hashMap.put("type", type);
                    XyyIoUtil.track("action_Sharedlink_Click", hashMap);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                try {
                    String isWholesale = getParamByKey(extInfo, "isWholesale");
                    if (!TextUtils.isEmpty(isWholesale) && Integer.parseInt(isWholesale) == 1) {
                        String merchantId = getParamByKey(extInfo, "merchantId");
                        String skuId = getParamByKey(extInfo, "skuId");
                        HashMap<String, String> hashMap = new HashMap<>();
                        hashMap.put("skuId", skuId);
                        hashMap.put("merchantId", merchantId);
                        XyyIoUtil.track("launchYbmApp-FreeproductDetail-success", hashMap);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }

        @Override
        public void onResp(BaseResp resp) {
            if (resp.getType() == ConstantsAPI.COMMAND_LAUNCH_WX_MINIPROGRAM) {
                WXLaunchMiniProgram.Resp launchMiniProResp = (WXLaunchMiniProgram.Resp) resp;
                String extraData = launchMiniProResp.extMsg; //对应小程序组件 <button open-type="launchApp"> 中的 app-parameter 属性
                LogUtils.tag("weixinmini").e(extraData);
                //{ "success":false, "code":0, "msg":"支付失败！" }
                try {
                    JSONObject jsonObject = new JSONObject(extraData);
                    boolean payResult = jsonObject.optBoolean("success");
                    String payMsg = jsonObject.optString("msg");
                    if (mPaySDKCallBack != null) {
                        if (payResult) {
                            mPaySDKCallBack.sdkPayCallBack(YBMPayUtil.RET_CODE_SUCCESS, YBMAppLike.getApp().getResources().getString(R.string.payway_result_succ), "");
                        } else {
                            mPaySDKCallBack.sdkPayCallBack(YBMPayUtil.RET_CODE_FAIL_SDK, YBMAppLike.getApp().getResources().getString(R.string.payway_result_error_sdk), extraData);
                        }
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }

            switch (resp.errCode) {
                case BaseResp.ErrCode.ERR_OK:
                    //正确返回
//                ToastUtils.showShort("分享成功");
                    if(resp instanceof SendAuth.Resp){
                        if (mCallBack != null) {
                            mCallBack.sdkOauthBack(((SendAuth.Resp) resp).code);
                        }
                    }else{
                        if (mCallBack != null) {
                            mCallBack.sdkCallBack(YBMWxUtil.RET_CODE_SUCCESS, "分享成功");
                        }
                    }
                    break;
                case BaseResp.ErrCode.ERR_USER_CANCEL:
                    //用户取消
//                ToastUtils.showShort("用户取消");
                    break;
                case BaseResp.ErrCode.ERR_AUTH_DENIED:
                    //认证被否决
                    ToastUtils.showShort("认证失败");
                    break;
                case BaseResp.ErrCode.ERR_SENT_FAILED:
                    //发送失败
                    ToastUtils.showShort("发送失败");
                    break;
                case BaseResp.ErrCode.ERR_UNSUPPORT:
                    //不支持错误
                    ToastUtils.showShort("不支持的错误");
                    break;
                case BaseResp.ErrCode.ERR_COMM:
                    //一般错误
                    ToastUtils.showShort("一般错误");
                    break;
                default:
                    //其他不可名状的情况
                    ToastUtils.showShort("其他错误");
                    break;
            }
        }
    }

    private static YBMWxUtil.SDKCallBack mCallBack;
    private static final WXHandler handler = new WXHandler();

    public static void setSDKCallBack(YBMWxUtil.SDKCallBack sdkCallBack) {
        mCallBack = sdkCallBack;
    }

    private static YBMPayUtil.PaySDKCallBack mPaySDKCallBack;

    public static void setPaySdkCallBack(YBMPayUtil.PaySDKCallBack paySDKCallBack) {
        mPaySDKCallBack = paySDKCallBack;
    }

    //Application异步操作初始化mApi，在不动其他逻辑的情况下，只能通过短期轮询临时解决一下。
    private final Handler mHandler = new Handler();
    private int mCounter = 3; //启动超过1.5秒失败
    private static final long SINTERVAL = 500L; //启动超过1.5秒失败
    private final Runnable mTask = new Runnable() {
        @Override
        public void run() {
            if (YBMAppLike.mApi != null) {
                try {
                    YBMAppLike.mApi.handleIntent(getIntent(), handler);
                } catch (Exception ignore) {

                }
                finish();
            } else if (--mCounter <= 0) {
                finish();
            } else {
                mHandler.postDelayed(this, SINTERVAL);
            }
        }
    };

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mHandler.postDelayed(mTask, SINTERVAL);
    }

    public static String getParamByKey(String url, String keyWord) {
        String retValue = "";
        try {
            final String charset = "utf-8";
            url = URLDecoder.decode(url, charset);
            if (url.indexOf('?') != -1) {
                final String contents = url.substring(url.indexOf('?') + 1);
                String[] keyValues = contents.split("&");
                for (String keyValue : keyValues) {
                    String key = keyValue.substring(0, keyValue.indexOf("="));
                    String value = keyValue.substring(keyValue.indexOf("=") + 1);
                    if (key.equals(keyWord)) {
                        if (!TextUtils.isEmpty(value)) {
                            retValue = value;
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return retValue;
    }
}