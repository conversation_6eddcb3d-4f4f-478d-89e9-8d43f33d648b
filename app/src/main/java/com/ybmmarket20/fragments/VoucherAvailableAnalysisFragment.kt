package com.ybmmarket20.fragments

import com.ybmmarket20.bean.VoucherListBean
import com.ybmmarket20.common.BaseFragment
import com.ybmmarket20.report.coupon.PaymentCouponCheck
import com.ybmmarket20.report.coupon.PaymentCouponExposure
import com.ydmmarket.report.ReportManager

/**
 * 选择优惠券
 */
abstract class VoucherAvailableAnalysisFragment: BaseFragment() {

    companion object {
        val SELECTE_VOUCHER_SELECTED = "1"
        val SELECTE_VOUCHER_UNSELECTED = "0"
    }

    private var mCouponIdCache: MutableSet<String> = mutableSetOf()

    /**
     * 待确认订单页优惠券曝光
     */
    fun paymentCouponExposure(voucherListBean: VoucherListBean?, position: Int) {
        if (voucherListBean == null) return
        if (mCouponIdCache.contains("${voucherListBean.voucherTemplateId}")) return
        mCouponIdCache.add("${voucherListBean.voucherTemplateId}")
        PaymentCouponExposure(
            voucherType = voucherListBean.voucherType,
            voucherTemplateId = voucherListBean.voucherTemplateId,
            voucherTitle = voucherListBean.voucherTitle,
            voucherRank = position + 1
        ).let(ReportManager.getInstance()::report)
    }

    /**
     * 待确认订单页优惠券曝光
     */
    fun paymentCouponCheck(voucherListBean: VoucherListBean?, position: Int, isTick: Boolean) {
        if (voucherListBean == null) return
        PaymentCouponCheck(
            voucherType = voucherListBean.voucherType,
            voucherTemplateId = voucherListBean.voucherTemplateId,
            voucherTitle = voucherListBean.voucherTitle,
            voucherRank = position + 1,
            isTick = if(isTick) SELECTE_VOUCHER_SELECTED else SELECTE_VOUCHER_UNSELECTED
        ).let(ReportManager.getInstance()::report)
    }
}