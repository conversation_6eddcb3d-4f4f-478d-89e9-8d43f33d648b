package com.ybmmarket20.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.EmptyBean
import com.ybmmarket20.bean.ImgCodeBean
import com.ybmmarket20.bean.PrivacyAgreementsBean
import com.ybmmarket20.bean.RegisterBeanV2
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.network.request.AccountInfoRequest
import com.ybmmarket20.network.request.RegisterRequestV2
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.SpUtil
import kotlinx.coroutines.launch

/**
 * 注册V2 ViewModel
 */
class RegisterV2ViewModel: ViewModel() {
    //注册
    private val _registerV2LiveData = MutableLiveData<BaseBean<RegisterBeanV2>>()
    val registerV2LiveData: LiveData<BaseBean<RegisterBeanV2>> = _registerV2LiveData
    //图片验证码
    private val _imgCodeLiveData = MutableLiveData<BaseBean<ImgCodeBean>>()
    val imgCodeLiveData: LiveData<BaseBean<ImgCodeBean>> = _imgCodeLiveData
    //手机二维码
    private val _phoneCodeLiveData = MutableLiveData<BaseBean<EmptyBean>>()
    val phoneCodeLiveData: LiveData<BaseBean<EmptyBean>> = _phoneCodeLiveData
    val agreementsList: ArrayList<PrivacyAgreementsBean> by lazy { ArrayList<PrivacyAgreementsBean>() }
    /**
     * 注册
     */
    fun register(mobile: String, password: String, photoCode: String, code: String, userName: String) {
        viewModelScope.launch {
            val registerBeanV2 = RegisterRequestV2().register(mobile, password, photoCode, code, userName)
            if (registerBeanV2.isSuccess) {
                SpUtil.setToken(registerBeanV2.data.token)
                SpUtil.setAccountId(registerBeanV2.data.accountId)
            }
            _registerV2LiveData.postValue(registerBeanV2)
        }
    }

    /**
     * 获取图片验证码
     */
    fun getImageCode() {
        viewModelScope.launch {
            val imageCode = RegisterRequestV2().getImageCode()
            _imgCodeLiveData.postValue(imageCode)
        }
    }

    /**
     * 获取手机验证码
     */
    fun getPhoneCode(mobileNumber: String, verifyCode: String, code: String) {
        viewModelScope.launch {
            val phoneCode = RegisterRequestV2().getPhoneCode(mobileNumber, verifyCode, code)
            _phoneCodeLiveData.postValue(phoneCode)
            if (!phoneCode.isSuccess) {
                getImageCode()
            }
        }
    }
    /**
     * 获取登录协议信息
     * type 1:隐私协议 2:用户协议 0 初始化时获取url id
     */
    fun getLoginAgreement(type: Int) {
        viewModelScope.launch {
            val loginAgreementInfo = AccountInfoRequest().getLoginAgreement()
            if (type == 1) {
                if (loginAgreementInfo.getData() != null && loginAgreementInfo.getData().privacyPolicy != null && !loginAgreementInfo.getData().privacyPolicy.url.isNullOrEmpty()) {
                    RoutersUtils.open("ybmpage://commonwebviewactivity?url=" + loginAgreementInfo.getData().privacyPolicy.url);
                } else {
                    RoutersUtils.open("ybmpage://commonwebviewactivity?url=" + AppNetConfig.PRIVACE)
                }
            } else if (type == 2) {
                if (loginAgreementInfo.getData() != null && loginAgreementInfo.getData().userServiceAgreement != null && !loginAgreementInfo.getData().userServiceAgreement.url.isNullOrEmpty()) {
                    RoutersUtils.open("ybmpage://commonwebviewactivity?url=" + loginAgreementInfo.getData().userServiceAgreement.url);
                } else {
                    RoutersUtils.open("ybmpage://commonwebviewactivity?url=" + AppNetConfig.CLAUSE)
                }
            } else if (type == 0) {
                agreementsList.clear()
                if (loginAgreementInfo.getData() != null && loginAgreementInfo.getData().userServiceAgreement != null && loginAgreementInfo.getData().userServiceAgreement.id != null) {
                    val privacyAgreementsBean = PrivacyAgreementsBean(
                        loginAgreementInfo.getData().userServiceAgreement.id,
                        loginAgreementInfo.getData().userServiceAgreement.version
                    )
                    agreementsList.add(privacyAgreementsBean)
                }
                if (loginAgreementInfo.getData() != null && loginAgreementInfo.getData().privacyPolicy != null && loginAgreementInfo.getData().privacyPolicy.id != null) {
                    val privacyAgreementsBean = PrivacyAgreementsBean(
                        loginAgreementInfo.getData().privacyPolicy.id,
                        loginAgreementInfo.getData().privacyPolicy.version
                    )
                    agreementsList.add(privacyAgreementsBean)
                }
            }
        }
    }

    fun saveLoginAgreement(paramsMap: Map<String, String>) {
        viewModelScope.launch {
            AccountInfoRequest().saveLoginAgreement(paramsMap)
        }
    }
}