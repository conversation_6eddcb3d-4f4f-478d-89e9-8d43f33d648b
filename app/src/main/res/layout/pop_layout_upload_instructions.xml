<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_4corner_ffffff"
    android:maxHeight="640dp"
    android:paddingStart="12dp"
    android:paddingEnd="12dp">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="上传说明"
        android:textColor="@color/color_292933"
        android:textSize="16sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/ivClose"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="@dimen/dimen_dp_12"
        android:padding="10dp"
        android:src="@drawable/close_icon"
        app:layout_constraintBottom_toBottomOf="@id/tv_title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_title" />

    <TextView
        android:id="@+id/tv_description"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="16dp"
        android:background="#FFF3E3"
        android:padding="8dp"
        android:text="请保持证件照位于首图（附件置于首图后），证件质量影响审核结果和发货时效，请按审核标准上传证件。"
        android:textColor="@color/color_black_000000"
        android:textSize="12sp"
        app:layout_constraintTop_toBottomOf="@id/tv_title" />

    <ScrollView
        android:id="@+id/sv_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="12dp"
        android:layout_marginBottom="20dp"
        android:fillViewport="true"
        android:scrollbars="none"
        app:layout_constraintBottom_toTopOf="@id/ll_bottom"
        app:layout_constraintHeight_default="wrap"
        app:layout_constraintTop_toBottomOf="@id/tv_description">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/iv_certificate"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:adjustViewBounds="true"
                android:scaleType="fitCenter" />

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:src="@drawable/icon_aptitude_tips" />

            <TextView
                android:id="@+id/tv_requirements"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dimen_dp_20"
                android:textColor="@color/text_color_333333"
                android:textSize="15sp"
                tools:text="1. 需上传复印件，盖清晰的企业公章；\n2. 企业名称/地址/法人/许可证编号/有效期限/发证机关章等信息清晰完整；\n3. 企业名称/地址/信用代码/法人等信息与营业执照一致；\n4. 证件需要在有效期内（不能过期）\n5. 证件四角边缘完整可见；\n6. 证件需盖一致公章；\n7. 加入正确门店，保持证件名称与加入的门店信息（名称）一致，如不一致，请切换到证件同名门店" />

        </LinearLayout>
    </ScrollView>

    <LinearLayout
        android:id="@+id/ll_bottom"
        android:layout_width="match_parent"
        android:layout_height="46dp"
        android:layout_marginTop="16dp"
        android:orientation="horizontal"
        android:paddingStart="10dp"
        android:paddingEnd="10dp"
        android:layout_marginBottom="30dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <com.ybmmarket20.common.widget.RoundTextView
            android:id="@+id/tv_album"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginEnd="17dp"
            android:layout_weight="1"
            android:gravity="center"
            android:text="相册"
            android:textColor="@color/text_color_333333"
            android:textSize="16sp"
            app:rv_backgroundColor="@color/color_white_FFFFFF"
            app:rv_cornerRadius="4dp"
            app:rv_strokeColor="@color/text_color_666666"
            app:rv_strokeWidth="0.5dp" />

        <com.ybmmarket20.common.widget.RoundTextView
            android:id="@+id/tv_camera"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:text="拍照"
            android:textColor="@color/white"
            android:textSize="16sp"
            app:rv_backgroundColor="@color/color_00B955"
            app:rv_cornerRadius="4dp" />
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>