<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dimen_dp_65"
    android:layout_marginTop="@dimen/dimen_dp_10"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@drawable/shape_back_corner_white_6dp">

    <ImageView
        android:id="@+id/iv_shop"
        android:layout_width="@dimen/dimen_dp_45"
        android:layout_height="@dimen/dimen_dp_45"
        android:layout_marginStart="@dimen/dimen_dp_10"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
    
    <TextView
        android:id="@+id/tv_shop_name"
        android:layout_width="@dimen/dimen_dp_0"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="@+id/iv_shop"
        app:layout_constraintStart_toEndOf="@+id/iv_shop"
        tools:text="淘药药旗舰店"
        android:layout_marginStart="@dimen/dimen_dp_10"
        android:layout_marginTop="@dimen/dimen_dp_3"
        app:layout_constraintEnd_toStartOf="@+id/tv_amount"
        android:textColor="@color/color_292933"
        android:textSize="@dimen/dimen_dp_14" />

    <TextView
        android:id="@+id/tv_shop_address"
        android:layout_width="@dimen/dimen_dp_0"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="@+id/iv_shop"
        app:layout_constraintStart_toEndOf="@+id/iv_shop"
        tools:text="湖南淘医药科技有限公司"
        app:layout_constraintEnd_toStartOf="@+id/tv_amount"
        android:layout_marginStart="@dimen/dimen_dp_10"
        android:layout_marginBottom="@dimen/dimen_dp_5"
        android:textColor="@color/color_676773"
        android:textSize="@dimen/dimen_dp_12" />

    <TextView
        android:id="@+id/tv_amount"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="@dimen/dimen_dp_18"
        android:textColor="#111334"
        android:text="¥2000.00"
        android:layout_marginEnd="@dimen/dimen_dp_10"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>