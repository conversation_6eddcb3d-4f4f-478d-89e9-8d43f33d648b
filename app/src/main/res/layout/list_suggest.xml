<?xml version="1.0" encoding="utf-8"?>
<com.ybmmarket20.common.widget.RoundLinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent" android:layout_height="match_parent"
    android:orientation="vertical"
    android:paddingBottom="1dp"
    android:paddingTop="0.5dp"
    app:rv_strokeColor="@color/keyboard_padding"
    app:rv_strokeWidth="1dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:orientation="vertical">

        <ListView
            android:id="@+id/listView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/white"
            android:cacheColorHint="@color/no_color"
            android:divider="@color/color_F5F5F5"
            android:dividerHeight="1dp"
            android:headerDividersEnabled="false"
            android:overScrollMode="never"
            android:paddingLeft="12dp"
            android:scrollbars="none">

        </ListView>
    </LinearLayout>
</com.ybmmarket20.common.widget.RoundLinearLayout>