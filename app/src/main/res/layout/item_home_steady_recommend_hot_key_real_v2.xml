<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:background="@color/white"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tv_hot_key"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dimen_dp_22"
        android:layout_marginStart="@dimen/dimen_dp_8"
        android:background="@drawable/shape_home_steady_recommend_hot_key_real_v2"
        android:gravity="center"
        android:paddingStart="@dimen/dimen_dp_7"
        android:paddingEnd="@dimen/dimen_dp_7"
        android:textColor="@color/color_676773"
        android:textSize="@dimen/dimen_dp_12"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="阿莫西林" />


</androidx.constraintlayout.widget.ConstraintLayout>