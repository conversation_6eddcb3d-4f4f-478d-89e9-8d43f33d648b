<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center"
    android:orientation="vertical"
    android:paddingLeft="5dp">

    <!--增加自己的固定布局-->
    <com.ybmmarket20.view.HorizontalScrollViewLoadMore
        android:id="@+id/sl"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:fillViewport="true"
        android:overScrollMode="never"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <com.ybmmarket20.view.MyGridView
                android:id="@+id/cheap_gv"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:listSelector="@null"
                android:scrollbars="none"
                android:stretchMode="columnWidth" />

            <ImageView
                android:id="@+id/iv_more"
                android:layout_width="27dp"
                android:layout_height="match_parent"
                android:src="@drawable/load_more" />
        </LinearLayout>

    </com.ybmmarket20.view.HorizontalScrollViewLoadMore>

    <View
        android:id="@+id/view_indicator_bg"
        android:layout_width="30dp"
        android:layout_height="3dp"
        android:layout_marginTop="10dp"
        android:background="@drawable/indicator_bg"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/sl" />

    <View
        android:id="@+id/view_indicator_before"
        android:layout_width="20dp"
        android:layout_height="3dp"
        android:layout_marginTop="10dp"
        android:background="@drawable/indicator_before"
        app:layout_constraintLeft_toLeftOf="@id/view_indicator_bg"
        app:layout_constraintTop_toBottomOf="@id/sl" />


</androidx.constraintlayout.widget.ConstraintLayout>