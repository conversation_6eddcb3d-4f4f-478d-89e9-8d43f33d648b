<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clipChildren="false">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="60dp"
        android:background="#ffffff"
        android:orientation="vertical">

        <include
            android:id="@+id/cl_title_bar"
            layout="@layout/layout_spell_group_recommend_selected_goods_title_bar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <include
            android:id="@+id/cl_header"
            layout="@layout/layout_spell_group_recommend_selected_goods_header"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="#eeeeee" />


        <include
            android:id="@+id/cl_content"
            layout="@layout/layout_spell_group_recommend_selected_goods_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1" />


        <View
            android:layout_width="0dp"
            android:layout_height="0.5dp"
            android:background="#eeeeee" />


    </LinearLayout>

    <include
        android:id="@+id/ll_carts_detail"
        layout="@layout/layout_spell_group_recommend_selected_goods_cart_detail"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="bottom"
        android:layout_marginBottom="60dp"
        android:visibility="gone" />

    <!--    <include-->
    <!--        layout="@layout/layout_spell_group_recommend_selected_goods_cart"-->
    <!--        android:layout_width="match_parent"-->
    <!--        android:layout_height="60dp"-->
    <!--        android:layout_gravity="bottom" />-->

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_cart"
        android:layout_width="match_parent"
        android:layout_height="70dp"
        android:layout_gravity="bottom"
        android:clipChildren="false">

        <View
            android:layout_width="match_parent"
            android:layout_height="60dp"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_marginTop="10dp"
            android:background="#ffffff"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toRightOf="parent"/>


        <View
            android:id="@+id/v_cart_bg"
            android:layout_width="0dp"
            android:layout_marginTop="10dp"
            android:layout_height="50dp"
            android:background="@drawable/bg_spell_group_cart"
            android:layout_marginLeft="9.5dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@+id/tv_settle"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.6" />

        <TextView
            android:id="@+id/tv_settle"
            android:layout_width="90dp"
            android:layout_height="50dp"
            android:background="@drawable/bg_spell_group_settle"
            android:gravity="center"
            android:text="去结算"
            android:textColor="#ffffff"
            android:textSize="16sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="@id/v_cart_bg"
            android:layout_marginRight="9.5dp"
            app:layout_constraintLeft_toRightOf="@id/v_cart_bg"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@+id/v_cart_bg" />


        <ImageView
            android:id="@+id/iv_cart_icon"
            android:layout_width="45dp"
            android:layout_height="58.6dp"
            android:layout_marginLeft="20dp"
            android:layout_marginBottom="5dp"
            android:scaleType="centerCrop"
            android:src="@drawable/icon_spell_group_cart_icon"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="@id/v_cart_bg" />


        <TextView
            android:id="@+id/tv_cart_count"
            android:layout_width="0dp"
            android:layout_height="18dp"
            android:background="@drawable/bg_spell_group_cart_count"
            android:ellipsize="end"
            android:gravity="center"
            android:includeFontPadding="false"
            android:singleLine="true"
            android:textColor="#ffffff"
            android:textSize="10sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="@id/v_cart_bg"
            app:layout_constraintDimensionRatio="h,1:1"
            app:layout_constraintLeft_toRightOf="@id/iv_cart_icon"
            app:layout_constraintRight_toRightOf="@id/iv_cart_icon"
            app:layout_constraintTop_toTopOf="@id/v_cart_bg"
            tools:text="99+" />


        <TextView
            android:id="@+id/tv_cart_price"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="19dp"
            android:ellipsize="end"
            android:includeFontPadding="false"
            android:singleLine="true"
            android:textColor="#ffffff"
            android:textSize="18sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toTopOf="@+id/tv_cart_tips"
            app:layout_constraintLeft_toRightOf="@id/iv_cart_icon"
            app:layout_constraintTop_toTopOf="@id/v_cart_bg"
            app:layout_constraintVertical_chainStyle="packed"
            tools:text="￥1239.20" />


        <TextView
            android:id="@+id/tv_cart_tips"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:includeFontPadding="false"
            android:text="无需运费，购买即可包邮配送"
            android:layout_marginBottom="5dp"
            android:textColor="#ffffff"
            android:textSize="11sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="@id/tv_cart_price"
            app:layout_constraintTop_toBottomOf="@id/tv_cart_price" />


        <TextView
            android:id="@+id/tv_cart_count_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="6dp"
            android:ellipsize="end"
            android:singleLine="true"
            android:textColor="#9affffff"
            android:textSize="11sp"
            app:layout_constraintBaseline_toBaselineOf="@id/tv_cart_price"
            app:layout_constraintLeft_toRightOf="@id/tv_cart_price"
            app:layout_constraintRight_toLeftOf="@id/tv_settle"
            tools:text="共10件商品" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>