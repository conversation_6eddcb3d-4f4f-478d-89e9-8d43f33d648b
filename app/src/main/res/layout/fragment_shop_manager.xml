<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/hintTv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#FFF7EF"
        android:paddingLeft="20dp"
        android:paddingTop="10dp"
        android:paddingRight="20dp"
        android:paddingBottom="10dp"
        android:text="如果您已关联多个店铺，可以在此管理维护"
        android:textColor="#99664d"
        android:textSize="@dimen/dimen_dp_14"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_0"
        app:layout_constraintBottom_toTopOf="@+id/divider"
        app:layout_constraintTop_toBottomOf="@+id/hintTv" />

    <View
        android:id="@+id/divider"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_1"
        android:background="@color/colors_DDDDDD"
        android:layout_marginBottom="@dimen/dimen_dp_8"
        app:layout_constraintBottom_toTopOf="@+id/spaceBottom" />

    <Space
        android:id="@+id/spaceBottom"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_44"
        android:layout_marginBottom="@dimen/dimen_dp_8"
        app:layout_constraintBottom_toBottomOf="parent" />

    <TextView
        android:id="@+id/tvEdit"
        android:layout_width="@dimen/dimen_dp_0"
        android:layout_height="@dimen/dimen_dp_44"
        android:text="编辑"
        android:gravity="center"
        android:textColor="@color/color_00b377"
        android:textSize="@dimen/dimen_dp_18"
        android:background="@drawable/shape_button_quite"
        android:layout_marginStart="@dimen/dimen_dp_15"
        android:layout_marginEnd="@dimen/dimen_dp_5"
        android:layout_marginBottom="@dimen/dimen_dp_8"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/tvOtherShop"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toStartOf="parent" />

    <TextView
        android:id="@+id/tvOtherShop"
        android:layout_width="@dimen/dimen_dp_0"
        android:layout_height="@dimen/dimen_dp_44"
        android:text="添加关联店铺"
        android:gravity="center"
        android:textColor="@color/white"
        android:textSize="@dimen/dimen_dp_18"
        android:background="@drawable/shape_button_other_shop"
        android:layout_marginStart="@dimen/dimen_dp_5"
        android:layout_marginEnd="@dimen/dimen_dp_15"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toEndOf="@+id/tvEdit"
        app:layout_constraintTop_toTopOf="@+id/tvEdit" />

    <TextView
        android:id="@+id/tvFinish"
        android:layout_width="@dimen/dimen_dp_0"
        android:layout_height="@dimen/dimen_dp_44"
        android:text="完成"
        android:gravity="center"
        android:textColor="@color/white"
        android:textSize="@dimen/dimen_dp_18"
        android:background="@drawable/shape_button_other_shop"
        android:layout_marginStart="@dimen/dimen_dp_15"
        android:layout_marginEnd="@dimen/dimen_dp_15"
        android:layout_marginBottom="@dimen/dimen_dp_8"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toStartOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>