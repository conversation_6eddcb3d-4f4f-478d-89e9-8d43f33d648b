<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
       xmlns:app="http://schemas.android.com/apk/res-auto"
       xmlns:tools="http://schemas.android.com/tools"
       android:layout_width="match_parent"
       android:layout_height="match_parent"
       android:orientation="vertical">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="match_parent"
        android:layout_height="30dp"
        android:gravity="center"
        android:text="动态布局标题"
        android:textColor="#292933"
        android:textSize="14sp"/>
    <!--增加自己的固定布局-->
    <com.ybmmarket20.common.widget.RoundLinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="6dp"
        app:rv_backgroundColor="@color/white"
        app:rv_cornerRadius="6dp">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="12dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="套餐价："
                android:textColor="#FF292933"
                android:textSize="16sp"/>

            <TextView
                android:id="@+id/tv_price"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text=""
                android:textColor="#FFFF0000"
                android:textSize="20sp"/>

            <TextView
                android:id="@+id/tv_old_price"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="8dp"
                android:text=""
                android:textColor="#FF676773"
                android:textSize="14sp"/>
        </LinearLayout>

        <com.ybmmarket20.common.widget.RoundLinearLayout
            android:id="@+id/ll_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginBottom="8dp"
            android:layout_marginTop="8dp"
            android:paddingBottom="6dp"
            android:paddingLeft="20dp"
            android:paddingRight="20dp"
            android:paddingTop="6dp"
            android:visibility="gone"
            app:rv_backgroundColor="#FFF7E8"
            app:rv_cornerRadius="3dp"
            tools:visibility="visible">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="距离开抢还剩:"
                android:textColor="#ED6464"
                android:textSize="13dp"/>

            <TextView
                android:id="@+id/tv_day"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="12"
                android:textColor="#ED6464"
                android:textSize="13dp"/>
            <TextView
                android:id="@+id/tv_dot_day"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="天"
                android:textColor="#ED6464"
                android:textSize="13dp"/>
            <TextView
                android:id="@+id/tv_hour"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="12"
                android:textColor="#ED6464"
                android:textSize="13dp"/>
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="时"
                android:textColor="#ED6464"
                android:textSize="13dp"/>
            <TextView
                android:id="@+id/tv_minute"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="50"
                android:textColor="#ED6464"
                android:textSize="13dp"/>
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="分"
                android:textColor="#ED6464"
                android:textSize="13dp"/>
            <TextView
                android:id="@+id/tv_second"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="50"
                android:textColor="#ED6464"
                android:textSize="13dp"/>
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="秒"
                android:textColor="#ED6464"
                android:textSize="13dp"/>
        </com.ybmmarket20.common.widget.RoundLinearLayout>

        <com.ybmmarket20.view.ProductEditLayout
            android:id="@+id/pel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginBottom="8dp"
            android:layout_marginTop="8dp">

        </com.ybmmarket20.view.ProductEditLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_list"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scrollbars="none"/>
    </com.ybmmarket20.common.widget.RoundLinearLayout>

</merge>