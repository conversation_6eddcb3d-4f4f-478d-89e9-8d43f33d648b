<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/ll_product_item"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">


    <ImageView
        android:id="@+id/iv_product"
        android:layout_width="84dp"
        android:layout_height="84dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/logo" />

    <View
        android:layout_width="@dimen/dimen_dp_84"
        android:layout_height="@dimen/dimen_dp_84"
        android:background="@color/translucent_black_97"
        app:layout_constraintLeft_toLeftOf="@id/iv_product"
        app:layout_constraintRight_toRightOf="@id/iv_product"
        app:layout_constraintTop_toTopOf="@id/iv_product" />


    <TextView
        android:id="@+id/tv_price"
        android:layout_width="@dimen/dimen_dp_84"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dimen_dp_5"
        android:textColor="@color/color_ff2121"
        android:textSize="@dimen/dimen_dp_10"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="@id/iv_product"
        app:layout_constraintEnd_toEndOf="@+id/iv_product"
        app:layout_constraintTop_toBottomOf="@id/iv_product"
        android:singleLine="true"
        tools:text="16161616161616161616.7" />

    <TextView
        android:id="@+id/tv_price_after_discount"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dimen_dp_2"
        android:textColor="@color/color_ff2121"
        android:textSize="@dimen/dimen_dp_12"
        android:visibility="gone"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="@id/iv_product"
        app:layout_constraintTop_toBottomOf="@id/tv_price"
        tools:text="折后价约11.65"
        tools:visibility="visible" />

    <!-- 拼团 -->
    <ImageView
        android:id="@+id/iv_title_tag"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_16"
        android:scaleType="fitXY"
        android:src="@drawable/icon_spell_group_goods_home_red"
        android:layout_marginStart="@dimen/dimen_dp_20"
        android:layout_marginEnd="@dimen/dimen_dp_20"
        app:layout_constraintBottom_toBottomOf="@+id/iv_yellow"
        app:layout_constraintStart_toStartOf="parent"/>

    <TextView
        android:id="@+id/tv_price_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="拼团价"
        android:textColor="@color/white"
        android:textSize="@dimen/dimen_dp_8"
        android:layout_marginBottom="@dimen/dimen_dp_3"
        app:layout_constraintBottom_toBottomOf="@+id/iv_title_tag"
        app:layout_constraintEnd_toStartOf="@+id/iv_yellow"
        app:layout_constraintStart_toStartOf="@+id/iv_title_tag" />

    <ImageView
        android:id="@+id/iv_yellow"
        android:layout_width="@dimen/dimen_dp_50"
        android:layout_height="@dimen/dimen_dp_18"
        android:src="@drawable/icon_spell_group_goods_home_yellow"
        android:scaleType="fitXY"
        android:layout_marginEnd="@dimen/dimen_dp_20"
        android:layout_marginTop="@dimen/dimen_dp_11"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/iv_product" />

    <TextView
        android:id="@+id/tvSpellPrice"
        android:layout_width="@dimen/dimen_dp_0"
        android:layout_height="wrap_content"
        android:text="￥26.70"
        android:textColor="#FE2021"
        android:textSize="@dimen/dimen_dp_7"
        android:maxLines="1"
        android:ellipsize="end"
        android:layout_marginStart="@dimen/dimen_dp_3"
        app:layout_constraintBottom_toBottomOf="@id/iv_yellow"
        app:layout_constraintEnd_toEndOf="@+id/iv_yellow"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toStartOf="@+id/iv_yellow"
        app:layout_constraintTop_toTopOf="@+id/iv_yellow" />


    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupSpellPrice"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="iv_title_tag, tv_price_title, iv_yellow, tvSpellPrice" />

</androidx.constraintlayout.widget.ConstraintLayout>