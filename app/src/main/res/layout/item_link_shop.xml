<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_item_link_shop"
    android:layout_width="match_parent"
    android:layout_height="69dp"
    android:background="#ffffffff"
    android:padding="15dp">

    <TextView
        android:id="@+id/tv_shop_name"
        android:layout_width="wrap_content"
        android:layout_height="21dp"
        android:textColor="#292933"
        android:textSize="15dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="武汉康城美汇大药房有限公司" />

    <TextView
        android:id="@+id/tv_shop_address"
        android:layout_width="wrap_content"
        android:layout_height="16.5dp"
        android:layout_marginTop="4dp"
        android:textColor="#676773"
        android:textSize="12dp"
        android:ellipsize="end"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_shop_name"
        tools:text="湖北省武汉市洪山区东湖高薪技术开发区" />

    <ImageView
        android:id="@+id/iv_selected"
        android:layout_width="20dp"
        android:layout_height="13dp"
        android:background="@drawable/shop_selected"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout>


