<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <com.ybmmarket20.view.CommonSearchView
        android:id="@+id/common_search_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <com.flyco.tablayout.CommonTabLayout
        android:id="@+id/tabLayout"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:layout_centerHorizontal="true"
        android:background="@color/white"
        android:visibility="gone"
        app:tl_indicator_color="@color/color_00b955"
        app:tl_indicator_corner_radius="2dp"
        app:tl_indicator_height="@dimen/dp_4"
        app:tl_indicator_margin_bottom="0dp"
        app:tl_indicator_width="34dp"
        app:tl_indicator_width_equal_title="false"
        app:tl_tab_padding="10dp"
        app:tl_tab_space_equal="true"
        app:tl_textBold="SELECT"
        app:tl_textSelectColor="@color/color_00b955"
        app:tl_textSelectSize="@dimen/dimen_dp_15"
        app:tl_textUnselectColor="@color/color_292933"
        app:tl_textsize="15dp"
        app:tl_indicator_anim_enable="false"
        tools:visibility="visible" />

    <View
        android:id="@+id/tabDivider"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_1"
        android:visibility="gone"
        tools:visibility="visible"
        android:background="@color/colors_f5f5f5" />

    <FrameLayout
        android:id="@+id/fl_container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

</LinearLayout>