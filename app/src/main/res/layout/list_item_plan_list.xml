<?xml version="1.0" encoding="utf-8"?>
<com.ybmmarket20.view.SwipeMenuLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="66dp"
    android:background="@color/white">

    <RelativeLayout
        android:id="@+id/rl_content"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:paddingLeft="12dp"
        android:paddingRight="12dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_toLeftOf="@+id/tv_kind_num"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_plan_list_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="#292933"
                    android:textSize="16sp"
                    tools:text="电子计划单" />

                <TextView
                    android:id="@+id/tv_plan_update_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:textColor="#AAAAAA"
                    android:textSize="12sp"
                    tools:text="2017-05-12" />
            </LinearLayout>

            <TextView
                android:id="@+id/tv_kind_num"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:drawableRight="@drawable/integral_market_01"
                android:textColor="#9494A6"
                android:textSize="14sp"
                tools:text="177种" />

    </RelativeLayout>

    <TextView
        android:id="@+id/tv_delete"
        android:layout_width="60dp"
        android:layout_height="match_parent"
        android:background="#FF624A"
        android:gravity="center"
        android:text="删除"
        android:textColor="@android:color/white"
        android:textSize="14sp" />

    <TextView
        android:id="@+id/tv_rename"
        android:layout_width="60dp"
        android:layout_height="match_parent"
        android:background="#FBCB32"
        android:gravity="center"
        android:text="重命名"
        android:textColor="@android:color/white"
        android:textSize="14sp" />

    <TextView
        android:id="@+id/tv_share"
        android:layout_width="60dp"
        android:layout_height="match_parent"
        android:background="#55A0FF"
        android:gravity="center"
        android:text="转发"
        android:textColor="@android:color/white"
        android:textSize="14sp" />

</com.ybmmarket20.view.SwipeMenuLayout>