<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:paddingStart="@dimen/dimen_dp_10"
    android:paddingEnd="@dimen/dimen_dp_10"
    android:background="@color/white"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dimen_dp_50">

    <TextView
        android:id="@+id/tvGoodsTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dimen_dp_10"
        android:drawablePadding="@dimen/dimen_dp_4"
        android:text="商品相关资质"
        android:textStyle="bold"
        android:textColor="@color/color_292933"
        android:textSize="@dimen/dimen_dp_14"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.ybmmarket20.common.widget.RoundFrameLayout
        android:id="@+id/rflBg"
        android:layout_width="@dimen/dimen_dp_0"
        android:layout_height="@dimen/dimen_dp_30"
        android:layout_marginStart="@dimen/dimen_dp_10"
        app:layout_constraintBottom_toBottomOf="@+id/tvGoodsTitle"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toEndOf="@+id/tvGoodsTitle"
        app:layout_constraintTop_toTopOf="@+id/tvGoodsTitle"
        app:rv_backgroundColor="@color/color_F5F5F5"
        app:rv_cornerRadius="2dp" />

    <ImageView
        android:id="@+id/ivSearchIcon"
        android:layout_width="@dimen/dimen_dp_18"
        android:layout_height="@dimen/dimen_dp_18"
        android:layout_marginStart="@dimen/dimen_dp_5"
        android:src="@drawable/icon_search_license_goods"
        app:layout_constraintBottom_toBottomOf="@+id/rflBg"
        app:layout_constraintStart_toStartOf="@+id/rflBg"
        app:layout_constraintTop_toTopOf="@+id/rflBg" />

    <EditText
        android:id="@+id/etLicense"
        android:layout_width="@dimen/dimen_dp_0"
        android:layout_height="wrap_content"
        android:hint="输入商品名称搜索"
        android:textSize="@dimen/dimen_dp_13"
        android:background="@color/transparent"
        android:layout_marginStart="@dimen/dimen_dp_5"
        android:layout_marginEnd="@dimen/dimen_dp_35"
        app:layout_constraintBottom_toBottomOf="@+id/rflBg"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/ivSearchIcon"
        app:layout_constraintTop_toTopOf="@+id/rflBg" />

    <ImageView
        android:id="@+id/ivEditClose"
        android:layout_width="@dimen/dimen_dp_22"
        android:layout_height="@dimen/dimen_dp_22"
        android:src="@drawable/icon_license_close"
        android:visibility="gone"
        android:layout_marginEnd="@dimen/dimen_dp_10"
        app:layout_constraintBottom_toBottomOf="@+id/rflBg"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/rflBg"
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout>