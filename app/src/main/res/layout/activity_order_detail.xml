<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <include layout="@layout/common_header_items" />

        <androidx.core.widget.NestedScrollView
            android:id="@+id/order_detail_sv"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:background="@color/base_bg_color"
            android:scrollbars="none">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <!--订单画报-->
                <LinearLayout
                    android:id="@+id/ll_order_pictorial"
                    android:layout_width="match_parent"
                    android:layout_height="98dp"
                    android:layout_margin="10dp"
                    android:focusable="true"
                    android:focusableInTouchMode="true"
                    android:gravity="center_vertical"
                    android:orientation="vertical"
                    tools:background="@drawable/icon_order_payment"
                    tools:visibility="visible">

                    <TextView
                        android:id="@+id/tv_order_state"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="15dp"
                        android:textColor="@color/white"
                        android:textSize="22dp"
                        android:textStyle="bold"
                        tools:text="待收货" />

                    <LinearLayout
                        android:id="@+id/ll_unpay_layout"
                        android:layout_width="wrap_content"
                        android:layout_height="20dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="@dimen/dimen_dp_15"
                        android:layout_marginTop="@dimen/dimen_dp_5"
                        android:background="@drawable/bg_order_detail_unpay"
                        android:orientation="horizontal"
                        android:padding="@dimen/dimen_dp_1"
                        android:visibility="gone"
                        tools:visibility="visible">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:background="@drawable/bg_order_detail_unpay_time_title"
                            android:gravity="center"
                            android:paddingLeft="@dimen/dimen_dp_5"
                            android:paddingRight="@dimen/dimen_dp_10"
                            android:text="剩余支付时间"
                            android:textColor="@color/white"
                            android:textSize="13dp" />

                        <TextView
                            android:id="@+id/tv_unpay_time_countdown"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:background="@drawable/bg_order_detail_unpay_time_content"
                            android:gravity="center"
                            android:maxWidth="120dp"
                            android:minWidth="100dp"
                            android:paddingLeft="5dp"
                            android:paddingRight="5dp"
                            android:textColor="#FFF75F4F"
                            android:textSize="13dp"
                            tools:text="1天22小时56分" />

                    </LinearLayout>

                    <TextView
                        android:id="@+id/tv_delivery_time"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="15dp"
                        android:textColor="@color/white"
                        android:textSize="15dp"
                        android:visibility="gone"
                        tools:text="预计送达: 2018-12-21"
                        tools:visibility="gone" />

                    <com.ybmmarket20.common.widget.RoundTextView
                        android:id="@+id/tv_view_refund"
                        android:layout_width="100dp"
                        android:layout_height="30dp"
                        android:layout_marginLeft="15dp"
                        android:layout_marginTop="5dp"
                        android:gravity="center"
                        android:text="查看退款/售后"
                        android:textColor="@color/white"
                        android:textSize="14dp"
                        android:visibility="gone"
                        app:rv_cornerRadius="2dp"
                        app:rv_strokeColor="@color/white"
                        app:rv_strokeWidth="1dp"
                        tools:visibility="gone" />

                </LinearLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/clSystemException"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:layout_marginRight="10dp"
                    android:background="@drawable/bg_corner_all"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <com.ybmmarket20.view.OrderItemAptitudeView
                        android:id="@+id/aptitudeView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dimen_dp_10"
                        android:layout_marginEnd="@dimen/dimen_dp_5"
                        android:layout_marginStart="@dimen/dimen_dp_5"
                        app:layout_constraintTop_toTopOf="parent" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <Space
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginLeft="10dp"
                    android:layout_marginRight="10dp" />

                <!--物流-->
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/cl_logistics"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:layout_marginRight="10dp"
                    android:background="@drawable/bg_corner_all"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <ImageView
                        android:id="@+id/iv_logistics_car"
                        android:layout_width="22dp"
                        android:layout_height="22dp"
                        android:layout_marginLeft="10dp"
                        android:layout_marginRight="7dp"
                        android:src="@drawable/icon_logistics_bull_car"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/tv_content"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="9dp"
                        android:layout_marginTop="21dp"
                        android:layout_marginRight="10dp"
                        android:drawableRight="@drawable/common_more"
                        android:textColor="@color/text_292933"
                        android:textSize="15dp"
                        android:textStyle="bold"
                        app:layout_constraintLeft_toRightOf="@id/iv_logistics_car"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:text="您的订单在顺丰【江夏区分拣中心】发货完成，准备送往顺丰【东西湖分拣中心】" />

                    <TextView
                        android:id="@+id/tv_time"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="9dp"
                        android:layout_marginTop="5dp"
                        android:layout_marginRight="10dp"
                        android:layout_marginBottom="10dp"
                        android:textColor="@color/text_9494A6"
                        android:textSize="12dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintLeft_toRightOf="@id/iv_logistics_car"
                        app:layout_constraintTop_toBottomOf="@id/tv_content"
                        tools:text="2018-08-20 11:24:00" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <Space
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginLeft="10dp"
                    android:layout_marginRight="10dp" />

                <!--新地址-->
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/ll_address"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:layout_marginRight="10dp"
                    android:background="@drawable/bg_corner_all">

                    <!--未上传凭证时的提示语-->
                    <com.ybmmarket20.common.widget.RoundConstraintLayout
                        android:id="@+id/cl_no_upload_voucher_tips"
                        android:layout_width="match_parent"
                        android:layout_height="28dp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:rv_backgroundColor="@color/color_FEF5F4"
                        app:rv_cornerRadius="@dimen/dimen_dp_2"
                        android:visibility="gone"
                        android:layout_marginTop="10dp"
                        android:layout_marginHorizontal="10dp"
                        app:rv_strokeColor="@color/color_FFD6D7"
                        app:rv_strokeWidth="@dimen/dimen_dp_0_5">

                        <TextView
                            android:id="@+id/tv_tips_1"
                            android:text="提示"
                            android:textColor="@color/color_ff2121"
                            android:textSize="11dp"
                            android:textStyle="bold"
                            android:gravity="center"
                            android:layout_marginStart="6dp"
                            android:padding="1dp"
                            android:background="@drawable/shape_ff2121_2dp_transparent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintBottom_toBottomOf="parent"
                            android:layout_width="28dp"
                            android:layout_height="wrap_content"/>

                        <TextView
                            android:id="@+id/tv_no_upload_voucher_tips"
                            app:layout_constraintStart_toEndOf="@id/tv_tips_1"
                            android:layout_marginStart="6dp"
                            android:text="请在剩余支付时间内完成汇款并上传凭证"
                            android:textSize="12dp"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintBottom_toBottomOf="parent"
                            android:textColor="@color/color_ff2121"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"/>

                    </com.ybmmarket20.common.widget.RoundConstraintLayout>

                    <!--已上传凭证 但未审核时的提示语-->
                    <com.ybmmarket20.common.widget.RoundConstraintLayout
                        android:id="@+id/cl_upload_voucher_tips"
                        android:layout_width="match_parent"
                        android:layout_height="28dp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:rv_backgroundColor="@color/color_FFFAF2"
                        app:rv_cornerRadius="@dimen/dimen_dp_2"
                        android:visibility="gone"
                        android:layout_marginTop="10dp"
                        android:layout_marginHorizontal="10dp"
                        tools:visibility="visible"
                        app:rv_strokeColor="@color/color_FFE9CA"
                        app:rv_strokeWidth="@dimen/dimen_dp_0_5">

                        <TextView
                            android:id="@+id/tv_tips_2"
                            android:text="提示"
                            android:textColor="@color/colors_FFA620"
                            android:textSize="11dp"
                            android:textStyle="bold"
                            android:gravity="center"
                            android:layout_marginStart="6dp"
                            android:padding="1dp"
                            android:background="@drawable/shape_f6a32e_2dp_transparent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintBottom_toBottomOf="parent"
                            android:layout_width="28dp"
                            android:layout_height="wrap_content"/>

                        <TextView
                            android:id="@+id/tv_upload_voucher_tips"
                            app:layout_constraintStart_toEndOf="@id/tv_tips_2"
                            android:layout_marginStart="6dp"
                            android:text="电汇凭证已上传，请等待商家审核凭证"
                            android:textSize="12dp"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintBottom_toBottomOf="parent"
                            android:textColor="@color/colors_FFA620"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"/>

                    </com.ybmmarket20.common.widget.RoundConstraintLayout>

                    <androidx.constraintlayout.widget.Barrier
                        android:id="@+id/barrier_tips"
                        app:constraint_referenced_ids="cl_no_upload_voucher_tips,cl_upload_voucher_tips"
                        app:barrierDirection="bottom"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"/>

                    <ImageView
                        android:id="@+id/iv_address"
                        android:layout_width="22dp"
                        android:layout_height="22dp"
                        android:layout_marginLeft="10dp"
                        android:layout_marginRight="7dp"
                        android:src="@drawable/icon_order_green_address"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/barrier_tips" />

                    <TextView
                        android:id="@+id/tv_name"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="9dp"
                        android:layout_marginTop="10dp"
                        app:layout_goneMarginTop="10dp"
                        android:layout_marginRight="10dp"
                        android:textColor="@color/text_292933"
                        android:textSize="14dp"
                        app:layout_constraintLeft_toRightOf="@id/iv_address"
                        app:layout_constraintTop_toBottomOf="@id/barrier_tips"
                        tools:text="健康人大药房" />

                    <TextView
                        android:id="@+id/tv_phone"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="10dp"
                        android:text=""
                        android:textColor="@color/text_292933"
                        android:textSize="14dp"
                        app:layout_constraintBaseline_toBaselineOf="@id/tv_name"
                        app:layout_constraintLeft_toRightOf="@id/tv_name"
                        tools:text="188****8898" />


                    <TextView
                        android:id="@+id/tv_address"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="9dp"
                        android:layout_marginTop="5dp"
                        android:layout_marginRight="10dp"
                        android:layout_marginBottom="9dp"
                        android:ellipsize="end"
                        android:maxLines="2"
                        android:textColor="@color/text_676773"
                        android:textSize="12sp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintLeft_toRightOf="@id/iv_address"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/tv_name"
                        tools:text="这里是收货地址，这里是收货地址，这里是收货地址，这里是收货地址，这里是收货地址，这里是收货地址，这里是收货地址，最多可以展示两行超两行的情况下用省略号" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginLeft="10dp"
                    android:layout_marginRight="10dp"
                    android:background="@color/color_F5F5F5" />

                <com.ybmmarket20.common.widget.RoundTextView
                    android:id="@+id/mOpenAccountRemindTv"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:layout_marginRight="10dp"
                    android:paddingLeft="10dp"
                    android:paddingTop="5dp"
                    android:paddingBottom="5dp"
                    android:text="您在以下商户中未开户，请阅读开户流程，提前准备好资料"
                    android:textColor="@color/colors_99664D"
                    android:textSize="14sp"
                    android:visibility="gone"
                    app:rv_backgroundColor="@color/colors_fff7ef"
                    tools:visibility="visible" />

                <!--未开户引导-->
                <com.ybmmarket20.common.widget.RoundLinearLayout
                    android:id="@+id/mOpenAccountLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:layout_marginRight="10dp"
                    android:orientation="vertical"
                    android:paddingLeft="10dp"
                    android:paddingTop="12dp"
                    android:paddingRight="12dp"
                    android:paddingBottom="12dp"
                    android:visibility="gone"
                    app:rv_backgroundColor="@color/white"
                    app:rv_cornerRadius="2dp"
                    tools:visibility="gone">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/mStoreName"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginRight="10dp"
                            android:layout_weight="1"
                            android:textColor="@color/text_676773"
                            android:textSize="15sp"
                            tools:text="广州医药集团有限公司" />

                        <TextView
                            android:id="@+id/mOpenAccountTv"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:drawableRight="@drawable/right"
                            android:gravity="center_vertical"
                            android:text="查看开户流程"
                            android:textColor="@color/text_9494A6"
                            android:textSize="13sp" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/mStep1Layout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/mStep1Tv"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="1.先把证件扫描或拍照上传"
                            android:textColor="@color/text_676773"
                            android:textSize="13sp" />

                        <TextView
                            android:id="@+id/mAptitudeTv"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:drawableRight="@drawable/right"
                            android:gravity="center_vertical"
                            android:text="我的资质信息"
                            android:textColor="@color/text_9494A6"
                            android:textSize="13sp" />

                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/mStep2Layout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/mStep2Tv"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginRight="10dp"
                            android:layout_weight="1"
                            android:textColor="@color/text_676773"
                            android:textSize="13sp" />

                        <TextView
                            android:id="@+id/mMailCertificateTv"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:drawableRight="@drawable/right"
                            android:gravity="center_vertical"
                            android:text="上传邮寄凭证"
                            android:textColor="@color/text_9494A6"
                            android:textSize="13sp" />
                    </LinearLayout>

                </com.ybmmarket20.common.widget.RoundLinearLayout>

                <!--余额使用-->
                <LinearLayout
                    android:id="@+id/ll_balance"
                    style="@style/order_detail_item_layout"
                    android:layout_width="match_parent"
                    android:layout_marginLeft="10dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginRight="10dp"
                    android:background="@drawable/bg_corner_all">

                    <TextView
                        android:id="@+id/tv_balance_desc"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:drawableLeft="@drawable/icon_receive"
                        android:drawablePadding="8dp"
                        android:gravity="center_vertical"
                        android:textColor="@color/text_292933"
                        android:textSize="14sp"
                        tools:text="100" />

                    <TextView
                        android:id="@+id/btn_order_balance"
                        android:layout_width="80dp"
                        android:layout_height="30dp"
                        android:layout_marginLeft="10dp"
                        android:background="@drawable/order_green_border"
                        android:gravity="center"
                        android:text="领取余额"
                        android:textColor="@color/base_colors"
                        android:textSize="14sp"
                        android:visibility="gone"
                        tools:visibility="visible" />

                </LinearLayout>


                <!--订单信息-->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginRight="10dp"
                    android:background="@drawable/bg_corner_all"
                    android:orientation="vertical"
                    tools:visibility="visible">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="44dp"
                        android:gravity="center_vertical"
                        android:paddingLeft="10dp"
                        android:text="订单信息"
                        android:textColor="@color/text_292933"
                        android:textSize="15sp"
                        android:textStyle="bold" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="@color/color_F5F5F5" />

                    <!--支付方式-->
                    <LinearLayout
                        style="@style/order_detail_item_layout"
                        android:layout_width="match_parent">

                        <TextView
                            android:id="@+id/tv_payway"
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_weight="1"
                            android:gravity="center_vertical"
                            android:textColor="@color/text_676773"
                            android:textSize="13sp"
                            tools:text="支付方式" />

                        <TextView
                            android:id="@+id/tv_payway_desc"
                            style="@style/payment_item_layout_text_left"
                            android:text="查看电汇凭证"
                            android:textColor="@color/text_292933"
                            android:textSize="12sp"
                            android:visibility="gone"
                            tools:visibility="visible" />
                    </LinearLayout>

                    <!--订单编号-->
                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="38.5dp"
                        android:layout_marginTop="0dp"
                        android:background="@color/white"
                        android:minHeight="30dp"
                        android:paddingLeft="10dp">

                        <TextView
                            android:id="@+id/tv_order_detail_no"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:text="订单编号"
                            android:textColor="@color/text_676773"
                            android:textSize="13sp" />

                        <TextView
                            android:id="@+id/tv_order_detail_no_copy"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentRight="true"
                            android:layout_centerVertical="true"
                            android:layout_gravity="center_vertical"
                            android:layout_marginRight="10dp"
                            android:background="@drawable/order_gray_border"
                            android:gravity="center_vertical"
                            android:paddingLeft="13dp"
                            android:paddingTop="3dp"
                            android:paddingRight="13dp"
                            android:paddingBottom="3dp"
                            android:text="复制"
                            android:textColor="@color/text_292933"
                            android:textSize="14sp" />

                    </RelativeLayout>

                    <!--下单时间-->
                    <TextView
                        android:id="@+id/tv_order_created_time"
                        style="@style/order_detail_item_time"
                        android:text="下单时间: " />
                    <!--支付时间-->
                    <!--完成时间-->
                    <TextView
                        android:id="@+id/tv_order_pay_time"
                        style="@style/order_detail_item_time"
                        android:text="支付时间: "
                        android:visibility="gone" />

                    <!--TODO(交易快照 因历史问题,使用单一TextView的drawableRight属性无法保证右侧图标对齐问题,所以嵌套一层,后期需优化)-->
                    <TextView
                        android:id="@+id/tv_order_end_time"
                        style="@style/order_detail_item_time"
                        android:text="完成时间: "
                        android:visibility="gone" />

                    <LinearLayout
                        android:id="@+id/ll_order_trading_snapshot"
                        android:layout_width="match_parent"
                        android:layout_height="38.5dp"
                        android:background="@color/white"
                        android:gravity="center_vertical"
                        android:visibility="gone"
                        tools:visibility="visible">

                        <TextView
                            android:id="@+id/tv_order_trading_snapshot"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="10dp"
                            android:layout_weight="1"
                            android:textColor="@color/text_676773"
                            android:textSize="13sp" />

                        <TextView
                            style="@style/payment_item_layout_text_left"
                            android:text="查看"
                            android:textColor="@color/text_292933"
                            android:textSize="12sp" />
                    </LinearLayout>

                    <TextView
                        android:id="@+id/tv_order_remark"
                        style="@style/order_detail_item_time"
                        android:maxLines="1"
                        android:singleLine="true"
                        android:text="订单备注: " />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:visibility="gone"
                        android:background="@color/color_F5F5F5" />

                    <!--随货资质需求-->
                    <LinearLayout
                        android:id="@+id/llLicense"
                        android:layout_width="match_parent"
                        android:layout_height="38.5dp"
                        android:background="@color/white"
                        android:gravity="center_vertical"
                        android:visibility="gone"
                        tools:visibility="visible">

                        <TextView
                            android:id="@+id/tvLicenseTitle"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="10dp"
                            android:textColor="@color/text_676773"
                            android:text="资质随货:"
                            android:textSize="13sp" />

                        <TextView
                            android:id="@+id/tvLicenseContent"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="查看详情"
                            android:textSize="@dimen/dimen_dp_13"
                            android:textColor="@color/color_292933"
                            android:layout_marginStart="@dimen/dimen_dp_10" />

                        <TextView
                            android:id="@+id/tvLicenseArrow"
                            style="@style/payment_item_layout_text_left"
                            android:textColor="@color/text_292933"
                            android:textSize="12sp" />
                    </LinearLayout>

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/cl_seller_remark"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:visibility="gone"
                        android:layout_marginStart="@dimen/dimen_dp_10"
                        tools:visibility="visible">

                        <TextView
                            android:id="@+id/tv_seller_remark_title"
                            android:layout_width="wrap_content"
                            android:layout_height="@dimen/dimen_dp_39"
                            android:gravity="center_vertical"
                            android:layout_centerVertical="true"
                            android:text="商家备注:"
                            android:textColor="@color/text_676773"
                            android:textSize="13sp"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                        <TextView
                            android:id="@+id/tv_seller_remark_content"
                            android:layout_width="@dimen/dimen_dp_0"
                            android:layout_height="wrap_content"
                            android:gravity="center_vertical"
                            android:layout_centerVertical="true"
                            android:textColor="@color/color_292933"
                            android:textSize="13sp"
                            android:text="商家备注：客户药品经营许可证过期，请尽快更新，更新后可发货商家备注：客户药品经营许可证过期，请尽快更新，更新后可发货商家备注：客户药品经营许可证过期，请尽快更新，更新后可发货"
                            android:layout_marginStart="@dimen/dimen_dp_10"
                            android:layout_marginEnd="@dimen/dimen_dp_10"
                            android:paddingTop="@dimen/dimen_dp_10"
                            android:paddingBottom="@dimen/dimen_dp_10"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintHorizontal_weight="1"
                            app:layout_constraintStart_toEndOf="@+id/tv_seller_remark_title"
                            app:layout_constraintTop_toTopOf="parent" />
                    </androidx.constraintlayout.widget.ConstraintLayout>
                </LinearLayout>

                <!--订单服务-->
                <com.ybmmarket20.view.OrderServiceView
                    android:id="@+id/oderServiceView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dimen_dp_10"
                    android:layout_marginEnd="@dimen/dimen_dp_10"
                    android:layout_marginTop="@dimen/dimen_dp_10" />

                <!--客服电话-->
                <LinearLayout
                    android:id="@+id/orderServiceLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/bg_corner_all"
                    android:layout_marginStart="@dimen/dimen_dp_10"
                    android:layout_marginEnd="@dimen/dimen_dp_10"
                    android:layout_marginTop="@dimen/dimen_dp_10"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="遇到问题？"
                        android:textSize="@dimen/dimen_dp_15"
                        android:textStyle="bold"
                        android:layout_marginStart="@dimen/dimen_dp_10"
                        android:layout_marginTop="@dimen/dimen_dp_13"
                        android:layout_marginBottom="@dimen/dimen_dp_13"
                        android:textColor="@color/color_292933" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dimen_dp_1"
                        android:background="@color/colors_f5f5f5" />

                    <LinearLayout
                        android:id="@+id/ll_kufu"
                        style="@style/order_detail_item_layout"
                        android:layout_height="50dp"
                        android:divider="@drawable/divider_line_h_1px_f5f5f5"
                        android:gravity="center"
                        android:orientation="horizontal"
                        android:showDividers="middle">
                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:id="@+id/cl_kefu"
                            android:layout_width="wrap_content"
                            android:padding="10dp"
                            android:visibility="gone"
                            android:layout_height="wrap_content">

                            <ImageView
                                android:id="@+id/iv_kefu"
                                android:src="@drawable/icon_third_company_kefu"
                                android:layout_width="16dp"
                                app:layout_constraintTop_toTopOf="parent"
                                android:layout_marginTop="4dp"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintHorizontal_chainStyle="packed"
                                app:layout_constraintEnd_toStartOf="@id/tv_online_kefu"
                                android:layout_marginEnd="6dp"
                                android:layout_height="16dp"/>

                            <TextView
                                android:id="@+id/tv_online_kefu"
                                app:layout_constraintStart_toEndOf="@id/iv_kefu"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintTop_toTopOf="parent"
                                android:layout_width="wrap_content"
                                android:textColor="@color/text_color_333333"
                                android:textSize="14dp"
                                app:layout_constraintBottom_toBottomOf="parent"
                                android:text="联系商家在线客服"
                                android:layout_height="wrap_content"/>

                        </androidx.constraintlayout.widget.ConstraintLayout>

                        <com.ybmmarket20.view.DrawableTextView
                            android:id="@+id/tv_im"
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_weight="1"
                            android:drawableStart="@drawable/icon_order_kefu"
                            android:drawablePadding="7dp"
                            android:gravity="center_vertical"
                            android:text="@string/kefu_im_order_detail"
                            android:textColor="@color/text_676773"
                            android:textSize="14sp"
                            android:visibility="gone" />

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:id="@+id/cl_call_service"
                            android:layout_width="@dimen/dimen_dp_0"
                            android:layout_height="match_parent"
                            android:layout_weight="1">

                            <TextView
                                android:id="@+id/tv_call_service"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:drawableStart="@drawable/icon_order_detail_im"
                                android:drawablePadding="10dp"
                                android:text="@string/kefu_im_order_detail"
                                android:textColor="@color/text_676773"
                                android:textSize="14sp"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent" />

                            <TextView
                                android:id="@+id/tv_call_service_bubble"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/dimen_dp_60"
                                android:background="@drawable/shape_call_service_bg"
                                android:paddingStart="@dimen/dimen_dp_3"
                                android:paddingEnd="@dimen/dimen_dp_3"
                                android:text="处理更快"
                                android:textColor="@color/white"
                                android:visibility="gone"
                                android:textSize="@dimen/dimen_dp_8"
                                app:layout_constraintBottom_toTopOf="@+id/tv_call_service"
                                app:layout_constraintStart_toStartOf="@+id/tv_call_service" />
                        </androidx.constraintlayout.widget.ConstraintLayout>

                        <com.ybmmarket20.view.DrawableTextView
                            android:id="@+id/tv_kefu"
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_weight="1"
                            android:drawableStart="@drawable/icon_order_phone"
                            android:drawablePadding="7dp"
                            android:gravity="center_vertical"
                            android:text="@string/kefuPhone_order_detail"
                            android:textColor="@color/text_676773"
                            android:textSize="14sp" />

                    </LinearLayout>
                </LinearLayout>


                <Space
                    android:layout_width="match_parent"
                    android:layout_height="10dp" />

                <LinearLayout
                    android:id="@+id/orderShopInfo"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:layout_marginRight="10dp"
                    android:background="@drawable/bg_corner_all"
                    android:orientation="vertical">
                    <!--公司名称-->
                    <TextView
                        android:id="@+id/tv_company_name"
                        android:layout_width="wrap_content"
                        android:layout_height="44dp"
                        android:drawablePadding="5dp"
                        android:gravity="center_vertical"
                        android:paddingLeft="10dp"
                        android:paddingRight="10dp"
                        android:textColor="@color/text_292933"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:visibility="gone"
                        tools:text="武汉小药药"
                        tools:visibility="visible" />

                    <LinearLayout
                        android:id="@+id/llPop"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dimen_dp_58"
                        android:visibility="gone"
                        android:orientation="horizontal">

                        <ImageView
                            android:id="@+id/ivPop"
                            android:layout_width="wrap_content"
                            android:layout_height="@dimen/dimen_dp_15"
                            android:layout_gravity="center_vertical"
                            android:layout_marginStart="@dimen/dimen_dp_10"
                            android:src="@drawable/icon_payment_pop" />

                        <LinearLayout
                            android:layout_width="@dimen/dimen_dp_0"
                            android:layout_height="match_parent"
                            android:layout_weight="1"
                            android:gravity="center_vertical"
                            android:layout_marginStart="@dimen/dimen_dp_6"
                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/tvCompanyPopName"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textSize="@dimen/dimen_dp_14"
                                android:textColor="@color/color_292933"
                                tools:text="以岭药业工业旗舰店" />

                            <TextView
                                android:id="@+id/tvOriginalName"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textSize="@dimen/dimen_dp_10"
                                android:textColor="@color/color_676773"
                                android:layout_marginTop="@dimen/dimen_dp_2"
                                tools:text="企业名称：中核医药（湖北）有限公司" />
                        </LinearLayout>

                        <ImageView
                            android:layout_width="@dimen/dimen_dp_15"
                            android:layout_height="@dimen/dimen_dp_15"
                            android:src="@drawable/right_new"
                            android:layout_gravity="center_vertical"
                            android:layout_marginEnd="@dimen/dimen_dp_5" />

                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="@color/color_F5F5F5" />

                    <!--商品数量-->
                    <TextView
                        android:id="@+id/tv_product_num"
                        android:layout_width="match_parent"
                        android:layout_height="40dp"
                        android:gravity="center_vertical"
                        android:paddingLeft="10dp"
                        android:text="订单商品"
                        android:textColor="@color/text_676773"
                        android:textSize="13sp" />

                    <!--商品列表-->
                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/lv_product"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content" />
                    </RelativeLayout>

                </LinearLayout>
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rlvShop"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:layout_marginRight="10dp"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                    android:background="@drawable/bg_corner_all"/>
            </LinearLayout>
        </androidx.core.widget.NestedScrollView>

        <LinearLayout
            android:id="@+id/ll_bottom"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom"
            android:background="@color/white"
            android:elevation="7dp"
            android:gravity="center_vertical"
            android:orientation="vertical"
            android:showDividers="middle">

            <!--订单金额明细-->
            <LinearLayout
                android:id="@+id/ll_payment_num"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dimen_dp_12"
                android:layout_marginBottom="@dimen/dimen_dp_5"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingStart="10dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_toLeftOf="@+id/order_center_all"
                    android:gravity="center_vertical"
                    android:includeFontPadding="false"
                    android:text="实付金额:"
                    android:textColor="@color/text_292933"
                    android:textSize="@dimen/dimen_dp_13" />

                <TextView
                    android:id="@+id/order_detail_pay_num"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:includeFontPadding="false"
                    android:textColor="@color/color_FF2121"
                    android:textSize="@dimen/dimen_dp_13"
                    android:textStyle="bold"
                    tools:text="1826" />

                <TextView
                    android:id="@+id/order_detail_show"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:drawableRight="@drawable/order_detail_selector"
                    android:drawablePadding="5dp"
                    android:gravity="center_vertical"
                    android:includeFontPadding="false"
                    android:paddingLeft="5dp"
                    android:paddingRight="5dp"
                    android:text="展开明细"
                    android:textColor="@color/text_292933"
                    android:textSize="13sp" />
            </LinearLayout>

            <TextView
                android:id="@+id/tv_virtual_gold_tips"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="gone"
                android:layout_marginBottom="@dimen/dimen_dp_5"
                android:textSize="@dimen/dimen_dp_13"
                android:textColor="@color/text_676773"
                android:layout_marginStart="@dimen/dimen_dp_10"
                android:text="(含购物金 ¥0.8元)"
                tools:visibility="gone" />

            <!--退款金额-->
            <LinearLayout
                android:id="@+id/ll_refund_num"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:layout_marginBottom="@dimen/dimen_dp_12"
                android:orientation="horizontal"
                android:paddingStart="10dp"
                android:visibility="gone"
                tools:visibility="gone">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_toLeftOf="@+id/order_center_all"
                    android:gravity="center_vertical"
                    android:includeFontPadding="false"
                    android:text="退款金额:"
                    android:textColor="@color/text_292933"
                    android:textSize="@dimen/dimen_dp_13" />

                <TextView
                    android:id="@+id/order_detail_refund_num"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:includeFontPadding="false"
                    android:textColor="@color/color_FF2121"
                    android:textSize="@dimen/dimen_dp_13"
                    android:textStyle="bold"
                    tools:text="1826" />
            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="1px"
                android:background="@color/colors_f5f5f5" />
            <!--订单操按钮-->
            <com.ybmmarket20.view.OrderActionLayout
                android:id="@+id/ll_btn"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dimen_dp_46"
                android:layout_marginLeft="10dp"
                android:layout_marginRight="10dp" />
        </LinearLayout>
    </LinearLayout>

    <View
        android:id="@+id/bg"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="@dimen/order_detail_pop"
        android:background="#aa222222"
        android:visibility="gone" />

    <include
        android:visibility="gone"
        layout="@layout/layout_after_sales_mask" />
</FrameLayout>