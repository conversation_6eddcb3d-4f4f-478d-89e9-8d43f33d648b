<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_dp_10"
        android:text="快递类型:"
        app:layout_goneMarginStart="@dimen/dimen_dp_10"
        android:textColor="@color/color_292933"
        android:textSize="@dimen/dimen_dp_14"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_desc"
        android:layout_width="@dimen/dimen_dp_0"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_dp_5"
        android:layout_marginEnd="@dimen/dimen_dp_20"
        android:text="京东物流、顺丰物流"
        android:textColor="#666883"
        android:textSize="@dimen/dimen_dp_14"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/tv_title"
        app:layout_constraintTop_toTopOf="@+id/tv_title" />

</androidx.constraintlayout.widget.ConstraintLayout>