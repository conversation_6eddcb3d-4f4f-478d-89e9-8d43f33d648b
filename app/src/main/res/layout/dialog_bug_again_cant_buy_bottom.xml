<?xml version="1.0" encoding="utf-8"?>
<com.ybmmarket20.common.widget.RoundConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:rv_backgroundColor="@color/white"
    app:rv_cornerRadius="4dp">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="18dp"
        android:text="该商品已不能购买"
        android:textColor="@color/color_292933"
        android:textSize="16dp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/iv_goods"
        android:layout_width="63dp"
        android:layout_height="63dp"
        android:layout_marginStart="10dp"
        android:layout_marginTop="16dp"
        android:alpha="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_title" />

    <com.ybmmarket20.common.widget.RoundTextView
        android:id="@+id/tv_expired"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:gravity="center"
        android:text="已失效"
        android:textColor="@color/white"
        android:textSize="12dp"
        app:layout_constraintBottom_toBottomOf="@id/iv_goods"
        app:layout_constraintEnd_toEndOf="@id/iv_goods"
        app:layout_constraintStart_toStartOf="@id/iv_goods"
        app:layout_constraintTop_toTopOf="@id/iv_goods"
        app:rv_backgroundColor="@color/color_A6000000"
        app:rv_cornerRadius="20dp" />

    <TextView
        android:id="@+id/tv_goods_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="6dp"
        android:textColor="@color/color_292933"
        android:textSize="16dp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/iv_goods"
        app:layout_constraintTop_toTopOf="@id/iv_goods"
        tools:text="999 感冒灵颗粒/10g*9袋" />

    <TextView
        android:id="@+id/tv_company"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="6dp"
        android:textColor="@color/color_777777"
        android:textSize="14dp"
        app:layout_constraintStart_toStartOf="@id/tv_goods_title"
        app:layout_constraintTop_toBottomOf="@id/tv_goods_title"
        tools:text="广东一力集团制药" />

    <TextView
        android:id="@+id/tv_price"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="9dp"
        android:textColor="@color/color_777777"
        android:textSize="18dp"
        app:layout_constraintStart_toStartOf="@id/tv_company"
        app:layout_constraintTop_toBottomOf="@id/tv_company"
        tools:text="￥0.01" />

    <TextView
        android:id="@+id/tv_tips_content"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="14dp"
        android:text="为您找到以下相似商品"
        android:textColor="@color/colors_888888"
        android:textSize="12dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_price" />

    <View
        android:id="@+id/tips_divider_left"
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:layout_marginHorizontal="10dp"
        android:background="@color/colors_D8D8D8"
        app:layout_constraintBottom_toBottomOf="@id/tv_tips_content"
        app:layout_constraintEnd_toStartOf="@id/tv_tips_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_tips_content" />

    <View
        android:id="@+id/tips_divider_right"
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:layout_marginHorizontal="10dp"
        android:background="@color/colors_D8D8D8"
        app:layout_constraintBottom_toBottomOf="@id/tv_tips_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tv_tips_content"
        app:layout_constraintTop_toTopOf="@id/tv_tips_content" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_same_goods"
        android:layout_width="0dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_tips_content"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_height="0dp"/>

</com.ybmmarket20.common.widget.RoundConstraintLayout>