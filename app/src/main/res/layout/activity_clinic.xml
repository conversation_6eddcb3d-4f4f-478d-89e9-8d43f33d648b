<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <include layout="@layout/common_header_items_cart" />

        <androidx.coordinatorlayout.widget.CoordinatorLayout
            xmlns:app="http://schemas.android.com/apk/res-auto"
            android:id="@+id/ctl_content"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <com.google.android.material.appbar.AppBarLayout
                android:id="@+id/appbar"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white">

                <LinearLayout
                    android:id="@+id/ll_search"
                    android:layout_width="match_parent"
                    android:layout_height="66dp"
                    android:orientation="horizontal"
                    android:paddingBottom="8dp"
                    android:paddingTop="8dp"
                    app:layout_scrollFlags="scroll|enterAlways">

                    <ImageView
                        android:id="@+id/iv_code"
                        android:layout_width="50dp"
                        android:layout_height="50dp"
                        android:layout_gravity="center_vertical"
                        android:paddingBottom="8dp"
                        android:paddingLeft="14dp"
                        android:paddingRight="8dp"
                        android:paddingTop="8dp"
                        android:src="@drawable/icon_search_scan" />

                    <RelativeLayout
                        android:id="@+id/search_rl"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_gravity="center_vertical"
                        android:layout_marginBottom="6dp"
                        android:layout_marginRight="10dp"
                        android:layout_marginTop="6dp"
                        android:layout_weight="1"
                        android:background="@drawable/search_round_corner_gray_bg"
                        android:padding="4dp">

                        <TextView
                            android:id="@+id/tv_search"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:layout_marginLeft="5dp"
                            android:background="@null"
                            android:maxLines="1"
                            android:singleLine="true"
                            android:text="@string/search_hint"
                            android:textColor="#adaba8"
                            android:textSize="14sp" />

                        <ImageView
                            android:id="@+id/iv_voice"
                            android:layout_width="40dp"
                            android:layout_height="match_parent"
                            android:layout_alignParentRight="true"
                            android:layout_centerVertical="true"
                            android:padding="2dp"
                            android:src="@drawable/nav_voice_01" />
                    </RelativeLayout>

                </LinearLayout>

                <com.google.android.material.tabs.TabLayout
                    android:id="@+id/tabs"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:layout_alignParentBottom="true"
                    app:elevation="0dp"
                    app:tabGravity="fill"
                    app:tabIndicatorColor="@color/tv_tab_color"
                    app:tabIndicatorHeight="3dp"
                    app:tabMode="fixed"
                    app:tabSelectedTextColor="@color/tv_tab_color"
                    app:tabTextAppearance="@style/TabLayoutTextAppearance"
                    app:tabTextColor="@color/text_676773" />

            </com.google.android.material.appbar.AppBarLayout>

            <com.ybmmarket20.view.NoScrollViewPager
                android:id="@+id/viewpager"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/activity_bg"
                app:layout_behavior="@string/appbar_scrolling_view_behavior" />

        </androidx.coordinatorlayout.widget.CoordinatorLayout>
    </LinearLayout>

    <ImageView
        android:id="@+id/iv_fastscroll"
        android:layout_width="54dp"
        android:layout_height="54dp"
        android:layout_gravity="bottom|right"
        android:layout_marginBottom="60dp"
        android:layout_marginRight="20dp"
        android:src="@drawable/icon_fast_to_top"
        android:visibility="invisible" />
</FrameLayout>