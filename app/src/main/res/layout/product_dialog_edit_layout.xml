<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rl_layout"
    android:layout_width="104dp"
    android:layout_height="27dp">

    <FrameLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true">

        <EditText
            android:id="@+id/et_num"
            android:layout_width="50dp"
            android:layout_height="27dp"
            android:background="@drawable/bg_dialog_product_edit_def"
            android:gravity="center"
            android:imeOptions="actionDone"
            android:inputType="number"
            android:maxLength="5"
            android:maxLines="1"
            android:minWidth="60dp"
            android:selectAllOnFocus="true"
            android:singleLine="true"
            android:textColor="#676773"
            android:textCursorDrawable="@drawable/color_cursor"
            android:textSize="14sp"
            tools:text="9999" />
    </FrameLayout>

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_numSub"
        android:layout_width="27dp"
        android:layout_height="27dp"
        android:layout_centerVertical="true"
        android:src="@drawable/icon_reduce_gray" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_numAdd"
        android:layout_width="27dp"
        android:layout_height="27dp"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        android:src="@drawable/icon_add_gray" />
</RelativeLayout>
