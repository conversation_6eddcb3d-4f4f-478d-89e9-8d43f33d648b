<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical">

    <include layout="@layout/common_header_items" />

    <TextView
        android:id="@+id/tv_hint"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/colors_fff7ef"
        android:gravity="center_vertical"
        android:lineSpacingExtra="2dp"
        android:minHeight="50dp"
        android:paddingBottom="6dp"
        android:paddingLeft="10dp"
        android:paddingRight="6dp"
        android:paddingTop="8dp"
        tools:text="@string/refund_optimize_hint"
        android:textColor="@color/text_676773"
        android:textSize="12sp" />

    <com.ybmmarket20.view.MyScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/activity_bg">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:paddingBottom="20dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:minHeight="40dp"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_amount"
                    style="@style/apply_refund_activiy_title"
                    android:layout_width="0dp"
                    android:layout_marginLeft="8dp"
                    android:layout_weight="1"
                    android:text="退款金额:" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="8dp"
                    android:text="（不含活动优惠金额）"
                    android:textColor="@color/text_9494A6"
                    android:textSize="14sp" />
            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_marginLeft="6dp"
                android:layout_marginRight="6dp"
                android:background="#eeeeee" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:id="@+id/ll_back_money"
                android:minHeight="40dp"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_balance"
                    style="@style/apply_refund_activiy_title"
                    android:layout_width="0dp"
                    android:layout_marginLeft="8dp"
                    android:layout_weight="1"
                    android:text="退回余额:" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="8dp"
                    android:text="（退款成功可退余额）"
                    android:textColor="@color/text_9494A6"
                    android:textSize="14sp" />
            </LinearLayout>

            <View
                android:id="@+id/v_line_virtual_money"
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_marginLeft="6dp"
                android:layout_marginRight="6dp"
                android:visibility="gone"
                tools:visibility="visible"
                android:background="#eeeeee" />

            <LinearLayout
                android:id="@+id/ll_virtual_money"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:minHeight="40dp"
                android:visibility="gone"
                tools:visibility="visible"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_virtual_money"
                    style="@style/apply_refund_activiy_title"
                    android:layout_width="0dp"
                    android:layout_marginLeft="8dp"
                    android:layout_weight="1"
                    android:text="退回购物金:" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="8dp"
                    android:text="（退款成功可退购物金）"
                    android:textColor="@color/text_9494A6"
                    android:textSize="14sp" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_bank_info"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                android:orientation="vertical">


                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:minHeight="38dp"
                    android:paddingLeft="8dp"
                    android:text="请填写您的收款账户信息"
                    android:textColor="@color/text_292933"
                    android:textSize="12sp" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:layout_marginLeft="6dp"
                    android:layout_marginRight="6dp"
                    android:background="#eeeeee" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/white"
                    android:minHeight="40dp"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="8dp"
                        android:text="*"
                        android:textColor="#FA5741" />

                    <TextView
                        style="@style/apply_refund_activiy_title"
                        android:layout_width="150dp"
                        android:text="开户行及支行：" />

                    <com.ybmmarket20.common.widget.RoundEditText
                        android:id="@+id/et_bank_name"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginRight="8dp"
                        android:layout_weight="1"
                        android:gravity="right"
                        android:imeOptions="actionGo"
                        android:maxLines="1"
                        android:singleLine="true"
                        android:text=""
                        android:textColor="@color/text_292933"
                        android:textCursorDrawable="@drawable/color_cursor"
                        android:textSize="15sp" />
                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:layout_marginLeft="6dp"
                    android:layout_marginRight="6dp"
                    android:background="#eeeeee" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/white"
                    android:minHeight="40dp"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="8dp"
                        android:text="*"
                        android:textColor="#FA5741" />

                    <TextView
                        style="@style/apply_refund_activiy_title"
                        android:layout_width="150dp"
                        android:text="银行卡号：" />

                    <com.ybmmarket20.common.widget.RoundEditText
                        android:id="@+id/et_bank_card"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginRight="8dp"
                        android:layout_weight="1"
                        android:gravity="right"
                        android:imeOptions="actionGo"
                        android:inputType="number"
                        android:maxLines="1"
                        android:singleLine="true"
                        android:text=""
                        android:textColor="@color/text_292933"
                        android:textCursorDrawable="@drawable/color_cursor"
                        android:textSize="15sp" />
                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:layout_marginLeft="6dp"
                    android:layout_marginRight="6dp"
                    android:background="#eeeeee" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/white"
                    android:minHeight="40dp"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="8dp"
                        android:text="*"
                        android:textColor="#FA5741" />

                    <TextView
                        style="@style/apply_refund_activiy_title"
                        android:layout_width="150dp"
                        android:text="开户人：" />

                    <com.ybmmarket20.common.widget.RoundEditText
                        android:id="@+id/et_owner"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginRight="8dp"
                        android:layout_weight="1"
                        android:gravity="right"
                        android:imeOptions="actionGo"
                        android:maxLines="1"
                        android:singleLine="true"
                        android:text=""
                        android:textColor="@color/text_292933"
                        android:textCursorDrawable="@drawable/color_cursor"
                        android:textSize="15sp" />

                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:layout_marginLeft="6dp"
                    android:layout_marginRight="6dp"
                    android:background="#eeeeee" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/white"
                    android:minHeight="40dp"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="8dp"
                        android:text="*"
                        android:textColor="#FA5741" />

                    <TextView
                        style="@style/apply_refund_activiy_title"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:text="联系电话：" />

                    <com.ybmmarket20.common.widget.RoundEditText
                        android:id="@+id/et_cell_phone"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginRight="8dp"
                        android:layout_weight="1"
                        android:gravity="right"
                        android:inputType="phone"
                        android:imeOptions="actionGo"
                        android:maxLines="1"
                        android:singleLine="true"
                        android:text=""
                        android:textColor="@color/text_292933"
                        android:textCursorDrawable="@drawable/color_cursor"
                        android:textSize="15sp" />

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:layout_marginTop="10dp"
                android:background="@color/white"
                android:orientation="horizontal"
                android:paddingLeft="8dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="*"
                    android:textColor="#FA5741" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:text="退款原因："
                    android:textColor="@color/text_292933"
                    android:textSize="15sp" />

                <TextView
                    android:id="@+id/tv_reason"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="8dp"
                    android:drawablePadding="5dp"
                    android:drawableRight="@drawable/icon_show_promition_action2"
                    android:text="请选择退款原因"
                    android:textColor="@color/text_292933"
                    android:textSize="15sp" />

            </LinearLayout>


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:layout_marginTop="10dp"
                android:background="@color/white"
                android:orientation="horizontal"
                android:paddingLeft="8dp">

                <TextView
                    android:layout_width="100dp"
                    android:layout_height="match_parent"
                    android:layout_gravity="center_vertical"
                    android:gravity="center_vertical"
                    android:text="退款说明："
                    android:textColor="@color/text_292933"
                    android:textSize="15sp" />

                <com.ybmmarket20.common.widget.RoundEditText
                    android:id="@+id/tv_info"
                    android:layout_width="0dp"
                    android:layout_height="50dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginRight="10dp"
                    android:layout_weight="1"
                    android:imeOptions="actionGo"
                    android:maxLines="1"
                    android:singleLine="true"
                    android:text=""
                    android:textColor="@color/color_434343"
                    android:textCursorDrawable="@drawable/color_cursor"
                    android:textSize="14sp"
                    app:rv_backgroundColor="@color/white"
                    app:rv_cornerRadius="4dp" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:background="@color/white"
                android:orientation="horizontal"
                android:paddingBottom="10dp"
                android:paddingLeft="8dp"
                android:paddingTop="10dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="上传凭证："
                    android:textColor="@color/text_292933"
                    android:textSize="15sp" />

                <com.ybmmarket20.common.widget.RoundRelativeLayout
                    android:id="@+id/fragment"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="7dp"
                    android:layout_marginRight="7dp"
                    app:rv_backgroundColor="@color/white"
                    app:rv_cornerRadius="4dp" />

            </LinearLayout>

            <TextView
                android:id="@+id/tv_refund_tip"
                style="@style/apply_refund_activiy_title"
                android:layout_marginLeft="12dp"
                android:layout_marginStart="@dimen/dimen_dp_0"
                android:background="@color/white"
                android:paddingStart="@dimen/dimen_dp_70"
                android:visibility="gone"
                android:textColor="#9494A6" />

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_marginTop="5dp"
                android:background="@color/back_gray" />

            <Button
                android:id="@+id/btn_ok"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:layout_gravity="bottom|center_vertical"
                android:layout_marginLeft="35dp"
                android:layout_marginRight="35dp"
                android:layout_marginTop="10dp"
                android:background="@drawable/common2_btn_base_color_bg"
                android:gravity="center"
                android:text="提交"
                android:textColor="@color/white"
                android:textSize="17sp" />
        </LinearLayout>
    </com.ybmmarket20.view.MyScrollView>
</LinearLayout>