<?xml version="1.0" encoding="utf-8"?>
<com.ybmmarket20.common.widget.RoundLinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="44dp"
    android:layout_gravity="center"
    android:layout_marginLeft="10dp"
    android:layout_marginRight="10dp"
    app:rv_backgroundColor="@color/color_F5F5F5"
    app:rv_cornerRadius_TL="2dp"
    app:rv_cornerRadius_TR="2dp">

    <LinearLayout
        android:id="@+id/cart_item_ll"
        android:layout_width="40dp"
        android:layout_height="match_parent"
        android:layout_gravity="bottom"
        android:gravity="center"
        android:orientation="vertical">

        <CheckBox
            android:id="@+id/shop_check"
            style="@style/CustomCheckboxTheme"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom"
            android:layout_margin="9dp"
            android:gravity="center"
            android:visibility="visible" />

    </LinearLayout>

    <com.ybmmarket20.common.widget.RoundLinearLayout
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_cart_proprietary"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_gravity="bottom"
            android:drawableEnd="@drawable/right_new"
            android:ellipsize="end"
            android:gravity="center_vertical"
            android:maxLines="1"
            android:text=""
            tools:text="xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
            android:textColor="@color/color_676773"
            android:textSize="@dimen/dimen_dp_14" />

        <TextView
            android:id="@+id/tv_head_tip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="@dimen/dimen_dp_5"
            android:drawableStart="@null"
            android:drawablePadding="@dimen/dimen_dp_3"
            android:textColor="@color/color_9494A6"
            android:textSize="@dimen/dimen_dp_12"
            android:visibility="gone"
            tools:drawableStart="@drawable/icon_hint_image_cart"
            tools:text="123"
            tools:visibility="visible" />

    </com.ybmmarket20.common.widget.RoundLinearLayout>

    <TextView
        android:id="@+id/icon_cart_proprietary"
        android:layout_width="wrap_content"
        android:layout_height="20dp"
        android:layout_gravity="center_vertical"
        android:gravity="center"
        android:text="优惠券"
        android:layout_marginRight="@dimen/dimen_dp_10"
        android:paddingLeft="@dimen/dimen_dp_20"
        android:textColor="@color/color_ff2121"
        android:textSize="@dimen/dimen_dp_14"
        tools:visibility="visible" />

</com.ybmmarket20.common.widget.RoundLinearLayout>