<?xml version="1.0" encoding="utf-8"?>
<com.ybmmarket20.common.widget.RoundLinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="138dp"
    android:layout_marginBottom="10dp"
    android:orientation="horizontal"
    android:padding="6dp"
    app:rv_backgroundColor="@color/white"
    app:rv_cornerRadius="6dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal">

        <RelativeLayout
            android:layout_width="120dp"
            android:layout_height="120dp"
            android:layout_gravity="center_vertical"
            android:layout_marginLeft="4dp">

            <ImageView
                android:id="@+id/icon"
                android:layout_width="120dp"
                android:layout_height="120dp"
                android:layout_centerInParent="true"
                android:background="@drawable/home_image_bg"
                android:padding="@dimen/home_product_image_padding" />

            <ImageView
                android:id="@+id/iv_shop_mark"
                android:layout_width="120dp"
                android:layout_height="120dp"
                android:layout_centerInParent="true"
                android:scaleType="fitXY"
                android:src="@drawable/transparent" />

            <com.ybmmarket20.view.PromotionTagView
                android:id="@+id/view_ptv"
                android:layout_width="120dp"
                android:layout_height="120dp"
                app:subTitleTextSize="5dp"
                app:contentTextSize="8dp"
                android:layout_gravity="center" />


            <TextView
                android:id="@+id/tv_activity_price"
                style="@style/activity_price"
                android:layout_alignParentBottom="true"
                android:layout_marginLeft="35dp"
                android:gravity="center_vertical"
                android:text=""
                android:visibility="gone" />

            <TextView
                android:id="@+id/shop_no_limit_tv01"
                android:layout_width="45dp"
                android:layout_height="45dp"
                android:layout_centerInParent="true"
                android:layout_gravity="center"
                android:background="@drawable/shop_limit01"
                android:gravity="center"
                android:text=""
                android:textColor="#ffffff"
                android:textSize="12dp"
                android:visibility="gone" />

        </RelativeLayout>

        <RelativeLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginLeft="12dp"
            android:layout_weight="1">

            <LinearLayout
                android:id="@+id/ll_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <ImageView
                    android:id="@+id/tv_procurement_festival"
                    android:layout_width="42dp"
                    android:layout_height="17dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginRight="2dp"
                    android:background="@drawable/icon_procurement_festival"
                    android:visibility="gone" />

                <TextView
                    android:id="@+id/tv_exclusive"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginRight="2dp"
                    android:background="@drawable/bg_brand_item_exclusive"
                    android:paddingLeft="2dp"
                    android:paddingRight="2dp"
                    android:text="独家"
                    android:textColor="@color/white"
                    android:textSize="12sp"
                    android:visibility="gone" />

                <TextView
                    android:id="@+id/tv_health_insurance"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginRight="2dp"
                    android:background="@drawable/bg_brand_item_health_insurance"
                    android:paddingLeft="2dp"
                    android:paddingRight="2dp"
                    android:text="医保"
                    android:textColor="@color/white"
                    android:textSize="12sp"
                    android:visibility="gone" />

                <TextView
                    android:id="@+id/shop_name"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:singleLine="true"
                    android:text="秦皇岛 阿莫西林胶囊"
                    android:textColor="@color/brand_shop_name"
                    android:textSize="16sp" />

                <LinearLayout
                    android:id="@+id/shop_ck_ll"
                    android:layout_width="33dp"
                    android:layout_height="33dp"
                    android:layout_alignParentRight="true"
                    android:gravity="center_horizontal"
                    android:paddingTop="11dp"
                    android:visibility="gone">

                    <CheckBox
                        android:id="@+id/shop_ck"
                        style="@style/shoucangCheckboxTheme"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:clickable="false" />
                </LinearLayout>
            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_shop_description"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/ll_layout"
                android:layout_marginTop="4dp"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/shop_description"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:maxLines="1"
                    android:singleLine="true"
                    android:text="0.4克*36粒"
                    android:textColor="@color/brand_description_tv1"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/shop_price_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:singleLine="true"
                    android:text=""
                    android:textColor="@color/text_292933"
                    android:textSize="14sp"
                    android:visibility="visible" />
            </LinearLayout>

            <com.ybmmarket20.view.TagView
                android:id="@+id/rl_icon_type"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/ll_shop_description"
                android:layout_marginTop="4dp"></com.ybmmarket20.view.TagView>

            <RelativeLayout
                android:id="@+id/shop_price_layout"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/rl_icon_type"
                android:layout_marginTop="4dp"
                android:gravity="center_vertical"
                android:visibility="invisible">

                <TextView
                    android:id="@+id/shop_price_kxj_title_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:background="@drawable/bg_brand_item_control_market1"
                    android:gravity="center"
                    android:minWidth="38dp"
                    android:paddingLeft="1.6dp"
                    android:paddingRight="1.6dp"
                    android:singleLine="true"
                    android:text="@string/product_list_kxj_title"
                    android:textColor="@color/detail_shop_price_kxj_title_tv"
                    android:textSize="11sp"
                    android:visibility="invisible" />

                <TextView
                    android:id="@+id/shop_price_kxj_number_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_toRightOf="@+id/shop_price_kxj_title_tv"
                    android:background="@drawable/bg_brand_item_control_market2"
                    android:gravity="center_vertical"
                    android:paddingLeft="1.6dp"
                    android:paddingRight="1.6dp"
                    android:singleLine="true"
                    android:text=""
                    android:textColor="@color/text_676773"
                    android:textSize="11sp"
                    android:visibility="invisible" />

                <TextView
                    android:id="@+id/shop_price_ml_title_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="5dp"
                    android:layout_toRightOf="@+id/shop_price_kxj_number_tv"
                    android:background="@drawable/bg_brand_item_control_market1"
                    android:gravity="center"
                    android:minWidth="38dp"
                    android:paddingLeft="1.6dp"
                    android:paddingRight="1.6dp"
                    android:singleLine="true"
                    android:text="@string/product_list_ml_title"
                    android:textColor="@color/detail_shop_price_kxj_title_tv"
                    android:textSize="11sp"
                    android:visibility="visible" />

                <TextView
                    android:id="@+id/shop_price_ml_number_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_toRightOf="@+id/shop_price_ml_title_tv"
                    android:background="@drawable/bg_brand_item_control_market2"
                    android:gravity="center_vertical"
                    android:paddingLeft="1.6dp"
                    android:paddingRight="1.6dp"
                    android:singleLine="true"
                    android:text=""
                    android:textColor="@color/text_676773"
                    android:textSize="11sp"
                    android:visibility="visible" />
            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/brand_rl_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_alignParentStart="true"
                android:layout_alignParentLeft="true"
                android:layout_alignParentBottom="true"
                android:layout_marginBottom="3dp">

                <TextView
                    android:id="@+id/shop_price"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_toLeftOf="@+id/el_edit_layout"
                    android:ellipsize="end"
                    android:lines="1"
                    android:singleLine="true"
                    android:text=""
                    android:textColor="@color/record_red"
                    android:textSize="@dimen/brand_shop_name" />

                <TextView
                    android:id="@+id/tv_oem"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_toLeftOf="@+id/el_edit_layout"
                    android:ellipsize="end"
                    android:lines="1"
                    android:singleLine="true"
                    android:text="价格签署协议可见"
                    android:textColor="@color/brand_new_color"
                    android:textSize="15sp"
                    android:visibility="gone" />

                <RelativeLayout
                    android:id="@+id/el_edit_layout"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_marginRight="10dp">

                    <com.ybmmarket20.view.ProductEditLayout
                        android:id="@+id/el_edit"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content" />

                    <TextView
                        android:id="@+id/tv_brand_control"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerInParent="true"
                        android:text="暂无购买权限"
                        android:textColor="@color/brand_control"
                        android:textSize="14sp"
                        android:visibility="gone" />
                </RelativeLayout>
            </RelativeLayout>
        </RelativeLayout>
    </LinearLayout>
</com.ybmmarket20.common.widget.RoundLinearLayout>