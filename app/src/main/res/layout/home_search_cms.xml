<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="@dimen/header_search_height"
    android:orientation="horizontal"
    android:paddingTop="@dimen/header_height_padding_top">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/ll_search"
        android:layout_width="match_parent"
        android:layout_height="@dimen/header_search_height"
        android:paddingTop="@dimen/header_height_padding_top">

        <ImageView
            android:id="@+id/iv_scan"
            android:layout_width="54dp"
            android:layout_height="54dp"
            android:scaleType="center"
            android:src="@drawable/icon_search_scan"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


        <RelativeLayout
            android:id="@+id/home_search_rl"
            android:layout_width="0dp"
            android:layout_height="34dp"
            android:layout_weight="1"
            app:layout_goneMarginRight="12dp"
            android:background="@drawable/search_round_corner_gray_bg"
            android:layout_gravity="center_vertical"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@id/iv_scan"
            app:layout_constraintRight_toLeftOf="@id/title_right"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/iv_a_magnifying_glass"
                android:layout_width="22dp"
                android:layout_height="22dp"
                android:layout_centerVertical="true"
                android:layout_marginLeft="6dp"
                android:src="@drawable/icon_a_magnifying_glass" />

            <TextView
                android:id="@+id/title_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="3dp"
                android:layout_toRightOf="@id/iv_a_magnifying_glass"
                android:background="@null"
                android:maxLines="1"
                android:singleLine="true"
                android:text="@string/search_hint"
                android:textColor="@color/color_9494A6"
                android:textSize="13dp" />

            <ImageView
                android:id="@+id/iv_voice"
                android:layout_width="22dp"
                android:layout_height="22dp"
                android:layout_marginRight="6dp"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:src="@drawable/nav_voice_01" />
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/title_right"
            android:layout_width="54dp"
            android:layout_height="54dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/iv_msg"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:layout_marginLeft="15dp"
                android:src="@drawable/icon_search_message" />

            <TextView
                android:id="@+id/tv_smg_num"
                style="@style/more_msg_tip_style"
                android:layout_margin="10dp"
                android:visibility="gone"
                tools:text="9"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/tv_smg_num_more"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignTop="@id/tv_smg_num"
                android:layout_alignRight="@id/tv_smg_num"
                android:layout_marginTop="-4dp"
                android:layout_marginRight="1.5dp"
                android:text="+"
                android:textColor="@color/white"
                android:textSize="9dp"
                android:visibility="gone"
                tools:visibility="visible" />


        </RelativeLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</merge>