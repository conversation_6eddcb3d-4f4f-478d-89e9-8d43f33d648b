<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:paddingStart="18dp"
    android:paddingEnd="11dp"
    android:paddingTop="10dp"
    android:paddingBottom="6dp">

    <ImageView
        android:id="@+id/iv_bg"
        android:layout_width="71dp"
        android:layout_height="123dp"
        android:layout_centerHorizontal="true"
        android:layout_centerVertical="true"
        android:background="@drawable/icon_consultant_bg"
        android:padding="3dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="21dp"
        android:layout_marginTop="17dp"
        android:ellipsize="end"
        android:includeFontPadding="false"
        android:maxLines="2"
        android:textColor="@color/text_color_333333"
        android:textSize="21sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/iv_bg"
        app:layout_constraintTop_toTopOf="@id/iv_bg"
        tools:text="王晓东" />


    <TextView
        android:id="@+id/tv_desc"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:includeFontPadding="false"
        android:textColor="@color/text_color_666666"
        android:textSize="14sp"
        app:layout_constraintStart_toStartOf="@id/tv_name"
        app:layout_constraintTop_toBottomOf="@id/tv_name"
        tools:text="“有事您找我”" />

    <LinearLayout
        android:layout_width="0dp"
        android:orientation="horizontal"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/tv_name">

        <com.ybmmarket20.common.widget.RoundConstraintLayout
            android:id="@+id/con_call_phone"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:paddingTop="10dp"
            android:paddingBottom="10dp"
            app:rv_backgroundColor="#F1F4F7"
            app:rv_cornerRadius="19dp">

            <TextView
                android:id="@+id/tv_call_phone"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawableStart="@drawable/icon_call_phone_yellow"
                android:drawablePadding="5dp"
                android:text="电话联系"
                android:textColor="@color/text_color_333333"
                android:textSize="14sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </com.ybmmarket20.common.widget.RoundConstraintLayout>

        <com.ybmmarket20.common.widget.RoundConstraintLayout
            android:id="@+id/con_call_wechat"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:layout_weight="1"
            android:paddingTop="10dp"
            android:paddingBottom="10dp"
            app:rv_backgroundColor="#F1F4F7"
            app:rv_cornerRadius="19dp">

            <TextView
                android:id="@+id/tv_call_wechat"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawableStart="@drawable/icon_wechat_small"
                android:drawablePadding="5dp"
                android:text="微信联系"
                android:textColor="@color/text_color_333333"
                android:textSize="14sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </com.ybmmarket20.common.widget.RoundConstraintLayout>
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>