<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#fafafa"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/header_search_height"
        android:background="@drawable/base_header_default_bg"
        android:orientation="horizontal"
        android:paddingTop="@dimen/header_height_padding_top">

        <RelativeLayout
            android:id="@+id/title_left"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:paddingLeft="10dp"
            android:visibility="visible">

            <ImageView
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_centerVertical="true"
                android:minWidth="54dp"
                android:src="@drawable/ic_back" />

        </RelativeLayout>

        <RadioGroup
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="4"
            android:background="@color/white"
            android:orientation="horizontal">

            <FrameLayout
                android:id="@+id/fl_merchants"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1">

                <RadioButton
                    android:id="@+id/rb_merchants"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:background="@drawable/product_rb_selector_bg"
                    android:button="@null"
                    android:checked="true"
                    android:clickable="false"
                    android:gravity="center"
                    android:text="合作商家"
                    android:textColor="@drawable/product_base_selector_textcolor"
                    android:textSize="14sp" />
            </FrameLayout>

            <FrameLayout
                android:id="@+id/fl_merchant_list"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1">

                <RadioButton
                    android:id="@+id/rb_merchant_list"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:background="@drawable/product_rb_selector_bg"
                    android:button="@null"
                    android:checked="false"
                    android:clickable="false"
                    android:gravity="center"
                    android:text="商家列表"
                    android:textColor="@drawable/product_base_selector_textcolor"
                    android:textSize="14sp" />
            </FrameLayout>

        </RadioGroup>

        <LinearLayout
            android:visibility="invisible"
            android:id="@+id/tv_menu"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="right|center_vertical"
            android:orientation="horizontal"
            android:paddingRight="10dp">

            <ImageView
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_centerVertical="true"
                android:minWidth="54dp"
                android:src="@drawable/nav_scarch" />

        </LinearLayout>
    </LinearLayout>

    <com.ybm.app.view.CommonRecyclerView
        android:id="@+id/crv_list"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#f8f8f8" />

</LinearLayout>