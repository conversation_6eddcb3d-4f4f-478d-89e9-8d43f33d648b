<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/ll_title"
            android:layout_width="match_parent"
            android:layout_height="@dimen/header_height"
            android:background="@drawable/base_header_default_bg"
            android:orientation="horizontal"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <RelativeLayout
                android:id="@+id/title_left"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_gravity="center_vertical"
                android:layout_marginLeft="10dp"
                android:layout_weight="1"
                android:visibility="invisible">

                <TextView
                    android:id="@+id/tv_left"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:drawableLeft="@drawable/ic_back"
                    android:gravity="center_vertical"
                    android:paddingTop="7dp"
                    android:paddingBottom="7dp"
                    android:singleLine="true"
                    android:textColor="@color/black"
                    android:textSize="14sp" />
            </RelativeLayout>

            <TextView
                android:id="@+id/title_tv"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_gravity="center_vertical"
                android:layout_weight="1"
                android:gravity="center"
                android:singleLine="true"
                android:text=""
                android:textColor="@color/black"
                android:textSize="18sp" />

            <TextView
                android:id="@+id/title_right"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_gravity="center_vertical"
                android:layout_weight="1"
                android:gravity="center_vertical|right"
                android:paddingRight="10dp"
                android:text="编辑"
                android:textColor="@color/text_676773"
                android:visibility="invisible" />

            <TextView
                android:id="@+id/tv_platform_coupon"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="center"
                android:paddingStart="10dp"
                android:paddingEnd="15dp"
                android:text="优惠券"
                android:textColor="@color/color_FF2121"
                android:textSize="14dp"
                android:visibility="gone" />
        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1px"
            android:background="@color/divider_line_base_1px"
            app:layout_constraintTop_toBottomOf="@id/ll_title" />

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginTop="1px"
            android:background="@color/color_F5F5F5"
            app:layout_constraintBottom_toTopOf="@id/ll_freight_over_weight_tips"
            app:layout_constraintTop_toBottomOf="@id/ll_title">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <com.ybmmarket20.view.CartViewLayout
                    android:id="@+id/iv_cart_action"
                    style="@style/cart_layout_style" />

                <com.ybmmarket20.view.CartViewLayout
                    android:id="@+id/iv_cart_notice"
                    style="@style/cart_layout_style" />

                <include layout="@layout/layout_aptitude_overdue_tip" />

                <FrameLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <com.ybm.app.view.CommonRecyclerView
                        android:id="@+id/cart_list_lv"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent" />

                    <include layout="@layout/cart_section_head10" />

                </FrameLayout>

            </LinearLayout>
        </FrameLayout>

        <LinearLayout
            android:id="@+id/ll_freight_over_weight_tips"
            android:layout_width="match_parent"
            android:layout_height="38dp"
            android:background="@color/color_fff7ef"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:visibility="gone"
            app:layout_constraintBottom_toTopOf="@id/bottomBar"
            tools:visibility="visible">

            <TextView
                android:id="@+id/tv_over_weight_tips"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="9dp"
                android:layout_weight="1"
                android:drawableStart="@drawable/icon_cart_freight_add_on_item_unnormal"
                android:drawablePadding="3dp"
                android:gravity="center_vertical"
                android:text="@string/str_freight_over_weight_cart_bottom_tip"
                android:textColor="@color/colors_99664D"
                android:textSize="11dp" />

            <com.ybmmarket20.common.widget.RoundTextView
                android:id="@+id/rtv_over_weight_look"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="10dp"
                android:gravity="center"
                android:paddingStart="8dp"
                android:paddingTop="3dp"
                android:paddingEnd="8dp"
                android:paddingBottom="3dp"
                android:text="@string/str_freight_over_weight_cart_bottom_look"
                android:textColor="@color/colors_99664D"
                android:textSize="11dp"
                app:rv_cornerRadius="1dp"
                app:rv_strokeColor="@color/colors_99664D"
                app:rv_strokeWidth="1dp" />

        </LinearLayout>

        <include
            android:id="@+id/fl_cart_discount"
            layout="@layout/show_dicount_cart_pop"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:visibility="gone"
            app:layout_constraintBottom_toTopOf="@id/bottomBar"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="1"
            tools:visibility="gone" />

        <LinearLayout
            android:id="@+id/bottomBar"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_gravity="bottom"
            android:background="@color/white"
            android:divider="@drawable/divider_line_base_1px"
            android:elevation="3dp"
            android:orientation="horizontal"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            tools:visibility="visible">

            <LinearLayout
                android:id="@+id/cart_ll1"
                android:layout_width="40dp"
                android:layout_height="match_parent"
                android:gravity="center"
                android:orientation="horizontal">

                <CheckBox
                    android:id="@+id/shop_check"
                    style="@style/CustomCheckboxTheme"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:clickable="false"
                    android:enabled="false"
                    android:gravity="center" />

            </LinearLayout>

            <TextView
                android:id="@+id/cart_tv_edit"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center_vertical"
                android:text="全选"
                android:textColor="@color/color_292933"
                android:textSize="15sp"
                android:visibility="gone" />

            <RelativeLayout
                android:id="@+id/cart_rl2"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true">

                    <TextView
                        android:id="@+id/cart_tv_select"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="全选"
                        android:textColor="@color/color_292933"
                        android:textSize="15sp" />

                    <TextView
                        android:id="@+id/cart_ll2_tv1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_below="@+id/cart_tv_select"
                        android:gravity="center_vertical"
                        android:text="共有0种，已选0种"
                        android:textColor="@color/cart_tv_sp"
                        android:textSize="12sp"
                        android:visibility="gone" />
                </RelativeLayout>

                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:paddingRight="6dp">

                    <TextView
                        android:id="@+id/cart_ll2_tv2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:text="合计:"
                        android:textColor="@color/color_292933"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/cart_ll2_tv4"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_below="@+id/cart_ll2_tv2"
                        android:layout_alignBaseline="@id/cart_discount_detail"
                        android:layout_marginTop="0dp"
                        android:layout_marginRight="@dimen/dimen_dp_6"
                        android:layout_toStartOf="@+id/cart_ll2_tv5"
                        android:text="促销减:"
                        android:textColor="@color/color_9494A6"
                        android:textSize="10sp" />

                    <TextView
                        android:id="@+id/cart_ll2_tv5"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_below="@+id/cart_ll2_tv2"
                        android:layout_alignBaseline="@id/cart_discount_detail"
                        android:layout_marginTop="0dp"
                        android:layout_toStartOf="@id/cart_discount_detail"
                        android:text="用券减:"
                        android:textColor="@color/color_9494A6"
                        android:textSize="10sp" />

                    <TextView
                        android:id="@+id/cart_discount_detail"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_below="@+id/cart_ll2_tv2"
                        android:layout_alignParentRight="true"
                        android:layout_marginLeft="@dimen/dimen_dp_3"
                        android:layout_marginTop="0dp"
                        android:background="@drawable/bg_cart_discount"
                        android:drawableRight="@drawable/icon_arrow_up"
                        android:paddingLeft="@dimen/dimen_dp_5"
                        android:paddingTop="@dimen/dimen_dp_1"
                        android:paddingRight="@dimen/dimen_dp_5"
                        android:paddingBottom="@dimen/dimen_dp_1"
                        android:text="明细"
                        android:textColor="@color/color_9494A6"
                        android:textSize="10sp" />

                </RelativeLayout>
            </RelativeLayout>

            <Button
                android:id="@+id/cart_bt"
                android:layout_width="100dp"
                android:layout_height="match_parent"
                android:background="@drawable/bg_cart_check_out_disable"
                android:enabled="false"
                android:textColor="@color/white"
                android:textSize="17sp" />

        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.ybmmarket20.view.MyFastScrollView
        android:id="@+id/iv_fastscroll"
        android:layout_width="54dp"
        android:layout_height="54dp"
        android:layout_gravity="bottom|right"
        android:layout_marginRight="@dimen/fast_scroll_view_02"
        android:layout_marginBottom="@dimen/fast_scroll_view_01" />

    <ImageView
        android:id="@+id/iv_ad_suspension"
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:layout_gravity="bottom|right"
        android:layout_marginBottom="110dp"
        android:src="@drawable/icon_ad_suspension"
        android:visibility="invisible" />
</FrameLayout>