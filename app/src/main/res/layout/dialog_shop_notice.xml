<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/shape_back_corner_top_white_6dp"
    android:maxHeight="@dimen/dimen_dp_330"
    android:paddingStart="@dimen/dimen_dp_15"
    android:paddingEnd="@dimen/dimen_dp_15" >

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dimen_dp_25"
        android:text="商家公告"
        android:textColor="#191919"
        android:textSize="@dimen/dimen_dp_17"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/iv_close"
        android:layout_width="@dimen/dimen_dp_18"
        android:layout_height="@dimen/dimen_dp_18"
        android:src="@drawable/icon_full_video_close"
        android:text="x"
        app:layout_constraintBottom_toBottomOf="@+id/tv_title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_title" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="@dimen/dimen_dp_35"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_title"
        app:layout_constraintVertical_weight="1">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/dimen_dp_25"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_tag_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="快递类型:"
                    android:textColor="@color/color_292933"
                    android:textSize="@dimen/dimen_dp_14" />

                <com.ybmmarket20.view.ShopNameWithTagView
                    android:id="@+id/snwtv_express_tag"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dimen_dp_5" />

                <androidx.constraintlayout.widget.Group
                    android:id="@+id/group_express_tags"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:constraint_referenced_ids="tv_tag_title, snwtv_express_tag" />


            </LinearLayout>

            <TextView
                android:id="@+id/tv_order_express_remark"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="#666883"
                android:textSize="@dimen/dimen_dp_14"
                android:layout_marginStart="@dimen/dimen_dp_50"
                tools:text="快递类型备注" />

            <TextView
                android:id="@+id/tv_order_send_local"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dimen_dp_10"
                android:textColor="#666883"
                android:textSize="@dimen/dimen_dp_14"
                tools:text="发货省市：工作日早八点半晚5点半非工作日不处理" />

            <TextView
                android:id="@+id/tv_order_handle_time"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dimen_dp_10"
                android:textColor="#666883"
                android:textSize="@dimen/dimen_dp_14"
                tools:text="订单处理时间：工作日早八点半晚5点半非工作日不处理" />

            <TextView
                android:id="@+id/tv_send_product_time"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dimen_dp_10"
                android:textColor="#666883"
                android:textSize="@dimen/dimen_dp_14"
                tools:text="发货时间：每天15点前的订单当天发，15点后的订单第二天发（周六日节假日正常发货）" />

            <TextView
                android:id="@+id/tv_content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dimen_dp_4"
                android:textColor="#666883"
                android:textSize="@dimen/dimen_dp_14"
                android:paddingBottom="@dimen/dimen_dp_20"
                tools:text="起配包邮：200元起发、600元包邮起（偏远地区除外）发货方式：EMS发货（疫情区域邮件投递有所延迟）发票：本店默认提供电子发票，如需纸质发..."/>
        </LinearLayout>
    </ScrollView>
</androidx.constraintlayout.widget.ConstraintLayout>