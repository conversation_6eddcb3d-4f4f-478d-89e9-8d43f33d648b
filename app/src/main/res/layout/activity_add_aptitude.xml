<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">

    <include layout="@layout/common_header_items" />

    <View
        style="@style/normal_horizontal_line"
        android:background="@color/color_F5F5F5" />

    <LinearLayout
        android:id="@+id/ll_top_tips"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#FFF7EF">

        <TextView
            android:id="@+id/tvTopTips"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:padding="@dimen/dimen_dp_10"
            android:layout_weight="1"
            android:text="@string/str_aptitude_add_top_tips"
            android:textColor="#99664D"
            android:textSize="14sp" />

        <ImageView
            android:id="@+id/iv_top_tips_delete"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginEnd="10dp"
            android:padding="10dp"
            android:src="@drawable/icon_add_aptitude_basic_info_top_close" />

    </LinearLayout>

    <com.ybmmarket20.view.MyScrollView
        android:id="@+id/msv"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="@color/activity_bg"
        android:fillViewport="true"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/white"
            android:orientation="vertical">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_document_status"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:paddingBottom="12dp"
                android:visibility="gone">

                <TextView
                    android:id="@+id/tv_status_licence_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:layout_marginTop="12dp"
                    android:includeFontPadding="false"
                    android:text="审批状态"
                    android:textColor="@color/color_text_little_base_color"
                    android:textSize="14dp"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tvStatus"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:layout_marginTop="12dp"
                    android:layout_marginRight="10dp"
                    android:includeFontPadding="false"
                    android:textColor="@color/color_text_status_red"
                    android:textSize="14dp"
                    app:layout_constraintLeft_toRightOf="@+id/tv_status_licence_title"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="驳回" />

                <TextView
                    android:id="@+id/tv_remark_licence_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:layout_marginTop="6dp"
                    android:includeFontPadding="false"
                    android:text="@string/str_aptitude_log_remark"
                    android:textColor="@color/color_text_little_base_color"
                    android:textSize="14dp"
                    android:visibility="gone"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tvStatus" />

                <TextView
                    android:id="@+id/tvRemark"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:layout_marginTop="6dp"
                    android:layout_marginRight="10dp"
                    android:includeFontPadding="false"
                    android:textColor="@color/color_text_base_color"
                    android:textSize="14dp"
                    android:visibility="gone"
                    app:layout_constraintLeft_toRightOf="@+id/tv_remark_licence_title"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tvStatus"
                    tools:text="备注信息" />

                <TextView
                    android:id="@+id/textView8"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:layout_marginTop="6dp"
                    android:text="单据编号:"
                    android:textColor="@color/color_text_little_base_color"
                    android:textSize="14dp"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tvRemark" />

                <TextView
                    android:id="@+id/tvId"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:layout_marginTop="6dp"
                    android:layout_marginRight="10dp"
                    android:textColor="@color/color_text_base_color"
                    android:textSize="14dp"
                    app:layout_constraintLeft_toRightOf="@+id/textView8"
                    app:layout_constraintTop_toBottomOf="@+id/tvRemark"
                    tools:text="ddddddd" />

                <TextView
                    android:id="@+id/tv_up_time_hint"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:layout_marginTop="6dp"
                    android:text="提交时间:"
                    android:textColor="@color/color_text_little_base_color"
                    android:textSize="14dp"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tvId" />

                <TextView
                    android:id="@+id/tv_up_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:layout_marginTop="6dp"
                    android:layout_marginRight="10dp"
                    android:textColor="@color/color_text_base_color"
                    android:textSize="14dp"
                    app:layout_constraintLeft_toRightOf="@+id/tv_up_time_hint"
                    app:layout_constraintTop_toBottomOf="@+id/tvId"
                    tools:text="ddddddd" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:orientation="vertical">
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    android:orientation="vertical" >

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="94dp"
                        android:layout_marginTop="15dp"
                        android:layout_marginEnd="94dp"
                        android:gravity="center">

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:src="@drawable/shape_aptitude_add_basic_info_circle" />

                        <View
                            android:layout_width="0dp"
                            android:layout_height="2dp"
                            android:layout_weight="1"
                            android:background="@color/color_theme_base_color" />

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:src="@drawable/shape_aptitude_add_basic_info_circle" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="57dp"
                        android:layout_marginTop="12dp"
                        android:layout_marginEnd="57dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="start"
                            android:text="@string/str_aptitude_add_basic_info_input_tips"
                            android:textColor="@color/text_292933"
                            android:textSize="14sp" />

                        <View
                            android:layout_width="0dp"
                            android:layout_height="0dp"
                            android:layout_weight="1" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="end"
                            android:text="@string/str_aptitude_add_basic_info_upload_pic_tips"
                            android:textColor="@color/text_292933"
                            android:textSize="14sp" />
                    </LinearLayout>
                </LinearLayout>

                <com.ybmmarket20.view.AptitudeProgressView
                    android:id="@+id/apv"
                    android:layout_marginTop="@dimen/dimen_dp_15"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    tools:orientation="horizontal"
                    tools:itemCount="3"
                    app:layoutManager="com.ybm.app.view.WrapGridLayoutManager"
                    tools:listitem="@layout/item_aptitude_pregress"
                    tools:layout_height="@dimen/dimen_dp_50" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:layout_marginTop="25dp"
                    android:text="@string/str_aptitude_add_aptitude_info_tips"
                    android:textColor="@color/text_292933"
                    android:textSize="15sp"
                    android:textStyle="bold" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_necessary"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="0dp"
                android:background="@color/white"
                android:orientation="vertical"
                android:visibility="visible"
                tools:visibility="visible" />


        </LinearLayout>
    </com.ybmmarket20.view.MyScrollView>

    <com.ybmmarket20.common.widget.RoundTextView
        android:id="@+id/edit"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:layout_gravity="center_vertical"
        android:gravity="center"
        android:text="提交资质"
        android:textColor="@android:color/white"
        android:textSize="16dp"
        android:visibility="visible"
        app:rv_backgroundColor="@color/color_theme_base_color"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tvConfirm"
        android:layout_width="335dp"
        android:layout_height="44dp"
        android:layout_marginBottom="30dp"
        android:background="@drawable/selector_link_shop_confirm"
        android:gravity="center"
        android:text="提交资质"
        android:textColor="#FFFFFF"
        android:textSize="16dp"
        android:layout_gravity="center"
        android:enabled="true"
        android:visibility="gone"
        tools:visibility="visible" />
</LinearLayout>
