<?xml version="1.0" encoding="utf-8"?>
<com.ybmmarket20.view.SwipeMenuLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:card_view="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cart_swipe_layout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:orientation="horizontal"
    android:paddingBottom="@dimen/dimen_dp_10"
    card_view:ios="false"
    card_view:leftSwipe="true"
    card_view:swipeEnable="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_item"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:paddingBottom="@dimen/dimen_dp_10">

    <ImageView
        android:id="@+id/iv_good"
        android:layout_width="84dp"
        android:layout_height="84dp"
        android:layout_marginLeft="@dimen/dimen_dp_9"
        android:layout_marginTop="@dimen/dimen_dp_12"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:background="@color/translucent_black_97" />

    <View
        android:layout_width="@dimen/dimen_dp_84"
        android:layout_height="@dimen/dimen_dp_84"
        app:layout_constraintTop_toTopOf="@+id/iv_good"
        app:layout_constraintStart_toStartOf="@+id/iv_good"
        android:background="@color/translucent_black_90" />

    <ImageView
        android:id="@+id/iv_marker"
        android:layout_width="@dimen/dimen_dp_84"
        android:layout_height="@dimen/dimen_dp_84"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/iv_good"
        app:layout_constraintLeft_toLeftOf="@id/iv_good"
        app:layout_constraintRight_toRightOf="@id/iv_good"
        app:layout_constraintTop_toTopOf="@id/iv_good"
        tools:background="@drawable/load_more"
        tools:visibility="visible" />

        <TextView
            android:id="@+id/tv_goods_invalid_status"
            android:layout_width="0dp"
            android:layout_height="@dimen/dimen_dp_21"
            android:background="@color/translucent_black_60"
            android:gravity="center"
            android:textColor="@color/white"
            android:textSize="@dimen/dimen_dp_12"
            app:layout_constraintBottom_toBottomOf="@id/iv_good"
            app:layout_constraintLeft_toLeftOf="@id/iv_good"
            app:layout_constraintRight_toRightOf="@id/iv_good"
            tools:text="失效" />


        <TextView
            android:id="@+id/tv_goods_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dimen_dp_10"
            android:layout_marginRight="@dimen/dimen_dp_17"
            android:ellipsize="end"
            android:maxLines="2"
            android:textColor="@color/color_9494A6"
            android:textSize="@dimen/dimen_dp_15"
            app:layout_constraintLeft_toRightOf="@id/iv_good"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@id/iv_good"
            tools:text="感康 复方氨酚烷胺片感康复方氨酚烷胺片 感康 复方氨酚烷胺片感康复方氨酚烷胺片/ 50g*1瓶" />


        <TextView
            android:id="@+id/iv_good_num_in_group"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dimen_dp_10"
            android:layout_marginRight="@dimen/dimen_dp_14"
            android:textColor="@color/color_676773"
            android:textSize="@dimen/dimen_dp_13"
            app:layout_constraintLeft_toLeftOf="@id/tv_goods_name"
            app:layout_constraintTop_toBottomOf="@id/tv_goods_name"
            tools:text="x1" />

        <TextView
            android:id="@+id/iv_goods_invalid_status_content"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dimen_dp_5"
            android:layout_marginBottom="@dimen/dimen_dp_6"
            android:textColor="@color/color_292933"
            android:textSize="@dimen/dimen_dp_12"
            app:layout_constraintLeft_toLeftOf="@id/tv_goods_name"
            app:layout_constraintTop_toBottomOf="@id/iv_good_num_in_group"
            tools:text="商品已下架" />

        <com.ybmmarket20.common.widget.RoundTextView
            android:id="@+id/tv_find_similarity"
            android:layout_width="53dp"
            android:layout_height="22dp"
            android:layout_marginRight="@dimen/dimen_dp_10"
            android:gravity="center"
            android:text="找相似"
            android:textColor="@color/color_292933"
            android:textSize="@dimen/dimen_dp_12"
            android:textStyle="bold"
            app:layout_constraintBaseline_toBaselineOf="@id/iv_goods_invalid_status_content"
            app:layout_constraintRight_toRightOf="parent"
            app:rv_backgroundColor="@color/white"
            app:rv_cornerRadius="@dimen/dimen_dp_100"
            app:rv_strokeColor="@color/color_292933"
            app:rv_strokeWidth="0.75dp"
            tools:text="找相似" />


    </androidx.constraintlayout.widget.ConstraintLayout>


    <Button
        android:id="@+id/btnCollect"
        android:layout_width="50dp"
        android:layout_height="match_parent"
        android:background="@drawable/bg_cart_section_content_05_gray"
        android:clickable="true"
        android:text="收藏"
        android:textColor="@android:color/white" />

    <Button
        android:id="@+id/btn_find_same_goods"
        android:layout_width="50dp"
        android:layout_height="match_parent"
        android:textSize="@dimen/dimen_dp_13"
        android:background="#FD7121"
        android:clickable="true"
        android:text="找相似"
        android:textColor="@android:color/white" />

    <Button
        android:id="@+id/btnDelete"
        android:layout_width="50dp"
        android:layout_height="match_parent"
        android:background="@drawable/bg_cart_section_content_06"
        android:text="删除"
        android:textColor="@android:color/white" />


</com.ybmmarket20.view.SwipeMenuLayout>