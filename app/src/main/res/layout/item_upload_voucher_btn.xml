<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">
    
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_btn"
        android:layout_width="0dp"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginHorizontal="3dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:background="@drawable/shape_uoload_voucher_add"
        app:layout_constraintDimensionRatio="1"
        android:layout_height="0dp">
        
        <ImageView
            android:id="@+id/iv_add"
            android:layout_width="34dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:src="@drawable/icon_upload_voucher_add"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toTopOf="@id/tv_content"
            android:layout_marginBottom="20dp"
            app:layout_constraintVertical_chainStyle="packed"
            android:layout_height="34dp"/>

        <TextView
            android:id="@+id/tv_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/iv_add"
            app:layout_constraintBottom_toBottomOf="parent"
            tools:text="上传凭证0/3"
            android:textSize="13dp"
            android:textColor="@color/color_00b377"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
        
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>