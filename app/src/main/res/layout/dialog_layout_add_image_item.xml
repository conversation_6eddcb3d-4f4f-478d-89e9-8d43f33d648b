<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <com.ybmmarket20.common.widget.RoundLinearLayout
        android:layout_width="match_parent"
        android:layout_height="108dp"
        android:orientation="vertical"
        app:rv_backgroundColor="@color/white"
        app:rv_cornerRadius="10dp">

        <com.ybmmarket20.common.widget.RoundTextView
            android:id="@+id/tv_taking_pictures"
            android:layout_width="match_parent"
            android:layout_height="54dp"
            android:background="@color/white"
            android:gravity="center"
            android:text="拍照"
            android:textColor="#292626"
            android:textSize="18sp" />

        <View
            android:layout_width="match_parent"
            android:layout_height="0.33dp"
            android:background="#F4F4F4" />

        <com.ybmmarket20.common.widget.RoundTextView
            android:id="@+id/tv_photo_gallery"
            android:layout_width="match_parent"
            android:layout_height="54dp"
            android:background="@color/white"
            android:gravity="center"
            android:text="照片图库"
            android:textColor="#292626"
            android:textSize="18sp" />

    </com.ybmmarket20.common.widget.RoundLinearLayout>

    <com.ybmmarket20.common.widget.RoundTextView
        android:id="@+id/tv_cancel"
        style="@style/dialog_bottom_sheet_style"
        android:layout_marginTop="10dp"
        android:text="取消"
        android:textColor="#292626"
        android:textSize="18sp"
        app:rv_backgroundColor="@color/white"
        app:rv_cornerRadius="10dp" />

</LinearLayout>