<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#ffffffff">

    <include
        android:id="@+id/ll_title"
        layout="@layout/common_header_items"
        android:layout_width="match_parent"
        android:layout_height="@dimen/header_height"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintLeft_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@color/base_bg"
        android:orientation="vertical"
        app:layout_constraintBottom_toTopOf="@id/tv_confirm"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/ll_title">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_create_shop"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="#ffffffff"
            android:visibility="gone"
            tools:visibility="visible">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="15dp"
                android:layout_marginTop="14.5dp"
                android:text="新建店铺"
                android:textColor="#9595A6"
                android:textSize="12dp"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_edit_shop"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="14.5dp"
                android:layout_marginRight="15dp"
                android:text="编辑"
                android:textColor="#00B377"
                android:textSize="12dp"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <include
                android:id="@+id/ils_new_create"
                layout="@layout/item_link_shop"
                android:layout_width="match_parent"
                android:layout_height="69dp"
                app:layout_constraintBottom_toTopOf="@id/divider"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_edit_shop" />

            <View
                android:id="@+id/divider"
                android:layout_width="match_parent"
                android:layout_height="10dp"
                android:layout_marginTop="69dp"
                android:background="#fff7f7f8"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_edit_shop" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="52dp"
            android:background="#ffffffff"
            android:gravity="center_vertical"
            android:paddingLeft="15dp"
            android:paddingRight="15dp">

            <TextView
                android:id="@+id/tv_city"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:enabled="false"
                android:text="定位中..."
                android:textColor="@color/text_292933"
                android:textSize="14dp"
                tools:text="拉格朗日市" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="34dp"
                android:layout_marginStart="17dp"
                android:background="#f5f5f5"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="18dp"
                    android:layout_height="18dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="10dp"
                    android:background="@drawable/icon_agent_order_search" />

                <EditText
                    android:id="@+id/et_search_shop_name"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="2dp"
                    android:background="@null"
                    android:hint="店铺名称"
                    android:imeOptions="actionSearch"
                    android:maxLines="1"
                    android:singleLine="true"
                    android:textSize="13dp" />

            </LinearLayout>


        </LinearLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_shop"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="#ffffffff"
            tools:listitem="@layout/item_link_shop"
            tools:visibility="gone">

        </androidx.recyclerview.widget.RecyclerView>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_dismiss_location"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="gone"
            tools:visibility="gone">

            <TextView
                android:id="@+id/tv_tips"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingLeft="40dp"
                android:paddingRight="40dp"
                android:text="@string/str_link_shop_location_tips"
                android:textColor="#8E8E93"
                android:textSize="14dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_tosettle"
                android:layout_width="175dp"
                android:layout_height="32dp"
                android:layout_marginTop="18dp"
                android:background="#00b377"
                android:gravity="center"
                android:text="去设置"
                android:textColor="#FFFFFF"
                android:textSize="14dp"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_tips" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_dismiss_location_init"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="gone"
            tools:visibility="visible">

            <TextView
                android:id="@+id/tvStartLocate"
                android:layout_width="wrap_content"
                android:layout_height="32dp"
                android:layout_marginTop="18dp"
                android:paddingStart="@dimen/dimen_dp_20"
                android:paddingEnd="@dimen/dimen_dp_20"
                android:background="#00b377"
                android:gravity="center"
                android:text="开启定位权限获取周边店铺"
                android:textColor="#FFFFFF"
                android:textSize="14dp"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </LinearLayout>

    <TextView
        android:id="@+id/tv_confirm"
        android:layout_width="335dp"
        android:layout_height="44dp"
        android:layout_marginBottom="30dp"
        android:background="@drawable/selector_link_shop_confirm"
        android:gravity="center"
        android:text="确定"
        android:textColor="#FFFFFF"
        android:textSize="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        tools:visibility="gone" />

</androidx.constraintlayout.widget.ConstraintLayout>
