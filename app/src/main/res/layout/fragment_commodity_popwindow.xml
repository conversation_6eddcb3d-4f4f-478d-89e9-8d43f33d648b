<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:paddingRight="12dp">

    <LinearLayout
        android:id="@+id/ll_countent"
        android:layout_width="88dp"
        android:layout_height="wrap_content"
        android:background="@drawable/bg32"
        android:orientation="vertical"
        android:gravity="center">

        <TextView
            android:paddingTop="8dp"
            android:id="@+id/pop_home"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:drawableLeft="@drawable/pop_home"
            android:drawablePadding="3dp"
            android:gravity="center_vertical"
            android:paddingLeft="10dp"
            android:text="首页"
            android:textColor="@color/white"
            android:textSize="15sp" />

        <View
            android:layout_width="110dp"
            android:layout_height="0.5dp"
            android:layout_gravity="center"
            android:background="@color/gray_gray"
            android:visibility="visible" />

        <TextView
            android:id="@+id/pop_search"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:drawableLeft="@drawable/pop_search"
            android:drawablePadding="3dp"
            android:gravity="center_vertical"
            android:paddingLeft="10dp"
            android:text="搜索"
            android:textColor="@color/white"
            android:textSize="15sp" />

        <View
            android:layout_width="110dp"
            android:layout_height="0.5dp"
            android:layout_gravity="center"
            android:background="@color/gray_gray"
            android:visibility="visible" />

        <TextView
            android:id="@+id/pop_message"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:drawableLeft="@drawable/pop_message"
            android:drawablePadding="3dp"
            android:gravity="center_vertical"
            android:paddingLeft="10dp"
            android:text="消息"
            android:textColor="@color/white"
            android:textSize="15sp" />

        <View
            android:layout_width="110dp"
            android:layout_height="0.5dp"
            android:layout_gravity="center"
            android:background="@color/gray_gray"
            android:visibility="visible" />

        <TextView
            android:id="@+id/pop_more"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:drawableLeft="@drawable/pop_more"
            android:drawablePadding="3dp"
            android:gravity="center_vertical"
            android:paddingLeft="10dp"
            android:text="我的"
            android:textColor="@color/white"
            android:textSize="15sp" />

        <View
            android:id="@+id/v_share_line"
            android:layout_width="110dp"
            android:layout_height="0.5dp"
            android:layout_gravity="center"
            android:background="@color/gray_gray"
            android:visibility="visible" />

        <TextView
            android:id="@+id/pop_share"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:drawableLeft="@drawable/pop_share"
            android:drawablePadding="3dp"
            android:gravity="center_vertical"
            android:paddingLeft="10dp"
            android:text="分享"
            android:visibility="gone"
            tools:visibility="visible"
            android:textColor="@color/white"
            android:textSize="15sp" />

        <View
            android:id="@+id/v_collection_line"
            android:layout_width="110dp"
            android:layout_height="0.5dp"
            android:layout_gravity="center"
            android:background="@color/gray_gray"
            android:visibility="gone" />
        <LinearLayout
            android:id="@+id/ll_collect_pop"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dimen_dp_40"
            android:layout_alignParentRight="true"
            android:layout_centerInParent="true"
            android:orientation="horizontal">

            <CheckBox
                android:id="@+id/cb_collection_pop"
                style="@style/shoucangCheckboxTheme4"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginStart="@dimen/dimen_dp_12"
                android:clickable="false" />

            <TextView
                android:id="@+id/tv_collection_pop"
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:drawablePadding="3dp"
                android:gravity="center_vertical"
                android:paddingStart="@dimen/dimen_dp_5"
                android:text="收藏"
                android:textColor="@color/white"
                android:textSize="15sp" />

        </LinearLayout>
    </LinearLayout>

</LinearLayout>