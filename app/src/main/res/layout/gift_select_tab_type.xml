<?xml version="1.0" encoding="utf-8"?>

<androidx.constraintlayout.widget.ConstraintLayout xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="37dp">

    <TextView
        android:id="@+id/tv_tab"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:maxLines="1"
        android:paddingHorizontal="21dp"
        android:textColor="@color/black"
        android:textSize="15dp"
        tools:text="满赠活动111" />

    <TextView
        android:id="@+id/tv_tags"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingHorizontal="2dp"
        android:textColor="@color/white"
        android:textSize="9dp"
        tools:text="已选" />

</androidx.constraintlayout.widget.ConstraintLayout>
