<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools" >

    <TextView
        android:id="@+id/iv_search_spell_group_head_bg"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_43"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/iv_search_spell_group_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dimen_dp_11"
        android:text="热卖排行榜"
        android:textSize="@dimen/dimen_dp_14"
        android:textColor="#FA2C19"
        android:textStyle="bold"
        android:drawableEnd="@drawable/icon_search_start_recommend"
        android:drawablePadding="@dimen/dimen_dp_5"
        app:layout_constraintStart_toStartOf="@+id/iv_search_spell_group_head_bg"
        app:layout_constraintTop_toTopOf="@+id/iv_search_spell_group_head_bg" />

    <TextView
        android:id="@+id/tv_search_start_recommend_more"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="@dimen/dimen_dp_13"
        android:textColor="@color/color_292933"
        android:text="更多"
        android:drawableEnd="@drawable/icon_search_spell_group_arrow_right"
        android:drawablePadding="@dimen/dimen_dp_5"
        android:layout_marginEnd="@dimen/dimen_dp_10"
        app:layout_constraintBottom_toBottomOf="@+id/iv_search_spell_group_title"
        app:layout_constraintEnd_toEndOf="@+id/iv_search_spell_group_head_bg"
        app:layout_constraintTop_toTopOf="@+id/iv_search_spell_group_title" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_search_start_recommend"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        tools:itemCount="6"
        tools:listitem="@layout/item_search_start_recommend"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/iv_search_spell_group_head_bg" />
</androidx.constraintlayout.widget.ConstraintLayout>