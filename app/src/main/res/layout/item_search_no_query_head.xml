<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dimen_dp_20">

    <ImageView
        android:id="@+id/iv_search_head_left"
        android:layout_width="@dimen/dimen_dp_18"
        android:layout_height="@dimen/dimen_dp_10"
        android:src="@drawable/icon_search_head_left"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/tv_search_head_text"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_search_head_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/recommondforyou"
        android:textColor="@color/color_676773"
        android:textSize="@dimen/dimen_dp_14"
        android:layout_marginStart="@dimen/dimen_dp_5"
        android:layout_marginEnd="@dimen/dimen_dp_5"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@+id/iv_search_head_left"
        app:layout_constraintEnd_toStartOf="@+id/iv_search_head_right"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/iv_search_head_right"
        android:layout_width="@dimen/dimen_dp_18"
        android:layout_height="@dimen/dimen_dp_10"
        android:src="@drawable/icon_search_head_right"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/tv_search_head_text"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>