<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/ll_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#80000000">

    <View
        android:id="@+id/v_mask"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/transparent" />

    <FrameLayout
        android:id="@+id/fl_content"
        android:layout_width="match_parent"
        android:layout_height="350dp"
        android:layout_gravity="bottom"
        android:background="@drawable/bg_spell_group_dialog"
        android:paddingLeft="15dp"
        android:paddingRight="10dp">

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:fillViewport="true">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">


                <TextView
                    android:id="@+id/tv_main_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:text="购买主品"
                    android:textColor="#292933"
                    android:textSize="15sp"
                    android:textStyle="bold"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tv_main_desc"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="（已选购的主商品）"
                    android:textColor="#575766"
                    android:textSize="11sp"
                    app:layout_constraintBaseline_toBaselineOf="@id/tv_main_title"
                    app:layout_constraintLeft_toRightOf="@id/tv_main_title" />


                <ImageView
                    android:id="@+id/iv_main_logo"
                    android:layout_width="60dp"
                    android:layout_height="60dp"
                    android:layout_marginTop="14dp"
                    app:layout_constraintLeft_toLeftOf="@id/tv_main_title"
                    app:layout_constraintTop_toBottomOf="@id/tv_main_title" />


                <TextView
                    android:id="@+id/tv_main_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="5.5dp"
                    android:ellipsize="end"
                    android:singleLine="true"
                    android:textColor="#292933"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    app:layout_constraintLeft_toRightOf="@id/iv_main_logo"
                    app:layout_constraintTop_toTopOf="@id/iv_main_logo"
                    tools:text="念慈菴 蜜炼川贝枇杷膏/500ml/盒" />


                <TextView
                    android:id="@+id/tv_main_price"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:textColor="#292933"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    app:layout_constraintLeft_toLeftOf="@id/tv_main_name"
                    app:layout_constraintTop_toBottomOf="@id/tv_main_name"
                    tools:text="￥500.00/盒" />


                <TextView
                    android:id="@+id/tv_main_count"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="#575766"
                    android:textSize="12sp"
                    app:layout_constraintHorizontal_bias="1.0"
                    app:layout_constraintLeft_toRightOf="@id/tv_main_price"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="@id/tv_main_price"
                    tools:text="x10" />


                <TextView
                    android:id="@+id/tv_sub_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="29dp"
                    android:text="随心拼"
                    android:textColor="#292933"
                    android:textSize="15sp"
                    android:textStyle="bold"
                    app:layout_constraintLeft_toLeftOf="@id/tv_main_title"
                    app:layout_constraintTop_toBottomOf="@id/iv_main_logo" />

                <TextView
                    android:id="@+id/tv_sub_desc"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="（已选购的随心拼商品）"
                    android:textColor="#575766"
                    android:textSize="11sp"
                    app:layout_constraintBaseline_toBaselineOf="@id/tv_sub_title"
                    app:layout_constraintLeft_toRightOf="@id/tv_sub_title" />


                <androidx.constraintlayout.widget.Group
                    android:id="@+id/group_empty"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:visibility="visible"
                    app:constraint_referenced_ids="iv_empty,tv_empty" />

                <ImageView
                    android:id="@+id/iv_empty"
                    android:layout_width="110dp"
                    android:layout_height="110dp"
                    android:src="@drawable/icon_empty"
                    app:layout_constraintVertical_chainStyle="packed"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintBottom_toTopOf="@+id/tv_empty"
                    app:layout_constraintTop_toBottomOf="@id/tv_sub_title" />


                <TextView
                    android:id="@+id/tv_empty"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="7dp"
                    android:text="加购任意一盒即可一起发货，快去加购吧"
                    app:layout_constraintBottom_toBottomOf="parent"
                    android:textSize="11sp"
                    android:textColor="#9494a5"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/iv_empty"
                    app:layout_constraintVertical_bias="0.0" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_sub_list"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    android:layout_marginTop="15dp"
                    app:layout_constraintLeft_toLeftOf="@id/tv_sub_title"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tv_sub_title" />


            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.core.widget.NestedScrollView>
    </FrameLayout>
</FrameLayout>