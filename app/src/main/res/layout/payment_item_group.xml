<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:layout_marginTop="10dp"
        android:background="@color/white"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingLeft="10dp">

        <TextView
            android:id="@+id/tv_cart_proprietary"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginRight="5dp"
            android:layout_weight="1"
            android:drawablePadding="4dp"
            android:gravity="center_vertical"
            android:textColor="@color/cart_head_tv01"
            android:textSize="@dimen/sp_14"
            android:textStyle="bold"
            tools:text="小药药自营" />

        <com.ybmmarket20.common.widget.RoundTextView
            android:id="@+id/icon_arrow"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="5dp"
            android:drawableRight="@drawable/icon_payment_down_arrow"
            android:drawablePadding="5dp"
            android:text="展开"
            android:textColor="@color/color_676773"
            android:textSize="13sp"
            android:visibility="gone" />

    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:visibility="gone"
        android:background="@color/color_F5F5F5" />
    <!-- 次日达 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/rl_next_day"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="10dp"
        android:paddingBottom="6dp"
        android:background="@color/white"
        android:visibility="gone"
        tools:visibility="visible">

        <TextView
            android:id="@+id/tv_next_day_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="次日达"
            android:textSize="10sp"
            android:paddingStart="3dp"
            android:paddingEnd="3dp"
            android:paddingTop="2dp"
            android:paddingBottom="2dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_next_day"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginStart="5dp"
            android:layout_marginEnd="8dp"
            android:textColor="@color/color_292933"
            android:textSize="12sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/tv_next_day_label"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="17:00前付款，预计明天(3月18日)23:59前送达" />
    </androidx.constraintlayout.widget.ConstraintLayout>
    <LinearLayout
        android:id="@+id/ll_content_show"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!--赠品提示栏-->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_gift_tips"
            android:layout_width="match_parent"
            android:background="@color/color_fff6f6"
            android:visibility="gone"
            tools:visibility="visible"
            android:layout_height="32dp">

            <TextView
                android:id="@+id/tv_gift_tips"
                android:layout_width="wrap_content"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                android:layout_marginStart="10dp"
                android:textSize="11dp"
                android:textColor="@color/color_ff2121"
                tools:text="恭喜，本单可领取3件赠品！"
                android:textStyle="bold"
                android:layout_height="wrap_content"/>

            <TextView
                android:id="@+id/tv_select_gift"
                android:layout_width="wrap_content"
                android:paddingStart="10dp"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                android:text="选赠品"
                android:textColor="@color/color_ff2121"
                android:textSize="11dp"
                android:gravity="center"
                app:layout_constraintEnd_toStartOf="@id/iv_arrow"
                android:layout_height="0dp"/>

            <ImageView
                android:id="@+id/iv_arrow"
                android:layout_width="28dp"
                android:layout_height="32dp"
                app:layout_constraintEnd_toEndOf="parent"
                android:paddingEnd="18dp"
                android:paddingVertical="11dp"
                android:paddingStart="2dp"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                android:src="@drawable/icon_gift_select_right_arrow"/>
        </androidx.constraintlayout.widget.ConstraintLayout>

        <LinearLayout
            android:id="@+id/ll_product"
            android:layout_width="match_parent"
            android:layout_height="100dp"
            android:background="@color/white"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingLeft="10dp"
            android:paddingTop="10dp"
            android:paddingRight="12dp"
            android:paddingBottom="10dp">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center_vertical">

                <FrameLayout style="@style/payment_product">

                    <ImageView
                        android:id="@+id/iv_product_1"
                        style="@style/payment_iv_product" />
                </FrameLayout>

                <FrameLayout style="@style/payment_product">

                    <ImageView
                        android:id="@+id/iv_product_2"
                        style="@style/payment_iv_product" />

                </FrameLayout>

                <FrameLayout style="@style/payment_product">

                    <ImageView
                        android:id="@+id/iv_product_3"
                        style="@style/payment_iv_product" />

                </FrameLayout>
            </LinearLayout>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_gravity="center_vertical"
                android:gravity="center"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_product_number"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:drawableRight="@drawable/common_more"
                    android:drawablePadding="3dp"
                    android:gravity="right"
                    android:text="共0件"
                    android:textColor="@color/text_9494A6"
                    android:textSize="12sp" />

                <TextView
                    android:id="@+id/tv_gift"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="3dp"
                    android:text="含物料心愿单礼包"
                    android:textColor="#F76A24"
                    android:textSize="12sp"
                    android:visibility="gone" />

            </LinearLayout>
        </LinearLayout>


        <!--优惠券-->
        <LinearLayout
            android:id="@+id/ll_order_discount_coupon"
            style="@style/payment_item_layout"
            android:layout_marginTop="0dp"
            android:visibility="gone">

            <TextView
                android:id="@+id/tv_discount_coupon"
                style="@style/payment_item_order_text_key3"
                android:text="优惠券" />

            <TextView
                android:id="@+id/tv_discount_coupon_num"
                style="@style/payment_item_order_text_value3"
                android:drawableRight="@drawable/common_more" />
        </LinearLayout>

        <!--活动优惠-->
        <LinearLayout
            android:id="@+id/ll_preferential_activities"
            style="@style/payment_item_layout"
            android:visibility="gone"
            android:layout_marginTop="0dp">

            <TextView
                android:id="@+id/tv_preferential_activities_text"
                style="@style/payment_item_order_text_key3"
                android:text="活动优惠" />

            <TextView
                android:id="@+id/tv_preferential_activities_price"
                style="@style/payment_item_order_text_value3"
                android:textSize="13sp" />
        </LinearLayout>

        <!--总优惠-->
        <LinearLayout
            android:id="@+id/ll_the_total_discount"
            style="@style/payment_item_layout"
            android:layout_marginTop="0dp">

            <TextView
                android:id="@+id/tv_the_total_discount_text"
                style="@style/payment_item_order_text_key2"
                android:text="总优惠" />

            <TextView
                android:id="@+id/tv_the_total_discount_price"
                style="@style/payment_item_order_text_value4" />
        </LinearLayout>

        <!--运费-->
        <LinearLayout
            android:id="@+id/ll_order_freight"
            style="@style/payment_item_layout"
            android:layout_marginTop="0dp">

            <TextView
                android:id="@+id/tv_freight"
                style="@style/payment_item_order_text_key2"
                android:layout_width="wrap_content"
                android:layout_weight="0"
                android:drawableEnd="@drawable/icon_freight_tip"
                android:drawablePadding="@dimen/dimen_dp_5"
                android:text="运费" />

            <TextView
                android:id="@+id/tv_freight_num"
                style="@style/payment_item_order_text_value4"
                android:layout_weight="1"
                android:gravity="end|center_vertical"
                tools:text="123" />
        </LinearLayout>
        <!--包邮提示-->
        <LinearLayout
            android:id="@+id/ll_order_freight_add_on_item"
            style="@style/payment_item_layout"
            android:layout_height="wrap_content"
            android:layout_marginTop="0dp"
            android:visibility="gone"
            tools:visibility="visible">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="35dp"
                android:layout_gravity="center_vertical"
                android:background="@color/color_F7F7F8"
                android:gravity="center_vertical">

                <TextView
                    android:id="@+id/tv_freight_add_on_item"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="9dp"
                    android:layout_weight="1"
                    android:maxLines="1"
                    android:textColor="@color/text_676773"
                    android:textSize="12sp"
                    tools:text="总优惠金额增加, 还需凑299.99元免运费" />

                <TextView
                    android:id="@+id/cart_new_tv_title_url"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="9dp"
                    android:drawableEnd="@drawable/icon_cart_drawable_right"
                    android:gravity="center"
                    android:paddingEnd="3dp"
                    android:textColor="@color/color_00b377"
                    android:textSize="13sp"
                    tools:text="去凑单" />
            </LinearLayout>
        </LinearLayout>
        <!--合计-->
        <LinearLayout
            android:id="@+id/ll_combined"
            style="@style/payment_item_layout"
            android:layout_marginTop="0dp">

            <TextView
                android:id="@+id/tv_combined_text"
                style="@style/payment_item_order_text_key2"
                android:text="合计" />

            <TextView
                android:id="@+id/tv_combined_price"
                style="@style/payment_item_order_text_value4" />
        </LinearLayout>

        <!--随货资质需求-->
        <LinearLayout
            android:id="@+id/llPaymentLicense"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="gone"
            android:orientation="vertical"
            tools:visibility="visible">

            <LinearLayout
                style="@style/payment_item_layout"
                android:layout_height="wrap_content"
                android:layout_marginTop="0dp"
                android:focusable="true"
                android:focusableInTouchMode="true">

                <TextView
                    android:layout_width="@dimen/dimen_dp_100"
                    android:layout_height="44dp"
                    android:layout_gravity="center_vertical"
                    android:gravity="center_vertical"
                    android:text="随货资质需求"
                    android:textColor="@color/text_292933"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/tvPaymentLicense"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:background="@null"
                    android:gravity="center_vertical"
                    android:hint="点击选择企业、商品资质"
                    android:maxLines="1"
                    android:padding="0dp"
                    android:singleLine="true"
                    android:textColor="@color/color_00b377"
                    android:textSize="14sp"
                    android:layout_marginEnd="@dimen/dimen_dp_5"
                    app:rv_backgroundColor="@color/white"
                    app:rv_cornerRadius="4dp" />

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/icon_payment_arrow_right" />

            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="1px"
                android:background="@color/color_F5F5F5" />
        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1px"
            android:background="@color/color_F5F5F5" />

        <!--留言-->
        <LinearLayout
            android:id="@+id/payment_message"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                style="@style/payment_item_layout"
                android:layout_height="wrap_content"
                android:layout_marginTop="0dp"
                android:focusable="true"
                android:focusableInTouchMode="true">

                <TextView
                    android:layout_width="@dimen/dimen_dp_76"
                    android:layout_height="44dp"
                    android:layout_gravity="center_vertical"
                    android:gravity="center_vertical"
                    android:text="备注留言"
                    android:textColor="@color/text_292933"
                    android:textSize="14sp" />

                <com.ybmmarket20.common.widget.RoundEditText
                    android:id="@+id/payment_message_leave_et"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:background="@null"
                    android:gravity="center_vertical"
                    android:hint="@string/payment_tv10"
                    android:imeOptions="actionGo"
                    android:maxLines="1"
                    android:padding="0dp"
                    android:singleLine="true"
                    android:textColor="@color/color_434343"
                    android:textCursorDrawable="@drawable/color_cursor"
                    android:textSize="14sp"
                    app:rv_backgroundColor="@color/white"
                    app:rv_cornerRadius="4dp" />

            </LinearLayout>

            <TextView
                android:layout_width="match_parent"
                android:layout_height="35dp"
                android:layout_margin="@dimen/dimen_dp_10"
                android:background="#FFF7EF"
                android:gravity="center_vertical"
                android:paddingLeft="@dimen/dimen_dp_10"
                android:text="提示：抱歉，平台目前不支持指定快递。"
                android:textColor="#99664D"
                android:textSize="12dp" />

        </LinearLayout>

    </LinearLayout>

</LinearLayout>