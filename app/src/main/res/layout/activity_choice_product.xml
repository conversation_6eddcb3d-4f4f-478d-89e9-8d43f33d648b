<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/activity_bg"
    android:orientation="vertical">

    <include layout="@layout/common_header_items" />
    <include layout="@layout/layout_channel_footer" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_product"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_refund_freight"
        android:background="@color/white"
        android:layout_width="match_parent"
        android:layout_height="50dp">

        <CheckBox
            android:id="@+id/cb_refund_freight"
            style="@style/CustomCheckboxTheme"
            android:layout_width="wrap_content"
            android:layout_height="30dp"
            android:layout_marginStart="10dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:checked="true"
            android:textColor="@color/white" />

        <TextView
            android:id="@+id/tv_refund_freight"
            android:layout_width="wrap_content"
            android:text="退运费"
            app:layout_constraintTop_toTopOf="@id/cb_refund_freight"
            app:layout_constraintBottom_toBottomOf="@id/cb_refund_freight"
            app:layout_constraintStart_toEndOf="@id/cb_refund_freight"
            android:layout_marginStart="15dp"
            android:textColor="@color/black"
            android:textStyle="bold"
            android:layout_height="wrap_content"/>

        <TextView
            android:id="@+id/tv_refund_freight_price"
            android:layout_width="wrap_content"
            tools:text="20元"
            app:layout_constraintTop_toTopOf="@id/cb_refund_freight"
            app:layout_constraintBottom_toBottomOf="@id/cb_refund_freight"
            app:layout_constraintStart_toEndOf="@id/tv_refund_freight"
            android:layout_marginStart="5dp"
            android:textColor="@color/black"
            android:textStyle="bold"
            android:layout_height="wrap_content"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="#475462"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <CheckBox
            android:id="@+id/cb_all"
            style="@style/CustomCheckboxTheme"
            android:layout_width="wrap_content"
            android:layout_height="30dp"
            android:layout_marginLeft="10dp"
            android:checked="false"
            android:text="　全选"
            android:textColor="@color/white" />

        <TextView
            android:id="@+id/tv_num"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:paddingRight="16dp"
            android:gravity="center_vertical|right"
            android:text=""
            android:textColor="@color/white_50"
            android:textSize="14sp" />
        <Button
            android:id="@+id/btn_refund"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_gravity="center_vertical"
            android:background="#00B377"
            android:gravity="center"
            android:minWidth="100dp"
            android:text="确认退货"
            android:textColor="@color/white"
            android:textSize="17sp" />
    </LinearLayout>

</LinearLayout>