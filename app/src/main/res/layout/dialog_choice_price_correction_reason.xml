<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingLeft="15dp"
    android:paddingRight="15dp">

  <RadioGroup
      android:id="@+id/priceSuggestRg"
      android:layout_width="match_parent"
      android:layout_height="wrap_content">
      <RadioButton
          android:id="@+id/priceHighSideTv"
          android:layout_width="match_parent"
          android:layout_height="44dp"
          android:background="@drawable/bg_comment_round_top_white"
          android:button="@null"
          android:gravity="center"
          android:text="@string/correction_price_high_side"
          android:textColor="@color/selector_correction_reason"
          android:textSize="15sp" />

      <View
          android:layout_width="match_parent"
          android:layout_height="@dimen/divider"
          android:background="@color/divider_line_color_eeeeee" />

      <RadioButton
          android:id="@+id/priceErrorTv"
          android:layout_width="match_parent"
          android:layout_height="44dp"
          android:background="@color/white"
          android:button="@null"
          android:gravity="center"
          android:text="@string/correction_price_reson_suggest_error"
          android:textColor="@color/selector_correction_reason"
          android:textSize="15sp" />

      <View
          android:layout_width="match_parent"
          android:layout_height="@dimen/divider"
          android:background="@color/divider_line_color_eeeeee" />

      <RadioButton
          android:id="@+id/priceOthersTv"
          android:layout_width="match_parent"
          android:layout_height="44dp"
          android:background="@drawable/bg_comment_round_bottom_white"
          android:button="@null"
          android:gravity="center"
          android:text="@string/others"
          android:textColor="@color/selector_correction_reason"
          android:textSize="15sp" />
  </RadioGroup>

    <TextView
        android:id="@+id/priceCancelTv"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="15dp"
        android:background="@drawable/bg_comment_round_white"
        android:button="@null"
        android:gravity="center"
        android:text="@string/cancel"
        android:textColor="@color/correction_text"
        android:textSize="15sp" />

</LinearLayout>

