<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:baselineAligned="false"
    android:orientation="vertical">

    <com.ybmmarket20.view.MyScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/white"
            android:orientation="vertical">

            <!--产品说明书-->
            <LinearLayout
                android:id="@+id/ll_specification"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:orientation="vertical"
                android:visibility="gone">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:background="@drawable/product_text_bg"
                    android:gravity="center_vertical"
                    android:paddingLeft="15dp"
                    android:text="产品说明书"
                    android:textColor="#333"
                    android:textSize="16sp" />

                <TextView
                    android:id="@+id/ll_specification_tv"
                    android:layout_width="match_parent"
                    android:layout_height="45dp"
                    android:background="@drawable/product_text_bg_2"
                    android:gravity="center"
                    android:text="点击图片可查看大图"
                    android:textSize="15sp" />
                <!--android:id="@+id/tv_hint"-->

            </LinearLayout>

            <com.ybmmarket20.view.ImageLayout
                android:id="@+id/il_specification"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="15dp"
                android:layout_marginRight="15dp" />

            <!--关于药帮忙-->
            <LinearLayout
                android:id="@+id/ll_about"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:orientation="vertical"
                android:visibility="visible">

                <TextView
                    android:visibility="gone"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:background="@drawable/product_text_bg"
                    android:gravity="center_vertical"
                    android:paddingLeft="15dp"
                    android:text="关于药帮忙"
                    android:textColor="#333"
                    android:textSize="16sp" />

                <ImageView
                    android:id="@+id/il_about"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:src="@drawable/icon_announcement" />
            </LinearLayout>
        </LinearLayout>
    </com.ybmmarket20.view.MyScrollView>
</LinearLayout>
