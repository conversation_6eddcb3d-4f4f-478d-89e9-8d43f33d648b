<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/more_ll"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:animateLayoutChanges="true">

    <com.ybmmarket20.view.ObservableScrollView
        android:id="@+id/sc"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/activity_bg">


        <RelativeLayout
            android:id="@+id/ll_base_info"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <ImageView
                android:layout_width="match_parent"
                android:layout_height="184dp"
                android:background="@drawable/icon_header_dark_bg"
                android:scaleType="center" />

            <TextView
                android:id="@+id/textView_shop"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="11dp"
                android:layout_marginTop="64dp"
                android:layout_marginRight="100dp"
                android:ellipsize="end"
                android:lines="1"
                android:textColor="@color/white"
                android:textSize="22dp"
                android:textStyle="bold"
                tools:text="武汉市健康大药房的房" />

            <com.ybmmarket20.common.widget.RoundTextView
                android:id="@+id/tv_account_info"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@id/textView_shop"
                android:layout_marginLeft="11dp"
                android:layout_marginTop="8dp"
                android:drawableRight="@drawable/me_arrow_white"
                android:drawablePadding="3dp"
                android:gravity="center_vertical"
                android:paddingLeft="6dp"
                android:paddingTop="3dp"
                android:paddingRight="6dp"
                android:paddingBottom="3dp"
                android:text="更多账号信息"
                android:textColor="@color/white"
                android:textSize="13dp"
                app:rv_backgroundColor="#1affffff"
                app:rv_cornerRadius="2dp" />


            <com.ybmmarket20.common.widget.RoundLinearLayout
                android:id="@+id/ll2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@id/tv_account_info"
                android:layout_marginLeft="10dp"
                android:layout_marginTop="10dp"
                android:layout_marginRight="10dp"
                android:orientation="vertical"
                app:rv_backgroundColor="@color/white"
                app:rv_cornerRadius="2dp">
                <!--充分使用textview属性代替简单LinearLayout布局-->
                <RelativeLayout
                    android:id="@+id/rl_order_form"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="10dp">

                    <TextView
                        android:id="@+id/favorable_tv"
                        style="@style/more_text_black_size_14"
                        android:layout_alignParentStart="true"
                        android:layout_alignParentLeft="true"
                        android:layout_centerVertical="true"
                        android:text="我的订单"
                        android:textColor="@color/color_292933"
                        android:textSize="15dp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tv_activity_matter"
                        style="@style/more_text_black_size_14"
                        android:layout_centerVertical="true"
                        android:layout_marginLeft="2dp"
                        android:layout_toRightOf="@+id/favorable_tv"
                        android:textColor="#ff2121" />

                    <ImageView
                        android:id="@+id/favorable_iv"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:src="@drawable/common_more" />

                    <TextView
                        style="@style/more_text_gray_size_12"
                        android:layout_centerVertical="true"
                        android:layout_toLeftOf="@id/favorable_iv"
                        android:text="全部订单"
                        android:textColor="#9494A6" />
                </RelativeLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.3dp"
                    android:background="@drawable/layout_divider_vertical" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:paddingTop="20dp"
                    android:paddingBottom="20dp">

                    <LinearLayout
                        android:id="@+id/detail_wait_payment_ll"
                        style="@style/more_text_layout_style">

                        <RelativeLayout style="@style/more_image_msg_style">

                            <ImageView
                                style="@style/more_text_layout_style2"
                                android:src="@drawable/icon_wait_pay" />

                            <TextView
                                android:id="@+id/tv_smg_wait_pay"
                                style="@style/more_msg_tip_style"
                                android:visibility="gone"
                                tools:text="12" />

                        </RelativeLayout>

                        <TextView
                            style="@style/more_text_gray_size_12"
                            android:text="待支付"
                            android:textColor="@color/color_292933" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/detail_wait_ll"
                        style="@style/more_text_layout_style">

                        <RelativeLayout style="@style/more_image_msg_style">

                            <ImageView
                                android:id="@+id/activity_common_shoucang_iv"
                                style="@style/more_text_layout_style2"
                                android:src="@drawable/icon_wait_deliver" />

                            <TextView
                                android:id="@+id/tv_smg_wait_deliver"
                                style="@style/more_msg_tip_style"
                                android:visibility="gone"
                                tools:text="1" />

                        </RelativeLayout>

                        <TextView
                            style="@style/more_text_gray_size_12"
                            android:text="待配送"
                            android:textColor="@color/color_292933" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/detail_process_ll"
                        style="@style/more_text_layout_style">

                        <RelativeLayout style="@style/more_image_msg_style">

                            <ImageView
                                style="@style/more_text_layout_style2"
                                android:src="@drawable/icon_wait_receive" />

                            <TextView
                                android:id="@+id/tv_smg_wait_receive"
                                style="@style/more_msg_tip_style"
                                android:visibility="gone"
                                tools:text="1" />

                        </RelativeLayout>

                        <TextView
                            style="@style/more_text_gray_size_12"
                            android:text="配送中"
                            android:textColor="@color/color_292933" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/detail_finish_ll"
                        style="@style/more_text_layout_style">

                        <RelativeLayout style="@style/more_image_msg_style">

                            <ImageView
                                style="@style/more_text_layout_style2"
                                android:src="@drawable/icon_wait_balance" />

                            <TextView
                                android:id="@+id/tv_smg_wait_balance"
                                style="@style/more_msg_tip_style"
                                android:visibility="gone"
                                tools:text="1" />

                        </RelativeLayout>

                        <TextView
                            style="@style/more_text_gray_size_12"
                            android:text="待评价"
                            android:textColor="@color/color_292933" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/detail_refund_ll"
                        style="@style/more_text_layout_style">

                        <RelativeLayout style="@style/more_image_msg_style">

                            <ImageView
                                style="@style/more_text_layout_style2"
                                android:src="@drawable/icon_wait_service" />

                            <TextView
                                android:id="@+id/tv_smg_wait_service"
                                style="@style/more_msg_tip_style"
                                android:visibility="gone"
                                tools:text="1" />

                        </RelativeLayout>

                        <TextView
                            android:id="@+id/textView"
                            style="@style/more_text_gray_size_12"
                            android:text="退款/售后"
                            android:textColor="@color/color_292933" />
                    </LinearLayout>
                </LinearLayout>
            </com.ybmmarket20.common.widget.RoundLinearLayout>

            <com.ybmmarket20.common.widget.RoundLinearLayout
                android:id="@+id/ll3"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@id/ll2"
                android:layout_marginLeft="10dp"
                android:layout_marginTop="10dp"
                android:layout_marginRight="10dp"
                android:orientation="vertical"
                android:paddingBottom="5dp"
                app:rv_backgroundColor="@color/white"
                app:rv_cornerRadius="2dp">

                <RelativeLayout
                    android:id="@+id/rl_more"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="10dp">

                    <TextView
                        style="@style/more_text_black_size_14"
                        android:layout_centerVertical="true"
                        android:text="常用工具"
                        android:textColor="@color/color_292933"
                        android:textSize="15dp"
                        android:textStyle="bold" />

                    <TextView
                        style="@style/more_text_gray_size_12"
                        android:layout_centerVertical="true"
                        android:layout_toLeftOf="@+id/iv_right_arrow"
                        android:text="查看更多"
                        android:textColor="#9494A6"
                        android:visibility="gone" />

                    <ImageView
                        android:id="@+id/iv_right_arrow"
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:src="@drawable/icon_right_gray"
                        android:visibility="gone" />

                </RelativeLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.3dp"
                    android:background="@drawable/layout_divider_vertical" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_tools"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:overScrollMode="never" />


            </com.ybmmarket20.common.widget.RoundLinearLayout>

            <com.ybmmarket20.common.widget.RoundLinearLayout
                android:id="@+id/ll_04"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@id/ll3"
                android:layout_marginLeft="10dp"
                android:layout_marginTop="10dp"
                android:layout_marginRight="10dp"
                android:orientation="vertical"
                android:paddingBottom="5dp"
                app:rv_backgroundColor="@color/white"
                app:rv_cornerRadius="2dp">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="10dp">

                    <TextView
                        style="@style/more_text_black_size_14"
                        android:layout_centerVertical="true"
                        android:text="帮助中心"
                        android:textColor="@color/color_292933"
                        android:textSize="15dp"
                        android:textStyle="bold" />

                    <TextView
                        style="@style/more_text_gray_size_12"
                        android:layout_centerVertical="true"
                        android:text="查看更多"
                        android:textColor="#9494A6"
                        android:visibility="gone" />

                </RelativeLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.3dp"
                    android:background="@drawable/layout_divider_vertical" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_helps"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:overScrollMode="never" />


            </com.ybmmarket20.common.widget.RoundLinearLayout>

            <com.ybmmarket20.common.widget.RoundRelativeLayout
                android:id="@+id/ll_kefu"
                android:layout_width="match_parent"
                android:layout_height="@dimen/more_tv_01"
                android:layout_below="@id/ll_04"
                android:layout_gravity="center_vertical"
                android:layout_marginLeft="10dp"
                android:layout_marginTop="10dp"
                android:layout_marginRight="10dp"
                android:layout_marginBottom="30dp"
                android:gravity="center_horizontal"
                android:padding="10dp"
                app:rv_backgroundColor="@color/white"
                app:rv_cornerRadius="2dp">

                <ImageView
                    android:id="@+id/ll_kefu_iv"
                    android:layout_width="28dp"
                    android:layout_height="28dp"
                    android:src="@drawable/icon_phone" />

                <TextView
                    android:id="@+id/ll_kefu_tv1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="8dp"
                    android:layout_toRightOf="@+id/ll_kefu_iv"
                    android:text="@string/kefuPhone"
                    android:textColor="@color/color_292933"
                    android:textSize="14dp" />
            </com.ybmmarket20.common.widget.RoundRelativeLayout>
        </RelativeLayout>


    </com.ybmmarket20.view.ObservableScrollView>

    <RelativeLayout
        android:id="@+id/ll_title"
        android:layout_width="match_parent"
        android:layout_height="@dimen/header_height"
        android:gravity="bottom"
        tools:alpha="1"
        tools:visibility="visible">


        <TextView
            android:id="@+id/title_tv"
            android:layout_width="wrap_content"
            android:layout_height="50dp"
            android:layout_centerHorizontal="true"
            android:alpha="0"
            android:gravity="center_vertical"
            android:text="我的"
            android:textColor="@color/back_white"
            android:textSize="18dp" />

        <RelativeLayout
            android:id="@+id/rl_setting"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:layout_alignParentEnd="true"
            android:gravity="center_vertical">

            <ImageView
                android:id="@+id/title_right"
                android:layout_width="22dp"
                android:layout_height="22dp"
                android:layout_alignParentEnd="true"
                android:layout_marginEnd="10dp"
                android:background="@drawable/main_sz"
                android:visibility="visible" />
        </RelativeLayout>


    </RelativeLayout>


</RelativeLayout>