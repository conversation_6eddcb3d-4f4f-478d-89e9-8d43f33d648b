<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="@dimen/dimen_dp_5"
    android:layout_marginBottom="10dp"
    android:minWidth="50dp">

    <CheckBox
        android:id="@+id/tvDynamicLabel"
        android:layout_width="match_parent"
        android:layout_height="@dimen/pabr_dimen29dp"
        android:background="@drawable/bg_selecter_seach_spacfication"
        android:button="@null"
        android:clickable="false"
        android:gravity="center"
        android:maxLines="1"
        android:textColor="@color/selector_text_color_292933"
        android:textSize="12sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:checked="true"
        tools:text="15mg*10" />
</androidx.constraintlayout.widget.ConstraintLayout>