<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">

    <!--        <include layout="@layout/common_header_items" />-->
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="@dimen/header_height_padding_top">

        <ImageView
            android:id="@+id/iv_back"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="7dp"
            android:padding="10dp"
            android:src="@drawable/ic_back" />

        <com.google.android.material.tabs.TabLayout
            android:layout_width="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_height="44dp"
            android:id="@+id/tab_layout"
            app:tabBackground="@android:color/transparent"
            app:tabRippleColor="@android:color/transparent"
            />
    </RelativeLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/color_F5F5F5" />

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/viewPager"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />


</LinearLayout>
