<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F7F7F7">

    <include
        android:id="@+id/title"
        layout="@layout/common_header_items" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_0"
        app:layout_constraintBottom_toTopOf="@+id/clConfirmLayout"
        app:layout_constraintTop_toBottomOf="@+id/title" />

    <LinearLayout
        android:id="@+id/clConfirmLayout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_64"
        android:background="@color/white"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent">

        <TextView
            android:id="@+id/tvPlatformin"
            android:layout_width="@dimen/dimen_dp_0"
            android:layout_height="@dimen/dimen_dp_44"
            android:layout_marginStart="@dimen/dimen_dp_10"
            android:layout_marginEnd="@dimen/dimen_dp_10"
            android:layout_weight="1"
            android:background="@drawable/selector_confirm_bottom"
            android:gravity="center"
            android:text="平台介入"
            android:visibility="gone"
            android:textColor="@color/white"
            android:textSize="@dimen/dimen_dp_16" />

        <TextView
            android:id="@+id/rvConfirm"
            android:layout_width="@dimen/dimen_dp_0"
            android:layout_height="@dimen/dimen_dp_44"
            android:layout_marginStart="@dimen/dimen_dp_10"
            android:layout_marginEnd="@dimen/dimen_dp_10"
            android:visibility="gone"
            android:layout_weight="1"
            android:background="@drawable/selector_confirm_bottom"
            android:gravity="center"
            android:text="再次发起售后"
            android:textColor="@color/white"
            android:textSize="@dimen/dimen_dp_16" />
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>