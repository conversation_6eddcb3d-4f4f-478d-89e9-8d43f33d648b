<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/ll_item"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/color_f7f7f8"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:paddingTop="@dimen/dimen_dp_10">


    <CheckBox
        android:id="@+id/cb_item"
        android:layout_width="match_parent"
        android:layout_height="@dimen/pabr_dimen29dp"
        android:gravity="center"
        android:background="@drawable/bg_selecter_seach_origin"
        android:button="@null"
        android:clickable="false"
        android:maxLines="1"
        android:textSize="@dimen/pax_core_sp_12"
        android:layout_marginHorizontal="@dimen/dimen_dp_5"
        android:textColor="@color/selector_text_color_292933"
        tools:checked="true"
        tools:text="15mg*10" />

</LinearLayout>