<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_spec_filter"
        android:layout_width="match_parent"
        android:layout_height="300dp"
        android:overScrollMode="never"
        android:background="@color/white"
        app:layout_constraintTop_toTopOf="parent" />

    <Button
        android:id="@+id/btn_spec_filter_reset"
        style="?android:attr/borderlessButtonStyle"
        android:layout_width="@dimen/dimen_dp_0"
        android:layout_height="@dimen/dimen_dp_48"
        app:layout_constraintWidth_percent="0.5"
        android:background="@drawable/bg_filtrate_classify_btn2_reset"
        android:text="重置"
        android:textColor="@color/text_292933"
        android:textSize="@dimen/dimen_dp_16"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintLeft_toRightOf="@+id/btn_spec_filter_confirm"
        app:layout_constraintTop_toTopOf="@+id/btn_spec_filter_confirm" />

    <Button
        android:id="@+id/btn_spec_filter_confirm"
        style="?android:attr/borderlessButtonStyle"
        android:layout_width="@dimen/dimen_dp_0"
        android:layout_height="@dimen/dimen_dp_48"
        android:background="@color/base_colors_new"
        android:elevation="0dp"
        android:text="确定"
        app:layout_constraintWidth_percent="0.5"
        android:textColor="@color/white"
        android:textSize="@dimen/dimen_dp_16"
        app:layout_constraintRight_toLeftOf="@+id/btn_spec_filter_reset"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/rv_spec_filter" />

</androidx.constraintlayout.widget.ConstraintLayout>