<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <include layout="@layout/common_header_items" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:paddingLeft="20dp"
        android:paddingRight="20dp">
        <!--输入店铺名称-->
        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/registerShopInputLayout"
            android:layout_width="0dp"
            android:layout_height="43dp"
            android:layout_marginTop="15dp"
            android:textColorHint="@color/loginTextAppearance"
            app:hintTextAppearance="@style/inputLayoutHintAppearance"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.appcompat.widget.AppCompatEditText
                android:id="@+id/register_shop"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@null"
                android:gravity="center_vertical"
                android:hint="@string/please_enter_shop_name"
                android:singleLine="true"
                android:textColor="#292933"
                android:textSize="16sp" />
        </com.google.android.material.textfield.TextInputLayout>

        <ImageView
            android:id="@+id/iv_shop_tip"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:layout_marginTop="7dp"
            android:visibility="invisible"
            android:src="@drawable/icon_register_shop_tip"
            app:layout_constraintBottom_toBottomOf="@+id/registerShopInputLayout"
            app:layout_constraintRight_toRightOf="@+id/registerShopInputLayout"
            app:layout_constraintTop_toTopOf="@id/registerShopInputLayout" />
        <!--输入收货地址省市区-->
        <View
            android:id="@+id/divider1"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="#F5F5F5"
            app:layout_constraintHorizontal_bias="0.1"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/registerShopInputLayout" />

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/registerAddressTitleInputLayout"
            android:layout_width="0dp"
            android:layout_height="43dp"
            android:layout_marginTop="9dp"
            android:textColorHint="@color/loginTextAppearance"
            app:hintTextAppearance="@style/inputLayoutHintAppearance"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/divider1">

            <androidx.appcompat.widget.AppCompatEditText
                android:id="@+id/register_tv_address_title"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@null"
                android:drawableRight="@drawable/icon_arrow_down"
                android:focusable="false"
                android:gravity="center_vertical"
                android:hint="@string/please_enter_relevance_shop_address"
                android:singleLine="true"
                android:textColor="#292933"
                android:textSize="16sp" />

        </com.google.android.material.textfield.TextInputLayout>

        <View
            android:id="@+id/divider2"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginTop="9dp"
            android:background="#F5F5F5"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/registerAddressTitleInputLayout" />
        <!--输入门店详细地址-->
        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/registerAddressInputLayout"
            android:layout_width="0dp"
            android:layout_height="43dp"
            android:layout_marginTop="9dp"
            android:textColorHint="@color/loginTextAppearance"
            app:hintTextAppearance="@style/inputLayoutHintAppearance"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/divider2">

            <androidx.appcompat.widget.AppCompatEditText
                android:id="@+id/register_et_address"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@null"
                android:gravity="center_vertical"
                android:hint="@string/please_enter_relevance_shop_address_detail"
                android:singleLine="true"
                android:textColor="#292933"
                android:textSize="16sp" />

        </com.google.android.material.textfield.TextInputLayout>

        <View
            android:id="@+id/divider3"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginTop="9dp"
            android:background="#F5F5F5"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/registerAddressInputLayout" />


        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/ilCompanyType"
            android:layout_width="0dp"
            android:layout_height="43dp"
            android:layout_marginTop="9dp"
            android:textColorHint="@color/loginTextAppearance"
            app:hintTextAppearance="@style/inputLayoutHintAppearance"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/divider3">

            <androidx.appcompat.widget.AppCompatEditText
                android:id="@+id/etCompanyType"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@null"
                android:drawableRight="@drawable/icon_arrow_down"
                android:focusable="false"
                android:gravity="center_vertical"
                android:hint="请选择企业类型"
                android:singleLine="true"
                android:textColor="#292933"
                android:textSize="16sp" />

        </com.google.android.material.textfield.TextInputLayout>

        <View
            android:id="@+id/divider4"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginTop="9dp"
            android:background="#F5F5F5"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/ilCompanyType" />

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/ilNumber"
            android:layout_width="0dp"
            android:layout_height="43dp"
            android:layout_marginTop="15dp"
            android:textColorHint="@color/loginTextAppearance"
            app:hintTextAppearance="@style/inputLayoutHintAppearance"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/divider4">

            <androidx.appcompat.widget.AppCompatEditText
                android:id="@+id/etNumber"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@null"
                android:gravity="center_vertical"
                android:hint="请输入营业执照"
                android:singleLine="true"
                android:textColor="#292933"
                android:textSize="16sp" />

        </com.google.android.material.textfield.TextInputLayout>

        <View
            android:id="@+id/divider5"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginTop="9dp"
            android:background="#F5F5F5"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/ilNumber" />

        <TextView
            android:id="@+id/tvImageTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="请上传营业执照电子版"
            android:layout_marginTop="@dimen/dimen_dp_18"
            android:textColor="#9595A6"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/divider5" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/clAddImageBtn"
            android:layout_width="@dimen/dimen_dp_79"
            android:layout_height="@dimen/dimen_dp_79"
            android:background="@drawable/shape_add_image_btn"
            android:layout_marginTop="@dimen/dimen_dp_10"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvImageTitle">

            <View
                android:layout_width="@dimen/dimen_dp_38"
                android:layout_height="@dimen/dimen_dp_1"
                android:background="#D8D8D8"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <View
                android:layout_width="@dimen/dimen_dp_1"
                android:layout_height="@dimen/dimen_dp_38"
                android:background="#D8D8D8"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/clImageUpdate"
            android:layout_width="@dimen/dimen_dp_79"
            android:layout_height="@dimen/dimen_dp_79"
            android:visibility="gone"
            android:layout_marginTop="@dimen/dimen_dp_10"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvImageTitle">

            <ImageView
                android:id="@+id/ivUpdate"
                android:layout_width="@dimen/dimen_dp_79"
                android:layout_height="@dimen/dimen_dp_79"
                android:scaleType="centerCrop"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/iv_del"
                android:layout_width="15dp"
                android:layout_height="15dp"
                android:contentDescription="@null"
                android:scaleType="fitXY"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                android:src="@drawable/icon_correction_delete" />
        </androidx.constraintlayout.widget.ConstraintLayout>


        <com.ybmmarket20.view.ButtonObserver
            android:id="@+id/register_btn_ensure"
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:layout_marginTop="16dp"
            android:layout_marginBottom="20dp"
            android:background="@drawable/selector_common_btn"
            android:enabled="false"
            android:gravity="center"
            android:text="@string/str_ensure"
            android:textColor="@color/white"
            android:textSize="16sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</LinearLayout>
